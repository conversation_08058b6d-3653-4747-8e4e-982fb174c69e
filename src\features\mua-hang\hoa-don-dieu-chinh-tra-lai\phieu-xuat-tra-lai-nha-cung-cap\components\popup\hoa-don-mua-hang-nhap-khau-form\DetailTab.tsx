import React from 'react';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import { FormField } from '@/components/custom/arito/form/form-field';
import { hoaDonMuaHangItemColumns } from '../../../cols-definition';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const HoaDonMuaHangNhapKhauDetailTab = ({ formMode }: Props) => {
  return (
    <div className='h-[500px] w-full'>
      <AritoInputTable
        value={[]}
        columns={hoaDonMuaHangItemColumns}
        onChange={() => {}}
        mode={formMode}
        tableActionButtons={['pin']}
      />
    </div>
  );
};

export default HoaDonMuaHangNhapKhauDetailTab;
