import { advancePaymentVoucherTaxColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface TaxesTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const TaxesTab = ({ value, onChange, formMode }: TaxesTabProps) => {
  return (
    <div className='h-[calc(100vh-400px)] w-screen'>
      <AritoInputTable value={value} columns={advancePaymentVoucherTaxColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
