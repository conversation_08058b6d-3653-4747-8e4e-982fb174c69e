'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { AxiosError } from 'axios';
import { CoA, Entity, Profile } from '@/interfaces/auth';
import api from '@/lib/api';

interface AuthContextType {
  profile: Profile | null;
  entity: Entity | null;
  coa: CoA | null;
  loading: boolean;
  error: Error | null;
  refetchProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [entity, setEntity] = useState<Entity | null>(null);
  const [coa, setCoa] = useState<CoA | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchProfile = async () => {
    try {
      const response = await api.get<Profile>('/profile/');
      setProfile(response.data);
      return response.data;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch profile'));
      throw err;
    }
  };

  const fetchEntity = async (entitySlug: string) => {
    try {
      const response = await api.get<Entity>(`/entities/${entitySlug}/`);
      setEntity(response.data);
      return response.data;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch entity'));
      throw err;
    }
  };

  const fetchCoa = async (entitySlug: string, coaSlug: string) => {
    try {
      const response = await api.get<CoA>(`/entities/${entitySlug}/coa/${coaSlug}/`);
      setCoa(response.data);
      return response.data;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch CoA'));
      throw err;
    }
  };

  const loadAuthData = async () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      setLoading(false);
      if (!window.location.href.includes('/xac-thuc')) {
        window.location.href = '/xac-thuc/';
      }

      return;
    }

    setLoading(true);
    setError(null);
    try {
      const profileData = await fetchProfile();
      if (profileData?.entity_slug) {
        const entityData = await fetchEntity(profileData.entity_slug);
        if (entityData?.default_coa_slug) {
          await fetchCoa(entityData.slug, entityData.default_coa_slug);
        }
      }
    } catch (err: unknown) {
      console.error('Error loading auth data:', err);
      // Only redirect on 401 - other errors should be handled by the api interceptor
      if (err instanceof AxiosError && err.response?.status === 401) {
        window.location.href = '/xac-thuc';
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (!token && !window.location.href.includes('/xac-thuc')) {
      setLoading(false);
      window.location.href = '/xac-thuc';
      return;
    }

    loadAuthData();
  }, []);

  const refetchProfile = async () => {
    await loadAuthData();
  };

  const value = {
    profile,
    entity,
    coa,
    loading,
    error,
    refetchProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
