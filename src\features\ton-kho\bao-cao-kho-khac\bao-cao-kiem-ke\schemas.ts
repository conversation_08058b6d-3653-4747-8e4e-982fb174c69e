import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  ngay_tu: z.string().min(1, '<PERSON><PERSON><PERSON> từ không được để trống'),
  ngay_den: z.string().min(1, '<PERSON><PERSON><PERSON> đến không được để trống'),

  // Detail fields
  don_vi: z.string().optional(),
  ma_kho: z.string().optional(),
  so_phieu_kiem_ke: z.string().optional(),
  tuy_chon_xu_ly: z.string().optional(),
  nhap_xuat_chenh_lech: z.string().optional(),
  kiem_xem: z.string().optional(),
  mau_bao_cao: z.string().min(1, 'Mẫu báo cáo không được để trống'),

  // Other fields
  mau_loc_bao_cao: z.string().optional(),
  mau_phan_tich_dl: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_tu: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
  ngay_den: new Date().toISOString().split('T')[0],
  don_vi: '0',
  ma_kho: '',
  so_phieu_kiem_ke: '',
  tuy_chon_xu_ly: '0',
  nhap_xuat_chenh_lech: '0',
  kiem_xem: '0',
  mau_bao_cao: '0',
  mau_loc_bao_cao: '0',
  mau_phan_tich_dl: '0'
};

// Keep the old schema for backward compatibility
import * as yup from 'yup';
export const baoCaoKiemKeSchema = yup.object({}).required();
