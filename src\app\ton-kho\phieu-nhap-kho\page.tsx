'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  getWarehouseReceiptColumns,
  warehouseReceiptDetailColumns
} from '@/features/ton-kho/phieu-nhap-kho/cols-definition';
import { WarehouseReceiptActionBar } from '@/features/ton-kho/phieu-nhap-kho/components/WarehouseReceiptActionBar';
import { WarehouseReceiptItemsTab } from '@/features/ton-kho/phieu-nhap-kho/components/WarehouseReceiptItemTab';
import { BasicInfoTab } from '@/features/ton-kho/phieu-nhap-kho/components/BasicInfoTab';
import { BottomBar } from '@/features/ton-kho/phieu-nhap-kho/components/BottomBar';
import { warehouseReceiptSchema } from '@/features/ton-kho/phieu-nhap-kho/schemas';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';

// Mock data for initial state
const initialReceiptRows: any[] = [
  {
    id: 1,
    status: 'Chờ duyệt',
    receiptNumber: 'PN001',
    date: '2023-07-15',
    objectCode: 'NCC001',
    objectName: 'Công ty ABC',
    departmentCode: 'IT',
    description: 'Phiếu nhập kho tháng 7/2023',
    totalAmount: ********,
    currency: 'VND',
    totalQuantity: 10,
    documentCode: 'CT001',
    createdBy: 'admin',
    createdAt: '2023-07-15',
    updatedBy: 'admin',
    updatedAt: '2023-07-15',
    receiptDetails: [
      {
        id: 'DET001',
        productCode: 'SP001',
        productName: 'Laptop Dell XPS 13',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 2,
        averagePrice: ********,
        priceInVND: ********,
        amountInVND: ********,
        debitAccount: '156',
        importReason: 'Mua hàng',
        creditAccount: '331',
        department: 'IT',
        product: 'SP001',
        price: ********,
        amount: ********,
        quantityReceived: 2,
        receiptNumber: 'PN001',
        receiptLine: '1'
      },
      {
        id: 'DET002',
        productCode: 'SP002',
        productName: 'Màn hình Dell 27"',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 3,
        averagePrice: 5000000,
        priceInVND: 5000000,
        amountInVND: ********,
        debitAccount: '156',
        importReason: 'Mua hàng',
        creditAccount: '331',
        department: 'IT',
        product: 'SP002',
        price: 5000000,
        amount: ********,
        quantityReceived: 3,
        receiptNumber: 'PN001',
        receiptLine: '2'
      }
    ]
  }
];

// Helper functions to calculate totals from receipt data
const calculateTotals = (selectedObj: any) => {
  if (!selectedObj) {
    return {
      totalQuantity: 0,
      totalAmount: 0
    };
  }

  // Initialize result object
  const result = {
    totalQuantity: 0,
    totalAmount: 0
  };

  // Calculate totals from receipt details
  if (selectedObj.receiptDetails && selectedObj.receiptDetails.length > 0) {
    selectedObj.receiptDetails.forEach((detail: any) => {
      // Total quantity
      result.totalQuantity += detail.quantity || 0;

      // Total amount
      result.totalAmount += detail.amountInVND || 0;
    });
  }

  return result;
};

export default function Page() {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [receiptRows, setReceiptRows] = useState<any[]>(initialReceiptRows);
  //Add and Edit
  const [inputReceiptDetail, setInputReceiptDetail] = useState<any[]>([]);

  // Helper function to calculate totals from current form state
  const calculateFormTotals = () => {
    // Initialize result object
    const result = {
      totalQuantity: 0,
      totalAmount: 0
    };

    // Calculate totals from current receipt details in the form
    if (inputReceiptDetail && inputReceiptDetail.length > 0) {
      inputReceiptDetail.forEach((detail: any) => {
        // Total quantity
        result.totalQuantity += Number(detail.quantity) || 0;

        // Total amount
        result.totalAmount += Number(detail.amountInVND) || 0;
      });
    }

    return result;
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.receiptDetails) {
      setInputReceiptDetail([...obj.receiptDetails]);
    } else {
      setInputReceiptDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.receiptDetails) {
      setInputReceiptDetail([...obj.receiptDetails]);
    } else {
      setInputReceiptDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputReceiptDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    console.log(inputReceiptDetail);
    console.log(data);
    try {
      if (inputReceiptDetail.length === 0) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi nhập liệu',
          html: `<p class="text-[15px]">Bạn chưa nhập chi tiết phiếu nhập</p>`
        });
        return;
      }

      // Validate receipt detail
      for (const row of inputReceiptDetail) {
        await warehouseReceiptSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...receiptRows.map(row => row.id || 0)) + 1;
        const newReceipt = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          receiptDetails: inputReceiptDetail
        };

        setReceiptRows([...receiptRows, newReceipt]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = receiptRows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              receiptDetails: inputReceiptDetail
            };
          }
          return row;
        });

        setReceiptRows(updatedRows);
      }

      setShowForm(false);
      setInputReceiptDetail([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const handleRowClick = (params: GridRowParams) => {
    const receipt = params.row as any;
    setSelectedObj(receipt);
    setInputReceiptDetail(receipt.receiptDetails);
  };

  interface FilterValue {
    name: string;
    value: string;
    color: string;
  }

  const filterValues: FilterValue[] = [
    { name: 'Lập chứng từ', value: 'Lập chứng từ', color: 'pink' },
    { name: 'Chờ duyệt', value: 'Chờ duyệt', color: '#EF4444' },
    { name: 'Nhập kho', value: 'Nhập kho', color: '#3B82F6' }
  ];
  const tables = [
    {
      name: 'Tất cả',
      rows: receiptRows,
      columns: getWarehouseReceiptColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = receiptRows.filter(row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: getWarehouseReceiptColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: receiptRows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: getWarehouseReceiptColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  const from = (
    <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Thực tế'
      >
        Thực tế
      </button>
    </div>
  );

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'lineItems',
                label: 'Chi tiết',
                component: (
                  <WarehouseReceiptItemsTab
                    value={inputReceiptDetail}
                    onChange={setInputReceiptDetail}
                    formMode={formMode}
                  />
                )
              },
              {
                id: 'filekem',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Phiếu nhập kho'}
            from={from}
            bottomBar={<BottomBar totalQuantity={0} totalAmount={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <WarehouseReceiptActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.receiptDetails || []}
                columns={warehouseReceiptDetailColumns}
                mode={formMode}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
