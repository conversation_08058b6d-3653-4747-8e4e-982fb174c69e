import React from 'react';

export interface BottomBarProps {
  totalQuantity: number;
  totalAmount: number;
  totalTax: number;
  totalPayment: number;
  formMode: 'add' | 'edit' | 'view';
}

export const BottomBar: React.FC<BottomBarProps> = ({
  totalQuantity,
  totalAmount,
  totalTax,
  totalPayment,
  formMode
}) => {
  return (
    <div className='bottom-0 left-0 z-50 w-full bg-white px-4 py-2 shadow-[0_-2px_8px_rgba(0,0,0,0.1)] lg:fixed'>
      <div className='mx-auto flex max-w-[1440px] items-start justify-between gap-4'>
        {/* LEFT COLUMN (Tổng số lượng) */}
        <div className='flex w-1/5 flex-col justify-start'>
          <div className='mb-2 grid grid-cols-[160px,1fr] items-center'>
            <div className='text-sm font-bold text-gray-700'>Tổng số lượng</div>
            <div className='border-b border-gray-300 pb-1 text-right text-sm font-bold'>{totalQuantity.toFixed(2)}</div>
          </div>
        </div>

        {/* MIDDLE COLUMN (Tổng tiền + Tổng thuế) */}
        <div className='flex w-1/5 flex-col justify-start'>
          <div className='mb-2 grid grid-cols-[160px,1fr] items-center'>
            <div className='text-sm font-bold text-gray-700'>Tổng tiền</div>
            <div className='border-b border-gray-300 pb-1 text-right text-sm font-bold'>
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              })
                .format(totalAmount)
                .replace('₫', ' VND')}
            </div>
          </div>

          <div className='grid grid-cols-[160px,1fr] items-center'>
            <div className='text-sm font-bold text-gray-700'>Tổng thuế</div>
            <div className='border-b border-gray-300 pb-1 text-right text-sm font-bold'>
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              })
                .format(totalTax)
                .replace('₫', ' VND')}
            </div>
          </div>
        </div>
        <div className='flex w-2/5 flex-col justify-start'></div>
        {/* RIGHT COLUMN (Tổng thanh toán) */}
        <div className='flex w-1/5 flex-col justify-end'>
          <div className='grid grid-cols-[160px,1fr] items-center'>
            <div className='text-sm font-bold text-gray-700'>Tổng thanh toán</div>
            <div className='border-b border-gray-300 pb-1 text-right text-sm font-bold text-blue-600'>
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              })
                .format(totalPayment)
                .replace('₫', ' VND')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
