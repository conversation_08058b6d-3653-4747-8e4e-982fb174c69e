import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

function ConfirmDialog({ onClose, onConfirm, open }: ConfirmDialogProps) {
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Xác nhận'
      maxWidth='sm'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            onClick={onConfirm}
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Không
          </Button>
        </>
      }
    >
      <p className='p-4 text-base font-medium'>
        Dữ liệu có thể bị mất nếu bạn thoát khỏi form này.
        <br />
        Bạn có chắc chắn muốn thoát?
      </p>
    </AritoDialog>
  );
}

export default ConfirmDialog;
