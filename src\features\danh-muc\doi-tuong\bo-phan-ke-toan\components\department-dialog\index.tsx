import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';

import { searchSchema, SearchFormValues, DepartmentFormattedData } from '../../schemas';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { <PERSON><PERSON><PERSON> } from '@/types/schemas/bo-phan.type';
import ChangeCodeDialog from '../chang-code-dialog';
import DepartmentForm from './DepartmentForm';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';

interface DepartmentDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: DepartmentFormattedData;
  onClose: () => void;
  selectedObj: BoPhan | null;
  addDepartment: (newDepartment: DepartmentFormattedData) => Promise<void>;
  updateDepartment: (uuid: string, updatedDepartment: DepartmentFormattedData) => Promise<void>;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

function DepartmentDialog({
  open,
  mode,
  initialData,
  onClose,
  selectedObj,
  addDepartment,
  updateDepartment,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: DepartmentDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showChangeCodeDialog, setShowChangeCodeDialog] = useState(false);

  const handleCloseConfirmDialog = () => {
    setShowConfirmDialog(false);
  };

  const handleConfirmClose = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  const handleSubmit = async (values: SearchFormValues) => {
    try {
      const formattedData: DepartmentFormattedData = {
        ma_bp: values.ma_bp,
        ten_bp: values.ten_bp,
        ten_bp2: values.ten_bp2 || null,
        ghi_chu: values.ghi_chu || null,
        status: values.status
      };

      if (mode === 'edit' && selectedObj) {
        formattedData.entity_model = selectedObj.entity_model;
        await updateDepartment(selectedObj.uuid, formattedData);
      } else if (mode === 'add') {
        await addDepartment(formattedData);
      }

      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  let title = '';
  switch (mode) {
    case 'add':
      title = 'Thêm bộ phận kế toán';
      break;
    case 'edit':
      title = 'Sửa bộ phận kế toán';
      break;
    case 'view':
      title = 'Xem bộ phận kế toán';
      break;
    default:
      title = 'Bộ phận kế toán';
  }

  const formInitialData = initialData
    ? {
        ma_bp: initialData.ma_bp,
        ten_bp: initialData.ten_bp,
        ten_bp2: initialData.ten_bp2 || undefined,
        ghi_chu: initialData.ghi_chu || undefined,
        status: initialData.status.toString()
      }
    : undefined;

  return (
    <>
      <AritoDialog
        open={open}
        onClose={() => setShowConfirmDialog(true)}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={searchSchema}
          onSubmit={handleSubmit}
          initialData={formInitialData}
          className='w-full md:min-w-[500px] lg:min-w-[700px]'
          headerFields={<DepartmentForm mode={mode} onChangeCodeClick={() => setShowChangeCodeDialog(true)} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}
              {mode !== 'view' && (
                <div className='flex w-full justify-end gap-2 p-4'>
                  <Button
                    className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
                    type='submit'
                    variant='contained'
                  >
                    <AritoIcon icon={884} marginX='4px' />
                    Đồng ý
                  </Button>

                  <Button onClick={() => setShowConfirmDialog(true)} variant='outlined'>
                    <AritoIcon icon={885} marginX='4px' />
                    Huỷ
                  </Button>
                </div>
              )}
            </>
          }
        />
      </AritoDialog>

      <ConfirmDialog open={showConfirmDialog} onClose={handleCloseConfirmDialog} onConfirm={handleConfirmClose} />

      {showChangeCodeDialog && (
        <ChangeCodeDialog
          open={showChangeCodeDialog}
          onClose={() => setShowChangeCodeDialog(false)}
          currentCode={initialData?.ma_bp || ''}
          onSuccess={() => {
            setShowChangeCodeDialog(false);
          }}
        />
      )}
    </>
  );
}

export default DepartmentDialog;
