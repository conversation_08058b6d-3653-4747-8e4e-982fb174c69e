import { Button } from '@mui/material';
import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
}

export const OtherTab: React.FC<OtherTabProps> = ({ formMode }) => {
  const disabled = formMode === 'view';

  return (
    <div className='flex flex-col gap-4 p-4'>
      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Mã đối tượng</Label>
        <FormField name='ma_kh' type='text' disabled={disabled} />
      </div>

      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Kèm theo</Label>
        <FormField name='so_ct_goc' type='number' defaultValue={0} disabled={disabled} />
      </div>

      <div className='flex items-center gap-4'>
        <Label htmlFor='choose-files' className='w-32 text-sm font-normal'>
          Chọn files
        </Label>
        <Button variant='outlined' component='label' disabled={disabled} size='small'>
          Chọn files
          <input type='file' hidden multiple />
        </Button>
      </div>

      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Chứng từ gốc</Label>
        <FormField name='dien_giai_ct_goc' type='text' disabled={disabled} />
      </div>
    </div>
  );
};
