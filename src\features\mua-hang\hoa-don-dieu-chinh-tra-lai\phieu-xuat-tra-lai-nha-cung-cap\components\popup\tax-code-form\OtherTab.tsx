import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const TaxCodeOtherTab = ({ formMode }: Props) => (
  <div className='p-4'>
    <div className='grid gap-x-6 gap-y-4 lg:grid-cols-1'>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
        <FormField
          label='Ngày sinh'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='ngay_sinh'
          type='date'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Số CMND/CCCD'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='so_cmnd_cccd'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
        <FormField
          label='Người đại diện p.luật'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='nguoi_dai_dien'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Chức vụ'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='chuc_vu'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        label='Số tài khoản'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='so_tai_khoan'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Tên ngân hàng'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='ten_ngan_hang'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Chi nhánh'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='chi_nhanh'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Tỉnh thành'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='tinh_thanh'
        type='text'
        disabled={formMode === 'view'}
      />
    </div>
  </div>
);
