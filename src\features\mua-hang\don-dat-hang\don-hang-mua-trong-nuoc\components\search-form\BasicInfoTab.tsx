import React from 'react';
import { DoiTuongSearchColBasicInfo } from '@/components/cac-loai-form/popup-form-type-1/cols-definition';
import { TKNoSearchColBasicInfo } from '@/components/cac-loai-form/popup-form-type-2/cols-definition';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import BasicInfoTabType2 from '@/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2';
import BasicInfoTabType1 from '@/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1';
import { Type2Tabs } from '@/components/cac-loai-form/popup-form-type-2/Tabs';
import { Type1Tabs } from '@/components/cac-loai-form/popup-form-type-1/Tabs';
import { FormField } from '@/components/custom/arito/form/form-field';
import DocumentNumberRange from '../DocumentNumberRange';
import { Ma<PERSON>hanVienColDef } from './cols-definition';
import { Label } from '@/components/ui/label';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <div className='flex flex-col space-y-2'>
        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
            <div className='flex w-full gap-2'>
              <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Số c/từ (từ/đến):</Label>
            <DocumentNumberRange
              fromDocumentNumberName='documentNumberStart'
              toDocumentNumberName='documentNumberEnd'
            />
          </div>
        </div>
      </div>
      <FormField
        className='flex max-w-[400px] items-center gap-x-12'
        label='Loại chứng từ'
        name='loai_chung_tu'
        type='select'
        labelClassName='min-w-[120px]'
        disabled={formMode === 'view'}
        options={[
          { value: '1', label: 'Tất cả' },
          { value: '2', label: '5. Đơn hàng' }
        ]}
        placeholder='Chọn loại chứng từ'
      />
      <FormField
        className='flex max-w-[400px] items-center gap-x-12'
        label='Giao dịch'
        name='giao_dich'
        type='select'
        labelClassName='min-w-[120px]'
        disabled={formMode === 'view'}
        options={[
          { value: '1', label: 'Tất cả' },
          { value: '2', label: 'DH. Đơn hàng' }
        ]}
        placeholder='Chọn giao dịch'
      />
      <FormField
        className='flex max-w-[400px] items-center gap-x-12'
        label='Mã nhà cung cấp'
        name='ma_nha_cung_cap'
        type='text'
        labelClassName='min-w-[120px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='tien-gui/hach-toan/giay-bao-no/tai-khoan-co'
        searchColumns={DoiTuongSearchColBasicInfo}
        actionButtons={['add', 'edit']}
        headerFields={<BasicInfoTabType1 formMode={formMode} />}
        tabs={Type1Tabs({ formMode })}
      />
      <FormField
        className='flex max-w-[400px] items-center gap-x-12'
        label='Mã nhân viên'
        name='ma_nhan_vien'
        type='text'
        labelClassName='min-w-[120px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='tien-gui/hach-toan/giay-bao-no/tai-khoan-co'
        searchColumns={MaNhanVienColDef}
      />
      <FormField
        className='flex items-center gap-x-12'
        label='Diễn giải'
        name='dien_giai'
        type='text'
        labelClassName='min-w-[120px]'
        disabled={formMode === 'view'}
      />
    </div>
  );
};

export default BasicInfoTab;
