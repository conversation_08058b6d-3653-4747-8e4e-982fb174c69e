import {
  MaNhapXuatSearchColDef,
  NhaCungCapSearchColDef,
  TaiKhoanNoSearchColDef,
  SoChungTuSearchColDef
} from '../cols-definition';
import { PaymentTermSelectField } from '@/components/custom/arito/form/search-fields/PaymentTermSelectField';
import { PaymentDueDateField } from '@/components/custom/arito/form/search-fields/PaymentDueDateField';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
interface Props {
  formMode: 'add' | 'edit' | 'view';
  showTaxCodePopupForm: boolean;
  setShowTaxCodePopupForm: (value: boolean) => void;
}

export const BasicInformationTab = ({ formMode, setShowTaxCodePopupForm, showTaxCodePopupForm }: Props) => {
  const isDisabled = formMode === 'view';
  return (
    <div className='p-4'>
      <div className='grid gap-x-6 gap-y-4 lg:grid-cols-5'>
        {/* Left column - 2/5 */}
        <div className='col-span-5 space-y-1 lg:col-span-2'>
          <div className='flex items-center gap-x-1'>
            <Label className='min-w-[160px]'>Mã nhà cung cấp</Label>
            <div className='w-full'>
              <SearchField
                className='w-full'
                disabled={isDisabled}
                searchEndpoint={`/erp/${QUERY_KEYS.NHA_CUNG_CAP}/`}
                searchColumns={NhaCungCapSearchColDef}
                displayRelatedField='ten_nha_cung_cap'
                columnDisplay='ma_nha_cung_cap'
              />
            </div>
          </div>

          <FormField
            label='Tên nhà cung cấp'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            name='ten_nha_cung_cap'
            type='text'
            disabled={formMode === 'view'}
          />

          <FormField
            label='Địa chỉ'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            name='dia_chi'
            type='text'
            disabled={formMode === 'view'}
          />

          <div className='flex items-center gap-x-1'>
            <Label className='min-w-[160px]'>Mã nhập xuất</Label>
            <div className='w-full'>
              <SearchField
                className='w-full'
                disabled={isDisabled}
                searchEndpoint={`/erp/${QUERY_KEYS.PHIEU_XUAT_TRA_LAI_NHA_CUNG_CAP}/`}
                searchColumns={MaNhapXuatSearchColDef}
                displayRelatedField='ten_ma_nhap_xuat'
                columnDisplay='ma_nhap_xuat'
              />
            </div>
          </div>

          <div className='flex items-center gap-x-1'>
            <Label className='min-w-[160px]'>Tài khoản nợ</Label>
            <div className='w-full'>
              <SearchField
                className='w-full'
                disabled={isDisabled}
                searchEndpoint={`/erp/${QUERY_KEYS.TAI_KHOAN}/`}
                searchColumns={TaiKhoanNoSearchColDef}
                displayRelatedField='ten_tai_khoan'
                columnDisplay='ma_tai_khoan'
              />
            </div>
          </div>
          <FormField
            label='Diễn giải'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            name='dien_giai'
            type='text'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Middle column - 2/5 */}
        <div className='col-span-5 space-y-1 lg:col-span-2'>
          <div className='flex items-center gap-x-2'>
            <FormField
              type='text'
              className='flex items-center gap-x-1'
              labelClassName='min-w-[160px]'
              label='Mã số thuế'
              name='taxCode'
              disabled={formMode === 'view'}
            />
            <div className='cursor-pointer'>
              <AritoIcon icon={15} />
            </div>
            <div className='cursor-pointer' onClick={() => setShowTaxCodePopupForm(true)}>
              <AritoIcon icon={58} />
            </div>
          </div>

          <FormField
            label='Người nhận'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            name='nguoi_nhan'
            type='text'
            disabled={formMode === 'view'}
          />

          <FormField
            label='Email'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            name='email'
            type='text'
            disabled={formMode === 'view'}
          />

          <PaymentTermSelectField
            name='han_thanh_toan'
            label='Hạn thanh toán'
            formMode={formMode === 'view' ? 'view' : formMode === 'edit' ? 'edit' : 'add'}
            labelClassName='w-32'
            inputClassName='w-48'
          />
        </div>

        {/* Right column - 1/5 */}
        <div className='col-span-1 space-y-1 lg:col-span-1'>
          <div className='flex items-center gap-x-1'>
            <Label className='min-w-[120px]'>Số chứng từ</Label>
            <div className='w-full'>
              <SearchField
                className='w-full'
                disabled={isDisabled}
                searchEndpoint={`/erp/${QUERY_KEYS.CHUNG_TU}/`}
                searchColumns={SoChungTuSearchColDef}
                displayRelatedField='ten_chung_tu'
                columnDisplay='ma_chung_tu'
              />
            </div>
          </div>

          <FormField
            label='Ngày chứng từ'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[120px]'
            name='ngay_chung_tu'
            type='date'
            disabled={formMode === 'view'}
          />

          <FormField
            label='Ngày hoá đơn'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[120px]'
            name='ngay_hoa_don'
            type='date'
            disabled={formMode === 'view'}
          />

          <div className='grid grid-cols-2 gap-x-4'>
            <FormField
              label='Ngoại tệ'
              className='flex items-center gap-x-1'
              labelClassName='min-w-[50px]'
              name='ngoai_te'
              type='select'
              disabled={formMode === 'view'}
              options={[
                { value: 0, label: 'VND' },
                { value: 1, label: 'USD' },
                { value: 2, label: 'EUR' },
                { value: 3, label: 'JPY' }
              ]}
            />
            <FormField
              label='Tỷ giá'
              className='flex items-center gap-x-1'
              labelClassName='min-w-[50px]'
              name='ty_gia'
              type='number'
              disabled={formMode === 'view'}
            />
          </div>

          <FormField
            label='Trạng thái'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[120px]'
            name='trang_thai'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { value: 0, label: 'Chưa ghi sổ' },
              { value: 1, label: 'Chờ duyệt' },
              { value: 2, label: 'Đã ghi sổ' },
              { value: 3, label: 'Huỷ' }
            ]}
          />

          <div className='flex w-full items-center gap-x-2 pl-[100px]'>
            <FormField
              className='flex items-center gap-x-1'
              labelClassName='min-w-[120px]'
              type='checkbox'
              label='Dữ liệu được nhận'
              name='du_lieu_duoc_nhan'
              disabled={formMode === 'view'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
