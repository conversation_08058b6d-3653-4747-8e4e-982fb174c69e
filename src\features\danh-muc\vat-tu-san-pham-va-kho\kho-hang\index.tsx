'use client';
import { FileText, LogOut, Pencil, Plus, Trash } from 'lucide-react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { Box } from '@mui/material';
import { useFormState, useRowSelection, useWarehouse } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { mainTabStyle } from '@/components/custom/arito/form/style';
import { warehouseSchema, initialFormValues } from './schemas';
import { AritoActionButton } from '@/components/custom/arito';
import { InfoTab, InventoryTab } from './components/tabs';
import { ActionBar, DeleteDialog } from './components';
import { AritoForm } from '@/components/custom/arito';
import { warehouseColumns } from './cols-definition';
import { cn } from '@/lib/utils';

export default function KhoHang() {
  const {
    formMode,
    handleCloseForm,
    handleCopyForm,
    handleOpenAddForm,
    handleOpenEditForm,
    setShowDeleteDialog,
    showDeleteDialog,
    showForm,
    activeTab,
    handleChangeTab,
    handleOpenViewForm,
    handleTabChange,
    copiedWarehouse,
    setSelectedWarehouse
  } = useFormState();

  const {
    selectedRow: selectedWarehouse,
    handleRowClick: baseHandleRowClick,
    selectedRowId,
    clearSelection
  } = useRowSelection();

  // Đồng bộ selectedWarehouse giữa useRowSelection và useFormState
  const handleRowClick = (params: any) => {
    baseHandleRowClick(params);
    setSelectedWarehouse(params.row);
  };
  const { warehouses, isLoading, addWarehouse, updateWarehouse, refreshWarehouses, deleteWarehouse } = useWarehouse();

  const handleFormSubmit = async (data: any) => {
    try {
      console.log('form', data);
      if (formMode === 'add') {
        await addWarehouse({
          unit_id: data.unit_id || null,
          ma_unit: data.ma_unit || '',
          ma_kho: data.ma_kho || '',
          ten_kho: data.ten_kho || '',
          ten_kho2: data.ten_kho2 || null,
          vi_tri_yn: data.vi_tri_yn || false,
          dai_ly_yn: data.dai_ly_yn || false,
          ma_nh: data.ma_nh || null,
          dia_chi: data.dia_chi || '',
          dien_thoai: data.dien_thoai || '',
          ghi_chu: data.ghi_chu || null,
          status: data.status || '1',
          entity_model: data.entity_model || ''
        });
      } else if (formMode === 'edit' && selectedWarehouse) {
        await updateWarehouse(selectedWarehouse.uuid, {
          unit_id: data.unit_id || null,
          ma_unit: data.ma_unit || '',
          ma_kho: data.ma_kho || '',
          ten_kho: data.ten_kho || '',
          ten_kho2: data.ten_kho2 || null,
          vi_tri_yn: data.vi_tri_yn || false,
          dai_ly_yn: data.dai_ly_yn || false,
          ma_nh: data.ma_nh || null,
          dia_chi: data.dia_chi || '',
          dien_thoai: data.dien_thoai || '',
          ghi_chu: data.ghi_chu || null,
          status: data.status || '1',
          entity_model: data.entity_model || ''
        });
      }
      await refreshWarehouses();
      handleCloseForm();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  console.log('selectedWarehouse', selectedWarehouse);

  const tables = [
    {
      name: 'Vật tư, sản phẩm',
      rows: [],
      columns: [],
      value: 0
    },
    {
      name: 'Lô hàng',
      rows: [],
      columns: [],
      value: 1
    },
    {
      name: 'Kho hàng',
      rows: warehouses,
      columns: warehouseColumns(),
      value: 2
    },
    {
      name: 'Vị trí kho hàng',
      rows: [],
      columns: [],
      value: 3
    }
  ];

  return (
    <div className='flex h-screen w-screen flex-col overflow-hidden'>
      {showForm && (
        <div className='flex h-full flex-col'>
          <div className='flex-1 overflow-y-auto pb-[120px]'>
            <AritoForm
              mode={formMode}
              schema={warehouseSchema}
              initialData={
                formMode === 'add'
                  ? copiedWarehouse
                    ? {
                        unit_id: copiedWarehouse.unit_id,
                        ma_unit: copiedWarehouse.ma_unit,
                        unit_name: '',
                        ma_kho: copiedWarehouse.ma_kho,
                        ten_kho: copiedWarehouse.ten_kho,
                        ten_kho2: copiedWarehouse.ten_kho2,
                        vi_tri_yn: copiedWarehouse.vi_tri_yn,
                        dai_ly_yn: copiedWarehouse.dai_ly_yn,
                        ma_nh: copiedWarehouse.ma_nh,
                        dia_chi: copiedWarehouse.dia_chi,
                        dien_thoai: copiedWarehouse.dien_thoai,
                        ghi_chu: copiedWarehouse.ghi_chu,
                        status: copiedWarehouse.status,
                        entity_model: copiedWarehouse.entity_model
                      }
                    : initialFormValues
                  : selectedWarehouse
                    ? {
                        unit_id: selectedWarehouse.unit_id,
                        ma_unit: selectedWarehouse.ma_unit,
                        unit_name: '',
                        ma_kho: selectedWarehouse.ma_kho,
                        ten_kho: selectedWarehouse.ten_kho,
                        ten_kho2: selectedWarehouse.ten_kho2,
                        vi_tri_yn: selectedWarehouse.vi_tri_yn,
                        dai_ly_yn: selectedWarehouse.dai_ly_yn,
                        ma_nh: selectedWarehouse.ma_nh,
                        dia_chi: selectedWarehouse.dia_chi,
                        dien_thoai: selectedWarehouse.dien_thoai,
                        ghi_chu: selectedWarehouse.ghi_chu,
                        status: selectedWarehouse.status,
                        entity_model: selectedWarehouse.entity_model
                      }
                    : {}
              }
              onSubmit={handleFormSubmit}
              onClose={handleCloseForm}
              subTitle='Danh mục kho hàng'
              headerFields={
                <>
                  {formMode === 'view' && (
                    <>
                      <Box sx={mainTabStyle}>
                        <Tabs
                          variant='scrollable'
                          scrollButtons='auto'
                          textColor='primary'
                          indicatorColor='primary'
                          value={activeTab}
                          onChange={handleChangeTab}
                        >
                          <Tab key={'thong tin'} value={'thong tin'} label={'Thông tin'} className='normal-case' />
                          <Tab key={'ton kho'} value={'ton kho'} label={'Tồn kho'} className='normal-case' />
                        </Tabs>
                      </Box>
                      <div className='w-full'>
                        <div key={'thong tin'} className={cn(activeTab === 'thong tin' ? 'block' : 'hidden', 'pb-24')}>
                          <InfoTab mode={formMode} />
                        </div>
                        <div key={'ton kho'} className={cn(activeTab === 'ton kho' ? 'block' : 'hidden', 'pb-24')}>
                          <InventoryTab />
                        </div>
                      </div>
                    </>
                  )}

                  {formMode !== 'view' && <InfoTab mode={formMode} />}
                </>
              }
              actionButtons={
                formMode === 'view' && [
                  <AritoActionButton
                    title='Thêm'
                    icon={Plus}
                    onClick={() => {
                      handleCloseForm();
                      handleOpenAddForm();
                    }}
                    variant='primary'
                    key={'them'}
                  />,
                  <AritoActionButton
                    title='Sửa'
                    icon={Pencil}
                    onClick={() => {
                      if (selectedWarehouse) {
                        handleCloseForm();
                        handleOpenEditForm();
                      }
                    }}
                    key={'sua'}
                  />,
                  <AritoActionButton
                    title='Xóa'
                    icon={Trash}
                    onClick={() => {
                      if (selectedWarehouse) {
                        handleCloseForm();
                        setShowDeleteDialog(true);
                      }
                    }}
                    key={'xoa'}
                  />,
                  <AritoActionButton
                    title='Sao chép'
                    icon={FileText}
                    onClick={() => {
                      if (selectedWarehouse) {
                        handleCloseForm();
                        handleCopyForm(selectedWarehouse);
                      }
                    }}
                    key={'sao-chep'}
                  />,
                  <AritoActionButton
                    title='Đóng'
                    icon={LogOut}
                    variant='destructive'
                    onClick={handleCloseForm}
                    key={'dong'}
                  />
                ]
              }
            />
          </div>
        </div>
      )}

      {!showForm && (
        <div className='flex h-full overflow-hidden'>
          <div className='flex flex-1 flex-col overflow-hidden'>
            <div className='flex shrink-0 items-center border-b bg-white p-2'>
              <div className='flex w-full'>
                <ActionBar
                  onAddClick={handleOpenAddForm}
                  onEditClick={() => {
                    if (selectedWarehouse) {
                      handleOpenEditForm();
                    }
                  }}
                  onDeleteClick={() => {
                    if (selectedWarehouse) {
                      setShowDeleteDialog(true);
                    }
                  }}
                  onCopyClick={() => {
                    if (selectedWarehouse) {
                      handleCopyForm(selectedWarehouse);
                    }
                  }}
                  onViewClick={() => {
                    if (selectedWarehouse) {
                      handleOpenViewForm();
                    }
                  }}
                  onSearchClick={() => {}}
                  onRefreshClick={refreshWarehouses}
                  onPrintClick={() => {}}
                />
              </div>
            </div>

            {isLoading && (
              <div className='flex h-64 items-center justify-center'>
                <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
              </div>
            )}

            {/* DataTable container */}
            {!isLoading && (
              <div className='w-full flex-1 overflow-auto'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedRowId}
                  defaultTabIndex={2}
                  onTabChange={(_, value: number) => {
                    const tabPaths = ['vat-tu-san-pham', 'lo-hang', 'kho-hang', 'vi-tri-kho-hang'];
                    handleTabChange(tabPaths[value]);
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {showDeleteDialog && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={() => {
            setShowDeleteDialog(false);
            clearSelection();
            refreshWarehouses();
          }}
          selectedWarehouse={selectedWarehouse}
        />
      )}
    </div>
  );
}
