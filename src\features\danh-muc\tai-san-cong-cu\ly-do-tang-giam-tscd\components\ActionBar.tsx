import { <PERSON><PERSON>, FileSearch, Pencil, Plus, RefreshCw, Table, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddIconClick: () => void;
  onEditIconClick: () => void;
  onDeleteIconClick: () => void;
  onCopyIconClick: () => void;
  onWatchIconClick: () => void;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddIconClick,
  onEditIconClick,
  onDeleteIconClick,
  onCopyIconClick,
  onWatchIconClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục lý do tăng giảm TSCD</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddIconClick} />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditIconClick} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteIconClick} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyIconClick} />
      <AritoActionButton title='Xem' icon={FileSearch} onClick={onWatchIconClick} />

      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={19} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={18} />,
            onClick: () => {},
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};
