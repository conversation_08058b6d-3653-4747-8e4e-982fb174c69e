import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const TaxCodeMoreInfoTab = ({ formMode }: Props) => (
  <div className='p-4'>
    <div className='grid gap-x-6 gap-y-4 lg:grid-cols-1'>
      <FormField
        label='Mô tả'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='mo_ta'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Địa chỉ giao hàng'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='dia_chi_giao_hang'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Lĩnh vực hoạt động'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='linh_vuc_hoat_dong'
        type='text'
        disabled={formMode === 'view'}
      />
    </div>
  </div>
);
