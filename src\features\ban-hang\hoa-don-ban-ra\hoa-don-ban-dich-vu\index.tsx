'use client';

import Split from 'react-split';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { useFormState, useSearchDialog, useTableData } from './hooks';
import { SearchDialog, ActionBar, FormDialog } from './components';
import { AritoDataTables } from '@/components/custom/arito';
import { getInputTableColumns } from './cols-definition';
import { useHoaDonBanDichVu } from '@/hooks';
import { DetailType } from './schema';

export default function HoaDonBanDichVuPage() {
  const {
    showForm,
    formMode,
    selectedRow,
    handleAdd,
    handleEdit,
    handleDelete,
    handleCopy,
    handlePrint,
    handleRowClick,
    closeForm
  } = useFormState();

  const { showSearchDialog, handleSearch, handleSearchSubmit, closeSearchDialog } = useSearchDialog();
  const {
    hoaDonBanDichVus,
    isLoading,
    addHoaDonBanDichVu,
    updateHoaDonBanDichVu,
    deleteHoaDonBanDichVu,
    refreshHoaDonBanDichVus
  } = useHoaDonBanDichVu();
  const { detailRows, totalAmount, totalDiscount, totalTax, tables } = useTableData(hoaDonBanDichVus);

  return (
    <div className='flex size-full min-h-[calc(100vh-120px)] flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={closeSearchDialog} onSearch={handleSearchSubmit} />
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          detailRows={detailRows}
          totalAmount={totalAmount}
          totalDiscount={totalDiscount}
          totalTax={totalTax}
          onClose={closeForm}
          addHoaDonBanDichVu={addHoaDonBanDichVu}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAdd}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onCopy={handleCopy}
            onPrint={handlePrint}
            onSearch={handleSearch}
            isEditDisabled={!selectedRow}
          />

          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={4}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='!h-[calc(100vh-250px)] overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>

            <div className='h-[250px] overflow-hidden'>
              <InputTable<DetailType>
                rows={detailRows}
                columns={getInputTableColumns()}
                mode={formMode}
                actionButtons={['export', 'pin']}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
