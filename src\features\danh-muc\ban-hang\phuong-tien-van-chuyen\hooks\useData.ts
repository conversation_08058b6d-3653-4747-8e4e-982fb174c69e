import { useState } from 'react';

const useData = (initialRows: any[]) => {
  // Dữ liệu mẫu thay thế sample data khi có dữ liệu thực
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.id
    }))
  );
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [currentObject, setCurrentObject] = useState<any>(null);
  const [showData, setShowData] = useState<boolean>(true);

  const handleRowClick = (params: any) => {
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted with data:', data);
    setCurrentObject(data);
  };

  const handleCopySubmit = (data: any) => {
    console.log('Copy submitted with data:', data);
    // TODO: Implement copy logic
  };

  const handleDeleteConfirm = () => {
    console.log('Delete confirmed');
    // TODO: Implement delete confirmation logic
  };

  const handleRefreshClick = () => {
    console.log('Refresh clicked');
    // TODO: Implement refresh logic
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
    // TODO: Implement fixed columns logic
  };

  return {
    rows,
    selectedRowIndex,
    currentObject,
    showData,
    handleRowClick,
    handleFormSubmit,
    handleCopySubmit,
    handleDeleteConfirm,
    handleRefreshClick,
    handleFixedColumnsClick
  };
};

export default useData;
