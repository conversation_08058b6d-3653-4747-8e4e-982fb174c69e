import React, { useState } from 'react';
import { <PERSON>ur<PERSON>cy<PERSON>ield } from '@/components/custom/arito/form/form-field/components';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Quy<PERSON><PERSON>hung<PERSON>u, <PERSON><PERSON><PERSON><PERSON> } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoHeaderTabs, TabItem } from '@/components/custom/arito';
import { QUERY_KEYS, quyenChungTuSearchColumns } from '@/constants';
import { Checkbox } from '@/components/ui/checkbox';
import { useHanThanhToan } from '@/hooks/queries';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInformationTabProps {
  formMode: FormMode;
  searchFieldStates: SearchFieldStates;
}

interface SearchFieldStates {
  customer: <PERSON><PERSON><PERSON><PERSON><PERSON> | null;
  setCustomer: (customer: <PERSON><PERSON><PERSON><PERSON><PERSON> | null) => void;
  employee: <PERSON><PERSON><PERSON><PERSON> | null;
  setEmployee: (employee: <PERSON><PERSON><PERSON><PERSON> | null) => void;
  account: <PERSON><PERSON><PERSON><PERSON> | null;
  setAccount: (account: TaiKhoan | null) => void;
  quyenChungTu: QuyenChungTu | null;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu | null) => void;
}

const InfoTab = ({ formMode, searchFieldStates }: { formMode: FormMode; searchFieldStates: SearchFieldStates }) => {
  const [collectMoneyChecked, setCollectMoneyChecked] = useState(false);

  const { customer, setCustomer, employee, setEmployee, account, setAccount, quyenChungTu, setQuyenChungTu } =
    searchFieldStates;

  const { paymentTerms, isLoading } = useHanThanhToan();

  const handleCheckbox = (checked: boolean) => {
    setCollectMoneyChecked(checked);
  };
  return (
    <div className='p-4'>
      <div className='flex'>
        <div className='min-w-0 flex-1'>
          <div className='flex items-center'>
            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Mã khách hàng</Label>
              <SearchField<KhachHang>
                type='text'
                displayRelatedField='customer_code'
                columnDisplay='customer_code'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
                searchColumns={[
                  { field: 'customer_code', headerName: 'Mã khách hàng', width: 150 },
                  { field: 'customer_name', headerName: 'Tên khách hàng', width: 200 }
                ]}
                value={customer?.customer_code || ''}
                relatedFieldValue={customer?.customer_name || ''}
                onRowSelection={setCustomer}
                disabled={formMode === 'view'}
              />
            </div>

            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Mã số thuế</Label>
              <FormField name='ma_so_thue' placeholder='Nhập và tra cứu' />
            </div>

            <div className='ml-4 flex items-center space-x-2'>
              <Checkbox
                id='pt_tao_yn'
                name='pt_tao_yn'
                checked={collectMoneyChecked}
                onCheckedChange={handleCheckbox}
                disabled={formMode === 'view'}
              />

              {!collectMoneyChecked && <Label>Thu tiền</Label>}
              {collectMoneyChecked && (
                <FormField
                  name='ma_httt'
                  type='select'
                  className='w-40'
                  options={[
                    { value: 'tien-mat', label: 'Tiền mặt' },
                    { value: 'chuyen-khoan', label: 'Chuyển khoản' },
                    { value: 'khac', label: 'Khác' }
                  ]}
                  disabled={formMode === 'view'}
                />
              )}
            </div>
          </div>

          <div className='flex items-center'>
            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Tên khách hàng</Label>
              <FormField
                name='ten_kh_thue'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập tên khách hàng/đơn vị'
              />
            </div>
            <div className='flex items-center gap-3'>
              <Label className='text-sm font-medium'>Dư công nợ</Label>
              <Label className='font-medium text-red-500'>0</Label>
            </div>
          </div>

          <div className='flex'>
            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Địa chỉ</Label>
              <FormField
                name='dia_chi'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập địa chỉ khách hàng'
              />
            </div>

            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Người mua hàng</Label>
              <FormField name='ong_ba' type='text' disabled={formMode === 'view'} placeholder='Người mua hàng' />
            </div>
          </div>

          <div className='flex items-center'>
            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Mã nhân viên</Label>
              <SearchField<NhanVien>
                type='text'
                displayRelatedField='ma_nhan_vien'
                columnDisplay='ma_nhan_vien'
                searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}`}
                searchColumns={[
                  { field: 'ma_nhan_vien', headerName: 'Mã nhân viên', width: 150 },
                  { field: 'ho_ten_nhan_vien', headerName: 'Tên nhân viên', width: 200 }
                ]}
                value={employee?.ma_nhan_vien || ''}
                relatedFieldValue={employee?.ho_ten_nhan_vien || ''}
                onRowSelection={setEmployee}
                disabled={formMode === 'view'}
              />
            </div>
            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Email</Label>
              <FormField name='e_mail' placeholder='Nhập email' />
            </div>
          </div>

          <div className='flex items-center'>
            <div className='grid grid-cols-[150px,1fr] items-center'>
              <Label className='text-sm font-medium'>Tài khoản nợ</Label>
              <SearchField<TaiKhoan>
                type='text'
                displayRelatedField='code'
                columnDisplay='code'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
                searchColumns={[
                  { field: 'code', headerName: 'Mã tài khoản', width: 150 },
                  { field: 'name', headerName: 'Tên tài khoản', width: 200 }
                ]}
                value={account?.code || ''}
                relatedFieldValue={account?.name || ''}
                onRowSelection={setAccount}
                disabled={formMode === 'view'}
              />
            </div>

            {!collectMoneyChecked && (
              <div className='grid grid-cols-[150px,1fr] items-center'>
                <Label className='text-sm font-medium'>Hạn thanh toán</Label>
                {!isLoading && (
                  <FormField
                    name='ma_tt'
                    type='select'
                    className='w-64'
                    options={paymentTerms.map(term => ({ value: term.uuid, label: term.ten_tt }))}
                  />
                )}
              </div>
            )}
          </div>

          <div className='grid grid-cols-[150px,1fr] items-center'>
            <Label className='text-sm font-medium'>Diễn giải</Label>
            <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
          </div>
        </div>

        {/* Right Column - Fixed width */}
        <div className='w-96 flex-shrink-0'>
          <div className='grid grid-cols-[150px,1fr] items-center'>
            <Label className='text-sm font-medium'>Số chứng từ</Label>
            <SearchField<QuyenChungTu>
              name='so_ct'
              type='text'
              disabled={formMode === 'view'}
              placeholder='Nhập số chứng từ'
              searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
              searchColumns={quyenChungTuSearchColumns}
              value={quyenChungTu?.ma_nk || ''}
              relatedFieldValue={quyenChungTu?.ten_nk || ''}
              onRowSelection={setQuyenChungTu}
            />
          </div>

          <div className='grid grid-cols-[150px,1fr] items-center'>
            <Label className='text-sm font-medium'>Ngày chứng từ</Label>
            <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} />
          </div>

          <CurrencyField name='ma_nt' disabled={formMode === 'view'} labelClassName='w-[150px]' />

          <div className='grid grid-cols-[150px,1fr] items-center'>
            <Label className='text-sm font-medium'>Trạng thái</Label>
            <FormField
              name='status'
              type='select'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: 'Chưa ghi sổ' },
                { value: '1', label: 'Chờ duyệt' },
                { value: '2', label: 'Đã ghi sổ' },
                { value: '3', label: 'Hủy' }
              ]}
            />
          </div>

          <div className='grid grid-cols-[150px,1fr] items-center'>
            <div></div>
            <div className='flex items-center space-x-2'>
              <Checkbox id='recordedData' defaultChecked disabled={formMode === 'view'} />
              <Label htmlFor='recordedData'>Dữ liệu được ghi nhận</Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function BasicInfoTab({ formMode, searchFieldStates }: BasicInformationTabProps) {
  const tabs: TabItem[] = [
    {
      id: 'info',
      label: 'Thông tin',
      component: <InfoTab formMode={formMode} searchFieldStates={searchFieldStates} />
    }
  ];

  return <AritoHeaderTabs tabs={tabs} />;
}
