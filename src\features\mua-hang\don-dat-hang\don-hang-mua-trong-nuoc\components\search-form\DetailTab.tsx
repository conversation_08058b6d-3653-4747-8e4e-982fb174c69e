import React from 'react';
import { DoiTuongSearchColBasicInfo } from '@/components/cac-loai-form/popup-form-type-1/cols-definition';
import { KhoHangSearchColBasicInfo } from '@/components/cac-loai-form/popup-form-type-3/cols-definition';
import BasicInfoTabType15 from '@/components/cac-loai-form/popup-form-type-15/BasicInfoTabType15';
import BasicInfoTabType3 from '@/components/cac-loai-form/popup-form-type-3/BasicInfoTabType3';
import { VatTuColDef } from '@/components/cac-loai-form/popup-form-type-11/cols-definition';
import { Type15Tabs } from '@/components/cac-loai-form/popup-form-type-15/Tabs';
import { FormField } from '@/components/custom/arito/form/form-field';
import { DonViColDef } from './cols-definition';
interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const DetailTab = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Mã vật tư'
        name='ma_vat_tu'
        type='text'
        labelClassName='min-w-[160px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='tien-gui/hach-toan/giay-bao-no/ma-khach-hang'
        searchColumns={VatTuColDef}
        actionButtons={['add', 'edit']}
        headerFields={<BasicInfoTabType15 formMode={formMode} />}
        tabs={Type15Tabs({ formMode })}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Mã kho'
        name='ma_kho'
        type='text'
        labelClassName='min-w-[160px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='tien-gui/hach-toan/giay-bao-no/ma-khach-hang'
        searchColumns={KhoHangSearchColBasicInfo}
        actionButtons={['add', 'edit']}
        headerFields={<BasicInfoTabType3 formMode={formMode} />}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Đơn vị'
        name='don_vi'
        type='text'
        labelClassName='min-w-[160px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='tien-gui/hach-toan/giay-bao-no/ma-khach-hang'
        searchColumns={DonViColDef}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Trạng thái'
        name='trang_thai'
        labelClassName='min-w-[160px]'
        type='select'
        disabled={formMode === 'view'}
        options={[
          { label: 'Tất cả', value: 'tat_ca' },
          { label: 'Chưa ghi sổ', value: 'chua_ghi_so' },
          { label: 'Chờ duyệt', value: 'cho_duyet' },
          { label: 'Đã ghi sổ', value: 'da_ghi_so' },
          { label: 'Đang chuyển lệnh', value: 'dang_chuyen_lenh' },
          { label: 'Hoàn thành', value: 'hoan_thanh' },
          { label: 'Hủy', value: 'huy' }
        ]}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Lọc người theo sd'
        name='loc_nguoi_theo_sd'
        type='select'
        labelClassName='min-w-[160px]'
        disabled={formMode === 'view'}
        options={[
          { label: 'Tất cả', value: 'tat_ca' },
          { label: 'Lọc theo người tạo', value: 'loc_theo_nguoi_tao' }
        ]}
      />
    </div>
  );
};

export default DetailTab;
