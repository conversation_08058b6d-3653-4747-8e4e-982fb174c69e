'use client';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import AritoDataTables from '@/components/custom/arito/data-tables';
import type { NhapXuat, NhapXuatInput } from '@/types/schemas';
import { LoadingOverlay } from '@/components/custom/arito';
import { useCRUD, useFormState, useRows } from '@/hooks';
import { getDataTableColumns } from './cols-definition';
import { FormDialog, ActionBar } from './components';
import { QUERY_KEYS } from '@/constants';

export default function LyDoNhapXuatPage() {
  const { addItem, updateItem, deleteItem, refreshData, isLoading, data } = useCRUD<NhapXuat, NhapXuatInput>({
    endpoint: QUERY_KEYS.LY_DO_NHAP_XUAT
  });

  const {
    showForm,
    showDelete,
    selectedObj,
    selectedRowIndex,
    formMode: mode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState<NhapXuat>();

  const handleSubmit = async (data: any) => {
    try {
      if (mode === 'add') {
        await addItem(data);
      } else if (mode === 'edit' && selectedObj) {
        await updateItem(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    }
    catch (err: any) {
      console.error('Error submitting form:', err);
    }
  };

  const tables = [
    {
      name: '',
      rows: data,
      columns: getDataTableColumns()
    }
  ];
  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          mode={mode}
          open={showForm}
          onClose={handleCloseForm}
          onAddButtonClick={handleAddClick}
          onEditButtonClick={handleEditClick}
          onCopyButtonClick={handleCopyClick}
          onWatchButtonClick={handleViewClick}
          onDeleteButtonClick={handleDeleteClick}
          onSubmit={handleSubmit}
          initialData={mode === 'add' && !isCopyMode ? undefined : selectedObj}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddIconClick={handleAddClick}
          onEditIconClick={handleEditClick}
          onDeleteIconClick={handleDeleteClick}
          onCopyIconClick={handleCopyClick}
          onWatchIconClick={handleViewClick}
          onRefreshClick={refreshData}
        />
        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showDelete && (
        <ConfirmationDialog
          onClose={handleCloseDelete}
          onConfirm={() => {
            deleteItem(selectedObj!.uuid);
            handleCloseDelete();
            clearSelection();
            handleCloseForm();
          }}
        />
      )}
    </div>
  );
}
