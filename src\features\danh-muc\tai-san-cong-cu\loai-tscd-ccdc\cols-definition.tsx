import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import AritoCheckboxCellRenderer from '@/components/custom/arito/cell-renderers/arito-checkbox-cell-renderer';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

// Cell renderer for loai_tscc column
const AssetTypeCellRenderer = (params: GridRenderCellParams) => {
  if (params.value === '1') return <span>Tài sản</span>;
  if (params.value === '2') return <span>Công cụ</span>;
  return <span>{params.value}</span>;
};

export const Columns: GridColDef[] = [
  {
    field: 'loai_tscc',
    headerName: 'Tài sản/Công cụ',
    width: 200,
    renderCell: AssetTypeCellRenderer
  },
  {
    field: 'ma_lts',
    headerName: 'Mã loại',
    width: 200
  },
  {
    field: 'ten_lts',
    headerName: 'Tên loại',
    width: 300
  }
];

export const SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'Mã tài khoản',
    headerName: 'Mã tài khoản',
    width: 120
  },
  {
    field: 'Tên tài khoản',
    headerName: 'Tên tài khoản',
    width: 200
  },
  {
    field: 'Tài khoản mẹ',
    headerName: 'Tài khoản mẹ',
    width: 200
  },
  {
    field: 'Tk sổ cái',
    headerName: 'Tk sổ cái',
    width: 200,
    renderCell: AritoCheckboxCellRenderer
  },
  {
    field: 'Tk chi tiết',
    headerName: 'Tk chi tiết',
    width: 200,
    renderCell: AritoCheckboxCellRenderer
  },
  {
    field: 'Bậc tk',
    headerName: 'Bậc tk',
    width: 200
  }
];
export const feeColumns: ExtendedGridColDef[] = [
  {
    field: 'Mã phí',
    headerName: 'Mã phí',
    width: 120
  },
  {
    field: 'Tên phí',
    headerName: 'Tên phí',
    width: 200
  }
];

export const SampleData = [
  {
    id: 1,
    assetType: 'Công cụ',
    code: '3123',
    name: '3123'
  },
  {
    id: 2,
    assetType: 'Công cụ',
    code: 'CCDC01',
    name: 'Máy móc, thiết bị'
  },
  {
    id: 3,
    assetType: 'Công cụ',
    code: 'CCDC02',
    name: 'Thiết bị nội thất văn phòng'
  },
  {
    id: 4,
    assetType: 'Tài sản',
    code: 'TS01',
    name: 'Nhà cửa, vật kiến trúc'
  },
  {
    id: 5,
    assetType: 'Tài sản',
    code: 'TS02',
    name: 'Máy móc, thiết bị'
  },
  {
    id: 6,
    assetType: 'Tài sản',
    code: 'TS03',
    name: 'Phương tiện vận tải, truyền dẫn'
  },
  {
    id: 7,
    assetType: 'Tài sản',
    code: 'TS04',
    name: 'Thiết bị dụng cụ quản lý'
  },
  {
    id: 8,
    assetType: 'Tài sản',
    code: 'TS05',
    name: 'Tài sản cố định hữu hình khác'
  },
  {
    id: 9,
    assetType: 'Tài sản',
    code: 'TS06',
    name: 'Tài sản cố định vô hình'
  }
];

export const debtTrackingAccount = [
  { value: '0', label: '0. Không theo dõi công nợ' },
  { value: '1', label: '1. Công nợ phải thu' },
  { value: '2', label: '1. Công nợ phải trả' },
  { value: '3', label: '1. Công nợ khác' }
];

export const debtCalculationMethod = [
  { value: '0', label: '0. Không tính chênh lệnh' },
  { value: '1', label: '1. Trung bình tháng' },
  { value: '2', label: '2. Đích danh' },
  { value: '3', label: '3. Trung bình di động' }
];

export const accountType = [
  { value: '0', label: '0. Không tính chênh lệnh' },
  { value: '1', label: '1. Trung bình tháng' },
  { value: '2', label: '2. Đích danh' },
  { value: '3', label: '3. Trung bình di động' }
];

export const accountTypeOptions = [
  { value: 'KPL', label: 'KPL. Không phân loại' },
  { value: 'TA', label: 'TA. Thuế' },
  { value: 'TI', label: 'TI. Tiền' },
  { value: 'CN', label: 'CN. Công nợ' },
  { value: 'NH', label: 'NH. Ngân hàng' },
  { value: 'VV', label: 'VV. Vụ việc' },
  { value: 'HD', label: 'HD. Hợp đồng' },
  { value: 'KU', label: 'KU. Khế ước' },
  { value: 'MR1', label: 'MR1. Mở rộng 1' },
  { value: 'MR2', label: 'MR2. Mở rộng 2' }
];
