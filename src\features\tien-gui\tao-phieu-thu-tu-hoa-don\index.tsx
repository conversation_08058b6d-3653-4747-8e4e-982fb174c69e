'use client';

import React from 'react';
import { getDataTableColumnsCreate, getDataTableColumnsDelete } from './cols-definition';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { useDialogState, useTaoPhieuThuTuHoaDon } from './hooks';
import { FormDialog, ActionBar } from './components';

export default function TaoPhieuThuTuHoaDonPage() {
  const { dialogOpen, searchParams, handleDialogClose, handleDialogOpen, handleFormSubmit } = useDialogState();

  const { isLoading, isSuccess, error, createdReceiptId, createReceiptFromInvoice } = useTaoPhieuThuTuHoaDon();

  const handleSearchClick = () => {
    handleDialogOpen();
  };

  const handleRefreshClick = () => {
    console.log('Refreshing data...');
  };

  const handleCreateReceiptClick = () => {
    if (!searchParams) return;
  };

  const handleDeleteReceiptClick = () => {
    if (!searchParams) return;
  };

  const handleSubmitWithReceiptCreation = async (values: any) => {
    try {
      await createReceiptFromInvoice(values);
      handleFormSubmit(values);
    } catch (error) {
      console.error('Error creating receipt:', error);
    }
  };

  const getColumns = () => {
    if (!searchParams) return [];
    if (searchParams.xu_ly === 'Tạo') {
      return getDataTableColumnsCreate();
    }

    return getDataTableColumnsDelete();
  };

  const tables = [
    {
      name: 'Kết quả tìm kiếm',
      columns: getColumns(),
      rows: []
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <ActionBar
        onSearchClick={handleSearchClick}
        onRefreshClick={handleRefreshClick}
        onCreateReceiptClick={handleCreateReceiptClick}
        onDeleteReceiptClick={handleDeleteReceiptClick}
        searchParams={searchParams}
      />

      {/* Data Tables */}
      <div className='flex-1 overflow-hidden'>
        {isLoading && <LoadingOverlay />}
        {!isLoading && <AritoDataTables tables={tables} />}
      </div>

      <FormDialog open={dialogOpen} onClose={handleDialogClose} onSubmit={handleSubmitWithReceiptCreation} />
    </div>
  );
}
