import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { NhaCungCap } from '@/types/schemas/nha-cung-cap.type';
import { AritoIcon } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';

export const exportMainColumns: GridColDef<NhaCungCap>[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    renderHeader: () => <Checkbox onChange={() => {}} />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.value} />
  },
  { field: 'customer_code', headerName: 'Mã nhà cung cấp', width: 150 },
  { field: 'customer_name', headerName: 'Tên nhà cung cấp', width: 220 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 120 },
  { field: 'delivery_address', headerName: 'Địa chỉ', width: 200 },
  {
    field: 'sales_rep_data',
    headerName: 'Mã nhân viên',
    width: 120,
    renderCell: (params: { row: NhaCungCap }) => params.row.sales_rep_data?.ma_nhan_vien
  },
  { field: 'cog_no_ptra', headerName: 'Công nợ p/trả', width: 120 },
  {
    field: 'refresh',
    headerName: '',
    width: 50,
    renderCell: () => {
      return (
        <div className='flex h-full w-full items-center justify-center bg-gray-200'>
          <AritoIcon icon={15} />
        </div>
      );
    }
  },
  { field: 'gioi_han_no', headerName: 'Trạng thái doanh nghiệp', width: 120, type: 'number' },
  {
    field: 'customer_group1_data',
    headerName: 'Nhóm 1',
    width: 120,
    renderCell: (params: { row: NhaCungCap }) => params.row.customer_group1_data?.ten_phan_nhom
  },
  {
    field: 'customer_group2_data',
    headerName: 'Nhóm 2',
    width: 120,
    renderCell: (params: { row: NhaCungCap }) => params.row.customer_group2_data?.ten_phan_nhom
  },
  {
    field: 'customer_group3_data',
    headerName: 'Nhóm 3',
    width: 120,
    renderCell: (params: { row: NhaCungCap }) => params.row.customer_group3_data?.ten_phan_nhom
  },
  { field: 'phone', headerName: 'Điện thoại', width: 120 },
  { field: 'email', headerName: 'Email', width: 180 }
];
