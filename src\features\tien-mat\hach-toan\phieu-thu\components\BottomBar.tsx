// Define FormMode type to match the AritoForm component
type FormMode = 'add' | 'edit' | 'view';

interface BottomBarProps {
  totalAmount: number;
  formMode: FormMode;
}

export const BottomBar: React.FC<BottomBarProps> = ({ totalAmount, formMode }) => {
  return (
    <div className='fixed bottom-0 left-0 z-50 w-full border-t border-gray-200 bg-white p-2'>
      <div className='mx-auto flex max-w-[1440px] items-center space-x-4'>
        <span className='text-sm font-medium'>Tổng tiền:</span>
        <span className='text-lg font-bold'>
          {totalAmount.toLocaleString('vi-VN', {
            style: 'currency',
            currency: 'VND'
          })}
        </span>
      </div>
    </div>
  );
};
