import { GridColDef } from '@mui/x-data-grid';
import { GroupType } from '@/types/schemas';

export const exportMainColumns: GridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 150 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 250 }
];

export const groupTypes = [
  { label: 'Nhóm phí 1', value: GroupType.FEE1 },
  { label: 'Nhóm phí 2', value: GroupType.FEE2 },
  { label: 'Nhóm phí 3', value: GroupType.FEE3 }
];
