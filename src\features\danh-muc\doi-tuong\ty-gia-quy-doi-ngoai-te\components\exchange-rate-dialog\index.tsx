import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';

import { searchSchema, SearchFormValues, ExchangeRateFormattedData } from '../../schemas';
import { TyGiaQuyDoiNgoaiTe } from '@/types/schemas/ty-gia-quy-doi-ngoai-te.type';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import ExchangeRateForm from './ExchangeRateForm';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';

interface ExchangeRateDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: SearchFormValues;
  onClose: () => void;
  onSubmit?: (data: ExchangeRateFormattedData) => void;
  selectedObj?: TyGiaQuyDoiNgoaiTe | null;
  updateExchangeRate?: (uuid: string, data: ExchangeRateFormattedData) => Promise<void>;
  addExchangeRate?: (data: ExchangeRateFormattedData) => Promise<void>;
  // Additional props for view mode actions
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

function ExchangeRateDialog({
  open,
  mode,
  initialData,
  onClose,
  onSubmit,
  selectedObj,
  updateExchangeRate,
  addExchangeRate,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: ExchangeRateDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleSubmit = async (data: SearchFormValues) => {
    const formattedData: ExchangeRateFormattedData = {
      ngay_hl: data.ngay_hl,
      ma_nt: data.ma_nt,
      ty_gia: data.ty_gia
    };

    try {
      if (mode === 'add' && addExchangeRate) {
        await addExchangeRate(formattedData);
      } else if (mode === 'edit' && selectedObj && updateExchangeRate) {
        await updateExchangeRate(selectedObj.uuid, formattedData);
      }
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  const title = mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem chi tiết';

  return (
    <>
      <AritoDialog
        open={open}
        onClose={() => setShowConfirmDialog(true)}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={searchSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<ExchangeRateForm mode={mode} selectedObj={selectedObj} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}
              {mode !== 'view' && (
                <div className='flex justify-end gap-2 p-4'>
                  <Button
                    className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
                    type='submit'
                    variant='contained'
                  >
                    <AritoIcon icon={884} marginX='4px' />
                    Đồng ý
                  </Button>

                  <Button onClick={() => setShowConfirmDialog(true)} variant='outlined'>
                    <AritoIcon icon={885} marginX='4px' />
                    Huỷ
                  </Button>
                </div>
              )}
            </>
          }
        />
      </AritoDialog>

      <ConfirmDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleCloseDialog}
      />
    </>
  );
}

export default ExchangeRateDialog;
