'use client';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import { filter } from 'lodash';
import { exportBottomColumns, exportMainColumns } from './cols-definition';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoColoredDot from '@/components/custom/arito/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import SearchForm from './components/search-form/SearchForm';
import AddForm from './components/add-form/AddForm';
import { ActionBar } from './components/ActionBar';
import { filterValues } from './types/filterTabs';
import { mockTableData } from './mock-data/data';

export default function DonHangMuaTrongNuocPage() {
  const [showForm, setShowForm] = useState(false);
  const [showSearchForm, setShowSearchForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [inputDetails, setInputDetails] = useState<any[]>([]);
  const [rows, setRows] = useState<any[]>(mockTableData);
  const [showTaxCodePopupForm, setShowTaxCodePopupForm] = useState(false);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputDetails([]);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const obj = params.row as any;
    const rowId = +obj?.id + 1;
    const fullData = mockTableData.find(item => item.id == rowId);
    if (fullData && fullData.details) {
      setSelectedObj(fullData);
      setInputDetails(fullData.details);
    }
  };

  const handleFormSubmit = async (data: any) => {
    // TODO: Implement form submission
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: rows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm ? (
        <AddForm
          open={showForm}
          onClose={handleCloseForm}
          formMode={formMode}
          initialData={currentObj}
          handleFormSubmit={handleFormSubmit}
          handleCloseForm={handleCloseForm}
          showTaxCodePopupForm={showTaxCodePopupForm}
          setShowTaxCodePopupForm={setShowTaxCodePopupForm}
        />
      ) : (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={() => {}}
            onCopyClick={() => {}}
            onSearchClick={() => setShowSearchForm(true)}
            onRefreshClick={() => {}}
            onPrintClick={() => {}}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.id || undefined}
              />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={inputDetails}
                columns={exportBottomColumns}
                tableActionButtons={['export', 'pin']}
                mode='view'
              />
            </div>
          </Split>
        </>
      )}
      {showSearchForm && (
        <SearchForm
          open={showSearchForm}
          onClose={setShowSearchForm}
          initialData={[]}
          handleSubmit={() => {}}
          formMode='add'
        />
      )}
    </div>
  );
}
