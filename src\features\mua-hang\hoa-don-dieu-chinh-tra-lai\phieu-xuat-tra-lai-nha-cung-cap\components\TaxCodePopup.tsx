import React from 'react';
import {
  TaxCodeBasicInformationTab,
  TaxCodeGeneralTab,
  TaxCodeMoreInfoTab,
  TaxCodeOtherTab
} from './popup/tax-code-form';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import AritoModal from '@/components/custom/arito/modal';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';

interface Props {
  showTaxCodePopup: boolean;
  setShowTaxCodePopup: (value: boolean) => void;
  formMode: 'add' | 'edit' | 'view';
}

export const TaxCodePopup = ({ showTaxCodePopup, setShowTaxCodePopup, formMode }: Props) => {
  const handleSubmit = (data: any) => {
    setShowTaxCodePopup(false);
  };
  return (
    <AritoModal
      open={showTaxCodePopup}
      onClose={() => {
        setShowTaxCodePopup(false);
      }}
      title={'Mới'}
      titleIcon={<AritoIcon icon={699} />}
      maxWidth='md'
    >
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
          <AritoForm<any>
            mode={formMode}
            hasAritoActionBar={false}
            className='!static !w-full'
            onSubmit={handleSubmit}
            onClose={() => {
              setShowTaxCodePopup(false);
            }}
            headerFields={<TaxCodeBasicInformationTab formMode={formMode} />}
            tabs={[
              {
                id: 'thong_tin_chung',
                label: 'Thông tin chung',
                component: <TaxCodeGeneralTab formMode={formMode} />
              },
              {
                id: 'khac',
                label: 'Khác',
                component: <TaxCodeOtherTab formMode={formMode} />
              },
              {
                id: 'thong_tin_them',
                label: 'Thông tin thêm',
                component: <TaxCodeMoreInfoTab formMode={formMode} />
              }
            ]}
          />
        </div>

        <BottomBar
          mode={formMode}
          onSubmit={() => {}}
          onClose={() => {
            setShowTaxCodePopup(false);
          }}
        />
      </div>
    </AritoModal>
  );
};
