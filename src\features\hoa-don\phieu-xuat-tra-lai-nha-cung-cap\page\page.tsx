'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  getSupplierReturnReceiptColumns,
  supplierReturnReceiptDetailColumns,
  supplierReturnReceiptItemColumns
} from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/cols-definition';
import { SupplierReturnReceiptActionBar } from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/components/ActionBar';
import { BasicInformationTab } from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/components/BasicInfoTab';
import { EInvoiceTab } from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/components/EInvoiceTab';
import { BottomBar } from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/components/BottomBar';
import { ItemsTab } from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/components/ItemsTab';
import { OtherTab } from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/components/OtherTab';
import { TaxTab } from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/components/TaxTab';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import {} from '@/features/hoa-don/phieu-xuat-tra-lai-nha-cung-cap/schemas';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';

export default function PhieuXuatTraLaiNhaCungCapPage({ initialRows }: { initialRows: any[] }) {
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  const [returnOrderRows, setReturnOrderRows] = useState<any[]>(initialRows);
  const [inputReturnOrderDetails, setInputReturnOrderDetails] = useState<any[]>([]);
  const [inputTaxDetail, setInputTaxDetail] = useState<any[]>([]);

  const handleCloseForm = () => setShowForm(false);

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.returnOrderDetails) {
      setInputReturnOrderDetails([...obj.returnOrderDetails]);
    } else {
      setInputReturnOrderDetails([]);
    }
    if (obj.expenses) {
      setInputTaxDetail([...obj.expenses]);
    } else {
      setInputTaxDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.returnOrderDetails) {
      setInputReturnOrderDetails([...obj.returnOrderDetails]);
    } else {
      setInputReturnOrderDetails([]);
    }
    if (obj.expenses) {
      setInputTaxDetail([...obj.expenses]);
    } else {
      setInputTaxDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputReturnOrderDetails([]);
    setInputTaxDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      for (const row of inputReturnOrderDetails) {
        // await returnOrderDetailSchema.validate(row);
      }

      for (const row of inputTaxDetail) {
        // await expenseSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...returnOrderRows.map(row => row.id || 0)) + 1;
        const newReturnOrder = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          statusHDDT: 'Chưa xuất',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          returnOrderDetails: inputReturnOrderDetails
        };

        setReturnOrderRows([...returnOrderRows, newReturnOrder]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = returnOrderRows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              returnOrderDetails: inputReturnOrderDetails
            };
          }
          return row;
        });

        setReturnOrderRows(updatedRows);
      }

      setShowForm(false);
      setInputReturnOrderDetails([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const convertToDetails = (order: any) => {
    return order.returnOrderItems.map((item: any) => ({
      ...item,
      productCode: item.productCode,
      productName: item.productName,
      unit: item.unit,
      warehouseCode: item.warehouseCode
    }));
  };

  const handleRowClick = (params: GridRowParams) => {
    let order = params.row as any;
    if (order.returnOrderItems) {
      const details = convertToDetails(order);
      order.returnOrderDetails = details;
    }

    console.log(order);

    setSelectedObj(order);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: returnOrderRows,
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Chưa ghi sổ',
      rows: returnOrderRows.filter(row => row.status === 'Lập chứng từ'),
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: returnOrderRows.filter(row => row.status === 'Chờ duyệt'),
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Đã ghi sổ',
      rows: returnOrderRows.filter(row => row.statusHDDT === 'Đã duyệt'),
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#3B82F6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Khác',
      rows: returnOrderRows,
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#000000' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  const from = (
    <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Hoá đơn mua hàng nhập khẩu'
      >
        Hoá đơn mua hàng nhập khẩu
      </button>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Hoá đơn mua hàng trong nước'
      >
        Hoá đơn mua hàng trong nước
      </button>
    </div>
  );

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={<BasicInformationTab formMode={formMode} />}
            tabs={[
              {
                id: 'items',
                label: 'Chi tiết',
                component: <ItemsTab formMode={formMode} columns={supplierReturnReceiptItemColumns} />
              },
              {
                id: 'tax',
                label: 'Thuế',
                component: <TaxTab formMode={formMode} />
              },
              {
                id: 'express',
                label: 'HĐĐT',
                component: <EInvoiceTab formMode={formMode} />
              },
              {
                id: 'more',
                label: 'Khác',
                component: <OtherTab formMode={formMode} />
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Phiếu xuất trả lại nhà cung cấp'}
            from={from}
            bottomBar={
              <BottomBar totalQuantity={0} totalTax={0} totalAmount={0} totalPayment={0} formMode={formMode} />
            }
          />
        </div>
      )}

      {!showForm && (
        <>
          <SupplierReturnReceiptActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
            onDeleteClick={() => {}}
            onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.returnOrderDetails || []}
                columns={supplierReturnReceiptDetailColumns}
                mode={formMode}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
