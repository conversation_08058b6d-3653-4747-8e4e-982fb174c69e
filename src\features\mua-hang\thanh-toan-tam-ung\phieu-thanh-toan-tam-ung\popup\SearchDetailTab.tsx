import { DateRangeField } from '@/components/custom/arito/form/search-fields';
import { FormField } from '@/components/custom/arito/form/form-field';

export const SearchDetailTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Tài khoản nợ'
        name='tai_khoan_no'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Đơn vị'
        name='don_vi'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='select'
        label='Trạng thái'
        name='trang_thai'
        disabled={formMode === 'view'}
        options={[
          { value: 0, label: 'Tất cả' },
          { value: 1, label: 'Chưa ghi sổ' },
          { value: 2, label: 'Chờ duyệt' },
          { value: 3, label: 'Đã ghi sổ' }
        ]}
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='select'
        label='Lọc theo người sd'
        name='loc_theo_nguoi_sd'
        disabled={formMode === 'view'}
        options={[
          { value: 0, label: 'Tất cả' },
          { value: 1, label: 'Lọc theo người tạo' }
        ]}
      />
    </div>
  </div>
);
