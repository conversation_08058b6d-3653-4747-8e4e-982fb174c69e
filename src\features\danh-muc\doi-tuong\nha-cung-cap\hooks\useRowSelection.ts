import { GridEventListener } from '@mui/x-data-grid';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';

interface UseRowSelectionReturn {
  selectedObj: any | null;
  selectedRowIndex: string | null;
  handleRowClick: GridEventListener<'rowClick'>;
  clearSelection: () => void;
}

const useRowSelection = (): UseRowSelectionReturn => {
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const handleRowClick = (params: GridRowParams) => {
    setSelectedRowIndex(params.id.toString());
    setSelectedObj(params.row);
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection
  };
};

export default useRowSelection;
