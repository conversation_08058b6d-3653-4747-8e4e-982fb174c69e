import { z } from 'zod';

export const FormSchema = z.object({
  tax_authority_code: z.string().nonempty(),
  tax_authority_name: z.string().nonempty(),
  tax_authority: z.string().nonempty(),
  status: z.string().nonempty()
});
export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  tax_authority_code: '',
  tax_authority_name: '',
  tax_authority: '',
  status: 'active'
};
