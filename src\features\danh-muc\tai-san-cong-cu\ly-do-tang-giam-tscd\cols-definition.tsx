import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getAssetChangeReasonColumns = (
  onCodeClick?: (uuid: string) => void,
  onEditClick?: () => void
): GridColDef[] => [
  {
    field: 'loai_tg_ts',
    headerName: 'Loại tăng giảm',
    width: 200,
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === undefined || value === null) return '';
      return <div>{value === '1' ? '1. Tăng' : '2. Giảm'}</div>;
    }
  },
  {
    field: 'ma_tg_ts',
    headerName: 'Mã tăng giảm',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <div
        className='cursor-pointer text-teal-600 hover:underline'
        onClick={e => {
          onCodeClick?.(params.row.uuid);
          e.stopPropagation();
        }}
      >
        {params.value}
      </div>
    )
  },
  { field: 'ten_tg_ts', headerName: 'Tên tăng giảm', width: 200 }
];

export const assetChangeReasonDetailColumns: GridColDef[] = [
  {
    field: 'loai_tg_ts',
    headerName: 'Loại tăng giảm',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === undefined || value === null) return '';
      return <div>{value === '1' ? '1. Tăng' : '2. Giảm'}</div>;
    }
  },
  { field: 'ma_tg_ts', headerName: 'Mã tăng giảm', width: 100 },
  { field: 'ten_tg_ts', headerName: 'Tên tăng giảm', width: 200 },
  { field: 'ten_tg_ts2', headerName: 'Tên khác', width: 200 },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === undefined || value === null) return '';
      return <div>{value === '1' ? '1. Còn sử dụng' : '0. Không sử dụng'}</div>;
    }
  }
];
