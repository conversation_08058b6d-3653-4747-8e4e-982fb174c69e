import { FormField } from '@/components/custom/arito/form';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
      {/* Cột trái */}
      <div className='col-span-4 space-y-2'>
        <FormField
          label='Loại phiếu thu'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[160px,1fr]'
          name='advanceType'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 2, label: '2. Tạm ứng thông thường' },
            { value: 3, label: '3. Cấn trừ công nợ' }
          ]}
        />
        <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-2 lg:space-y-0'>
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Mã khách hàng'
            name='customerCode'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Mã số thuế'
            name='customerCode'
            disabled={formMode === 'view'}
            withSearch={true}
          />
        </div>
        <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-2 lg:space-y-0'>
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Tên khách hàng'
            name='deliveryPerson'
            disabled={formMode === 'view'}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Người giao hàng'
            name='deliveryPerson'
            disabled={formMode === 'view'}
          />
        </div>
        <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-2 lg:space-y-0'>
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Địa chỉ'
            name='deliveryPerson'
            disabled={formMode === 'view'}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='text'
            label='Email'
            name='deliveryPerson'
            disabled={formMode === 'view'}
          />
        </div>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Tài khoản có'
          name='accountNumber'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          label='Diễn giải'
          type='text'
          name='description'
          disabled={formMode === 'view'}
        />
      </div>

      {/* Cột phải */}
      <div className='col-span-4 space-y-2 lg:col-span-1'>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          label='Số chứng từ'
          withSearch={true}
          type='text'
          name='documentNumber'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Ngày chứng từ'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          type='date'
          name='documentDate'
          disabled={formMode === 'view'}
        />
        <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-2 lg:space-y-0'>
          <FormField
            label='Ngoại tệ'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
            type='select'
            name='foreignCurrency'
            disabled={formMode === 'view'}
            options={[{ value: 'VND', label: 'VND' }]}
          />
          <FormField
            label='Tỷ giá'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[40px,1fr]'
            type='number'
            name='exchangeRate'
            disabled={formMode === 'view'}
          />
        </div>
        <FormField
          label='Trạng thái'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          name='status'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 0, label: 'Chưa ghi sổ' },
            { value: 1, label: 'Đã ghi sổ' },
            { value: 2, label: 'Chờ duyệt' }
          ]}
        />
        <div className='pl-[160px] lg:pl-[60px]'>
          <FormField
            type='checkbox'
            label='Dữ liệu được nhận'
            name='dataReceived'
            disabled={formMode === 'view'}
            className='flex items-center'
          />
        </div>
      </div>
    </div>
  </div>
);
