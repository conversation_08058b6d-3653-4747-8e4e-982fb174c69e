# Hooks Module

## Overview
The Hooks module provides custom React hooks that encapsulate reusable logic and stateful behavior across the application. These hooks enable components to share common functionality without duplicating code.

## Key Components
- `use-debounce.ts`: Hook for debouncing values to limit the rate of function calls
- `use-doc-type.tsx`: Hook for accessing and managing document type context
- `use-mobile.tsx`: Hook for detecting and responding to mobile device viewport
- `use-namespace.ts`: Hook for accessing and managing namespace context for internationalization
- `use-toast.ts`: Hook for displaying toast notifications
- `use-translations-with-namespace.ts`: Hook for accessing translations with namespace support

## Mermaid Diagrams

### Flowchart
```mermaid
flowchart TD
    A[Hooks] --> B[State Hooks]
    A --> C[Context Hooks]
    A --> D[Utility Hooks]
    
    B --> B1[useDebounce]
    B --> B2[useMobile]
    
    C --> C1[useDocType]
    C --> C2[useNamespace]
    C --> C3[useTranslationsWithNamespace]
    
    D --> D1[useToast]
```

### Component Diagram
```mermaid
classDiagram
    class ReactHook {
        +function()
    }
    
    class useDebounce {
        +value: any
        +delay: number
        +debouncedValue: any
    }
    
    class useDocType {
        +docType: string
        +setDocType: function
    }
    
    class useMobile {
        +isMobile: boolean
    }
    
    class useNamespace {
        +namespace: string
        +setNamespace: function
    }
    
    class useToast {
        +toast: function
    }
    
    class useTranslationsWithNamespace {
        +t: function
        +namespace: string
    }
    
    ReactHook <|-- useDebounce
    ReactHook <|-- useDocType
    ReactHook <|-- useMobile
    ReactHook <|-- useNamespace
    ReactHook <|-- useToast
    ReactHook <|-- useTranslationsWithNamespace
    
    useDocType --> DocTypeContext
    useNamespace --> NamespaceContext
    useTranslationsWithNamespace --> useNamespace
```

### Sequence Diagram
```mermaid
sequenceDiagram
    participant Component
    participant Hook
    participant Context
    participant ExternalAPI
    
    Component->>Hook: Call hook
    
    alt Context Hook
        Hook->>Context: Access context
        Context-->>Hook: Return context value
    else State Hook
        Hook->>Hook: Initialize state
    else Utility Hook
        Hook->>ExternalAPI: Interact with external API
        ExternalAPI-->>Hook: Return result
    end
    
    Hook-->>Component: Return hook value/functions
    Component->>Component: Use hook value/functions
