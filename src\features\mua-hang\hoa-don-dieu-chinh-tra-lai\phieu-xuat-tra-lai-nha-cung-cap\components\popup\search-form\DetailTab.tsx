import React from 'react';
import BasicInfoTabType1 from '@/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1';
import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const SearchDetailTab = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Tk có'
        name='tai_khoan_co'
        disabled={formMode === 'view'}
        withSearch={true}
        headerFields={<BasicInfoTabType1 formMode={formMode} />}
        searchColumns={[
          { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', width: 150 },
          { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', width: 150 },
          { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', width: 150 },
          { field: 'tk_so_cai', headerName: 'Tk sổ cái', width: 150 },
          { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', width: 150 },
          { field: 'bac_tk', headerName: 'Bậc tk', width: 150 }
        ]}
        searchEndpoint='/ke-toan/tai-khoan/tai-khoan'
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Mã kho'
        name='ma_kho'
        disabled={formMode === 'view'}
        withSearch={true}
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Mã lô'
        name='ma_lo'
        disabled={formMode === 'view'}
        withSearch={true}
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Mã vị trí'
        name='ma_vi_tri'
        disabled={formMode === 'view'}
        withSearch={true}
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Đơn vị'
        name='don_vi'
        disabled={formMode === 'view'}
        withSearch={true}
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='select'
        label='Trạng thái'
        name='trang_thai'
        disabled={formMode === 'view'}
        options={[
          { value: 0, label: 'Tất cả' },
          { value: 1, label: 'Chưa ghi sổ' },
          { value: 2, label: 'Chờ duyệt' },
          { value: 3, label: 'Đã ghi sổ' },
          { value: 4, label: 'Hủy' }
        ]}
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='select'
        label='Lọc theo người sd'
        name='loc_theo_nguoi_sd'
        disabled={formMode === 'view'}
        options={[
          { value: 0, label: 'Tất cả' },
          { value: 1, label: 'Lọc theo người tạo' }
        ]}
      />
    </div>
  );
};

export default SearchDetailTab;
