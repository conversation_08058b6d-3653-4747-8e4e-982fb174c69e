import React from 'react';
import { accountTypeOptions, debtTrackingAccount } from '../../../../cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

function GeneralInfoTab() {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-2 md:space-y-2'>
          {/* Tên ngắn */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên ngắn</Label>
            <FormField type='text' name='shortName' className='w-[400px]' />
          </div>

          {/* Tên ngắn khác */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên ngắn khác</Label>
            <FormField type='text' name='otherShortName' className='w-[400px]' />
          </div>

          {/* Tài khoản sổ cái & Ngoại tệ gốc */}
          <div className='flex items-center gap-2'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tài khoản sổ cái</Label>
              <FormField type='checkbox' name='isLedgerAccount' />
            </div>
            <div className='ml-8 flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ngoại tệ gốc</Label>
              <FormField
                type='select'
                name='originalCurrency'
                options={[
                  { value: 'VND', label: 'VND' },
                  { value: 'USD', label: 'USD' },
                  { value: 'EUR', label: 'EUR' },
                  { value: 'JPY', label: 'JPY' }
                ]}
                defaultValue='VND'
                className='w-[100px]'
              />
            </div>
          </div>

          {/* TK theo dõi công nợ */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>TK theo dõi công nợ</Label>
            <FormField
              type='select'
              name='debtTrackingAccount'
              options={debtTrackingAccount}
              defaultValue='0'
              className='w-[400px]'
            />
          </div>

          {/* Phân loại tài khoản */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Phân loại tài khoản</Label>
            <FormField type='multiselect' name='accountType' className='w-[400px]' options={accountTypeOptions} />
          </div>

          {/* PP tính TGNS nợ */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>PP tính TGNS nợ</Label>
            <FormField
              type='select'
              name='debtCalculationMethod'
              options={[{ value: '0', label: '0. Không tính chênh lệch' }]}
              defaultValue='0'
              className='w-[400px]'
            />
          </div>

          {/* PP tính TGNS có */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>PP tính TGNS có</Label>
            <FormField
              type='select'
              name='creditCalculationMethod'
              options={[{ value: '0', label: '0. Không tính chênh lệch' }]}
              defaultValue='0'
              className='w-[400px]'
            />
          </div>

          {/* Ghi chú */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ghi chú</Label>
            <FormField type='text' name='otherShortName' className='w-[400px]' />
          </div>

          {/* Trạng thái */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Trạng thái</Label>
            <FormField
              type='select'
              name='status'
              options={[
                { value: '1', label: '1. Còn sử dụng' },
                { value: '0', label: '0. Không sử dụng' }
              ]}
              defaultValue='1'
              className='w-[400px]'
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default GeneralInfoTab;
