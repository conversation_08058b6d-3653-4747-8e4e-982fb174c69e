'use client';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view'; activeGroup?: string }) => {
  return (
    <div className='p-4'>
      <div className='flex flex-col space-y-4'>
        {/* Thông tin nhóm khách hàng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhóm</Label>
          <FormField type='text' name='ma_nhom' className='w-64' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên phân nhóm</Label>
          <FormField type='text' name='ten_phan_nhom' className='w-64' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên 2</Label>
          <FormField type='text' name='ten2' className='w-64' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Trạng thái</Label>
          <FormField
            type='select'
            name='trang_thai'
            className='w-64'
            options={[
              { value: '1', label: '1. Còn sử dụng' },
              { value: '0', label: '0. Không sử dụng' }
            ]}
            defaultValue={'1'}
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
};
