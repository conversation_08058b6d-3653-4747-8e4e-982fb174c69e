import React from 'react';
import AritoDateRangeWithDropdown from '@/components/custom/arito/form/date-range-with-dropdown';
import BasicInfoTabType1 from '@/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1';
import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const SearchBasicInfoTab = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <div className='grid grid-cols-[150px,1fr] items-center gap-x-4'>
        <div className='mt-3 flex items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
          Ng<PERSON>y từ/đến
        </div>
        <AritoDateRangeWithDropdown fromDateName='ngay_tu' toDateName='ngay_den' />
      </div>
      <div className='grid grid-cols-[150px,1fr,1fr] items-center gap-x-4'>
        <div className='mt-3 flex items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
          Số c/từ (từ/đến)
        </div>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='number'
          name='so_ctu_tu'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='number'
          name='so_ctu_den'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Mã NCC'
        name='ma_ncc'
        disabled={formMode === 'view'}
        withSearch={true}
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Tài khoản nợ'
        name='tai_khoan_no'
        disabled={formMode === 'view'}
        withSearch={true}
        headerFields={<BasicInfoTabType1 formMode={formMode} />}
        searchColumns={[
          { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', width: 150 },
          { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', width: 150 },
          { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', width: 150 },
          { field: 'tk_so_cai', headerName: 'Tk sổ cái', width: 150 },
          { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', width: 150 },
          { field: 'bac_tk', headerName: 'Bậc tk', width: 150 }
        ]}
        searchEndpoint='/ke-toan/tai-khoan/tai-khoan'
      />
      <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Diễn giải'
        name='dien_giai'
        disabled={formMode === 'view'}
        withSearch={true}
      />
    </div>
  );
};

export default SearchBasicInfoTab;
