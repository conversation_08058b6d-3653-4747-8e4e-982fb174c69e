import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getSupplierReturnReceiptColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 120 },
  { field: 'invoiceStatus', headerName: 'Trạng thái HĐĐT', width: 150 },
  {
    field: 'receiptNumber',
    headerName: 'Số c/từ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'receiptDate', headerName: 'Ngày c/từ', width: 120, type: 'date' },
  { field: 'customerCode', headerName: 'Mã khách hàng', width: 150 },
  { field: 'customerName', headerName: 'Tên khách hàng', width: 200 },
  { field: 'description', headerName: 'Diễn giải', width: 200 },
  { field: 'account', headerName: 'Tài khoản', width: 120 },
  { field: 'totalAmount', headerName: 'Tổng tiền', width: 120, type: 'number' },
  { field: 'foreignCurrency', headerName: 'Ngoại tệ', width: 100 }
];

export const supplierReturnReceiptDetailColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unit', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 120 },
  { field: 'quantity', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'priceWithUnit', headerName: 'Giá %s', width: 120 },
  { field: 'amountWithUnit', headerName: 'Tiền %s', width: 120 },
  { field: 'taxWithUnit', headerName: 'Thuế %s', width: 120 },
  { field: 'creditAccount', headerName: 'Tk có', width: 120 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'task', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 150 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 150 },
  { field: 'fee', headerName: 'Phí', width: 120, type: 'number' },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'productionOrder', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'invalidExpense', headerName: 'C/p không h/lệ', width: 150 },
  { field: 'price', headerName: 'Giá', width: 120, type: 'number' },
  { field: 'amount', headerName: 'Tiền', width: 120, type: 'number' },
  { field: 'tax', headerName: 'Thuế', width: 120, type: 'number' },
  { field: 'purchaseInvoiceDomestic', headerName: 'Số hóa đơn mua trong nước', width: 180 },
  { field: 'purchaseInvoiceImport', headerName: 'Số hóa đơn mua nhập khẩu', width: 180 },
  { field: 'invoiceLine', headerName: 'Dòng HĐ', width: 100 },
  { field: 'receiptNumber', headerName: 'Số phiếu nhập', width: 150 },
  { field: 'receiptLine', headerName: 'Dòng', width: 80 }
];

export const supplierReturnReceiptItemColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unit', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 120 },
  { field: 'quantity', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'priceVND', headerName: 'Giá VND', width: 120, type: 'number' },
  { field: 'amountVND', headerName: 'Tiền VND', width: 120, type: 'number' },
  { field: 'taxVND', headerName: 'Thuế VND', width: 120, type: 'number' },
  { field: 'creditAccount', headerName: 'Tk có', width: 120 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'task', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 150 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 150 },
  { field: 'fee', headerName: 'Phí', width: 120, type: 'number' },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'productionOrder', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'invalidExpense', headerName: 'C/p không h/lệ', width: 150 },
  { field: 'purchaseInvoiceDomestic', headerName: 'Số hóa đơn mua trong nước', width: 180 },
  { field: 'purchaseInvoiceImport', headerName: 'Số hóa đơn mua nhập khẩu', width: 180 },
  { field: 'invoiceLine', headerName: 'Dòng HĐ', width: 100 }
];

export const hoaDonMuaHangItemColumns: GridColDef[] = [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 120 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'chon', headerName: 'Chọn', width: 80 },
  { field: 'sl_lay', headerName: 'Sl lấy', width: 120, type: 'number' },
  { field: 'so_luong', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'sl_con_lai', headerName: 'Sl còn lại', width: 120, type: 'number' },
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ma_lo', headerName: 'Mã lô', width: 120 },
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 120 },
  { field: 'tk_kho', headerName: 'Tk kho', width: 120 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 120 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 120 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 150 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 120, type: 'number' },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 120 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hop_le', headerName: 'C/p không h/lệ', width: 150 }
];

export const SoChungTuSearchColDef: GridColDef[] = [
  { field: 'ma_quyen', headerName: 'Mã quyển', flex: 1 },
  { field: 'ten_quyen', headerName: 'Tên quyển', flex: 2 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', flex: 1 }
];

export const MaNhapXuatSearchColDef: GridColDef[] = [
  { field: 'ma_ly_do', headerName: 'Mã lý do', flex: 1 },
  { field: 'ten_ly_do', headerName: 'Tên lý do nhập xuất', flex: 2 }
];

export const TaiKhoanNoSearchColDef: GridColDef[] = [
  { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'tai_khoan_so_cai', headerName: 'Tk sổ cái', flex: 1 },
  { field: 'tai_khoan_chi_tiet', headerName: 'Tk chi tiết', flex: 1 },
  { field: 'ban_tai_khoan', headerName: 'Bậc tk', flex: 1 }
];

export const NhaCungCapSearchColDef: GridColDef[] = [
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'so_dien_thoai', headerName: 'Số điện thoại', flex: 1 }
];

export const CucThueSearchColDef: GridColDef[] = [
  { field: 'code', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'supplier_name', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ phải thu', flex: 1 },
  { field: 'tax_code', headerName: 'Công nợ phải trả', flex: 1 },
  { field: 'address', headerName: 'Mã số thuế', flex: 1 },
  { field: 'employee', headerName: 'email', flex: 1 },
  { field: 'phone', headerName: 'Điện thoại', flex: 1 }
];

export const TaiKhoanThueSearchColDef: GridColDef[] = [
  { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'tai_khoan_so_cai', headerName: 'Tk sổ cái', flex: 1 },
  { field: 'tai_khoan_chi_tiet', headerName: 'Tk chi tiết', flex: 1 },
  { field: 'ban_tai_khoan', headerName: 'Bậc tk', flex: 1 }
];

export const MaThueSearchCol: GridColDef[] = [
  { field: 'ma_thue', headerName: 'Mã thuế', width: 120 },
  { field: 'ten_thue', headerName: 'Tên thuế', width: 200 }
];

export const SoHoaDonSearchColDef: GridColDef[] = [
  { field: 'so_chung_tu', headerName: 'Số c/từ', width: 120 },
  { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 120 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 120 },
  { field: 'tong_tien', headerName: 'Tổng tiền', width: 120 }
];
