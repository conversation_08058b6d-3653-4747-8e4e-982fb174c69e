import { CucThueSearchColDef, Ma<PERSON><PERSON>eSearchCol, Tai<PERSON>hoanThueSearchColDef } from '../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

export const TaxTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='px-4'>
    <div className='flex flex-col justify-between lg:flex-row'>
      {/* Left column */}
      <div className='w-2/3 space-y-1'>
        {/* Mẫu báo cáo */}
        <FormField
          label='Mẫu báo cáo'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='reportTemplate'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 3, label: '3. Ho<PERSON> đơn giá trị gia tăng' },
            { value: 4, label: '4. <PERSON><PERSON>ng hoá, dịch vụ mua vào không có hoá đơn' },
            { value: 5, label: '5. Hoá đơn bán hàng thông thường' }
          ]}
        />

        {/* Mã tính chất */}
        <FormField
          label='Mã tính chất'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='natureCode'
          type='select'
          disabled={formMode === 'view'}
          options={[
            {
              value: 1,
              label:
                '1. Hàng hóa, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hóa, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế'
            },
            {
              value: 2,
              label: '2. Hàng hóa, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT'
            },
            {
              value: 3,
              label: '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế'
            },
            {
              value: 4,
              label: '4. Hàng hóa, dịch vụ không đủ điều kiện khấu trừ'
            },
            {
              value: 5,
              label: '5. Hàng hóa, dịch vụ không phải tổng hợp trên tờ khai 01/GTGT'
            }
          ]}
        />

        {/* Mã thuế */}

        <div className='flex items-center gap-x-1'>
          <Label className='min-w-[160px]'>Mã thuế</Label>
          <div className='w-full'>
            <SearchField
              className='w-full'
              disabled={formMode === 'view'}
              searchEndpoint={`/erp/${QUERY_KEYS.THUE}/`}
              searchColumns={MaThueSearchCol}
              displayRelatedField='ten_thue'
              columnDisplay='ma_thue'
            />
          </div>
        </div>

        {/* TK thuế */}

        <div className='flex items-center gap-x-1'>
          <Label className='min-w-[160px]'>TK thuế</Label>
          <div className='w-full'>
            <SearchField
              className='w-full'
              disabled={formMode === 'view'}
              searchEndpoint={`/erp/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={TaiKhoanThueSearchColDef}
              displayRelatedField='ten_tai_khoan'
              columnDisplay='ma_tai_khoan'
            />
          </div>
        </div>
        {/* Cục thuế */}

        <div className='flex items-center gap-x-1'>
          <Label className='min-w-[160px]'>Cục thuế</Label>
          <div className='w-full'>
            <SearchField
              className='w-full'
              disabled={formMode === 'view'}
              searchEndpoint={`/erp/${QUERY_KEYS.CO_QUAN_THUE}/`}
              searchColumns={CucThueSearchColDef}
              displayRelatedField='supplier_name'
              columnDisplay='code'
            />
          </div>
        </div>

        {/* Nhóm sản phẩm */}
        <FormField
          label='Nhóm sản phẩm'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='productGroup'
          type='text'
          disabled={formMode === 'view'}
        />

        {/* Ghi chú */}
        <FormField
          label='Ghi chú'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='note'
          type='text'
          disabled={formMode === 'view'}
        />

        {/* Kê thuế đầu ra */}
        <div className='flex items-center gap-x-1'>
          <div className='mt-3 flex min-w-[160px] shrink-0 items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
            Kê thuế đầu ra
          </div>
          <FormField
            className='flex items-center gap-x-1'
            name='ke_thue_dau_Ra'
            type='checkbox'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  </div>
);
