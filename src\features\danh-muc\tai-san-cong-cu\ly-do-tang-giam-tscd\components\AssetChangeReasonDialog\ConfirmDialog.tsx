import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
}

export default function ConfirmDialog({ open, onClose, onConfirm, title, message }: ConfirmDialogProps) {
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='sm'
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' color='primary' onClick={onConfirm} className='bg-teal-600 hover:bg-teal-700'>
            Xác nhận
          </Button>
          <Button variant='outlined' color='primary' onClick={onClose}>
            Hủy
          </Button>
        </>
      }
    >
      <div className='p-4'>
        <p>{message}</p>
      </div>
    </AritoDialog>
  );
}
