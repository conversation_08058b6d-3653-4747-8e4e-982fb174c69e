'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import Split from 'react-split';
import { useRowSelection, useDialogState, useVatTuSanPhamIntegration } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, DeleteDialog } from './components';
import { itemColumns } from './cols-definition';

export default function VatTuSanPhamPage() {
  const router = useRouter();
  const pathname = usePathname();
  const [showDelete, setShowDelete] = useState(false);
  const [isCopyMode, setIsCopyMode] = useState(false);

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const { showForm, formMode, currentObj, handleCloseForm, handleOpenEditForm, handleOpenViewForm, handleOpenAddForm } =
    useDialogState();
  const {
    vatTuSanPhams,
    addVatTuSanPham,
    updateVatTuSanPham,
    deleteVatTuSanPham,
    copyVatTuSanPham,
    refreshVatTuSanPhams
  } = useVatTuSanPhamIntegration();

  // Load data when component mounts
  useEffect(() => {
    refreshVatTuSanPhams();
  }, [refreshVatTuSanPhams]);

  const handleFormSubmit = (data: any) => {
    // NOTE: Chỉnh lại những dữ liệu default do chưa có dữ liệu thực tế ở db
    // Start with only the required fields
    const enhancedData = {
      // Add empty array for dvt_phu (required by API)
      dvt_phu: [],
      ma_vt: data.ma_vt,
      ten_vt: data.ten_vt,
      ma_lvt: data.ma_lvt,
      dvt: data.dvt,
      ton_kho_yn: data.ton_kho_yn !== undefined ? data.ton_kho_yn : false,
      lo_yn: data.lo_yn !== undefined ? data.lo_yn : false,
      qc_yn: data.qc_yn !== undefined ? data.qc_yn : false,
      status: data.status !== undefined ? data.status : 1,
      ma_thue: data.ma_thue,
      tk_vt: data.tk_vt,
      tk_dt: data.tk_dt,
      tk_gv: data.tk_gv,
      sua_tk_vt: false,
      the_tich: data.the_tich || 0,
      khoi_luong: data.khoi_luong || 0,
      ma_vt2: data.ma_vt2 || '',
      kieu_hd: data.kieu_hd || 1,
      sl_min: data.sl_min || 0,
      sl_max: data.sl_max || 0,
      cach_tinh_gia_ton_kho: data.cach_tinh_gia_ton_kho || 1,
      // Add other fields from data that have values
      ...(data.ten_vt2 ? { ten_vt2: data.ten_vt2 } : {}),
      // Only include nh_vt1 if it has a valid value
      ...(data.nh_vt1 ? { nh_vt1: data.nh_vt1 } : {}),
      ...(data.nh_vt2 ? { nh_vt2: data.nh_vt2 } : {}),
      ...(data.nh_vt3 ? { nh_vt3: data.nh_vt3 } : {}),
      ...(data.ma_kho ? { ma_kho: data.ma_kho } : {}),
      ...(data.ma_vi_tri ? { ma_vi_tri: data.ma_vi_tri } : {}),
      ...(data.ma_thue_nk ? { ma_thue_nk: data.ma_thue_nk } : {}),
      ...(data.tk_ck ? { tk_ck: data.tk_ck } : {}),
      ...(data.tk_km ? { tk_km: data.tk_km } : {}),
      ...(data.tk_tl ? { tk_tl: data.tk_tl } : {}),
      ...(data.tk_spdd ? { tk_spdd: data.tk_spdd } : {}),
      ...(data.tk_cpnvl ? { tk_cpnvl: data.tk_cpnvl } : {}),
      ...(data.ghi_chu ? { ghi_chu: data.ghi_chu } : {})
    };

    if (formMode === 'add' && !isCopyMode) {
      addVatTuSanPham(enhancedData)
        .then(() => {
          handleCloseForm();
          clearSelection();
        })
        .catch(error => {
          console.error('Error adding vat tu san pham:', error);
        });
    } else if (formMode === 'add' && isCopyMode && selectedObj) {
      // Handle copy operation
      copyVatTuSanPham(enhancedData)
        .then(() => {
          handleCloseForm();
          clearSelection();
          setIsCopyMode(false);
        })
        .catch(error => {
          console.error('Error copying vat tu san pham:', error);
        });
    } else if (formMode === 'edit' && selectedObj) {
      console.log('Editing object:', selectedObj);

      // Mỗi object từ API đều có uuid, chỉ cần sử dụng nó trực tiếp
      // For edit operations, make sure to include the UUID
      const editData = {
        ...enhancedData,
        uuid: selectedObj.uuid // Include the UUID in the data
      };

      console.log('Update data:', editData);

      updateVatTuSanPham(selectedObj.uuid, editData)
        .then(() => {
          handleCloseForm();
          clearSelection();
        })
        .catch(error => {
          console.error('Error updating vat tu san pham:', error);
        });
    }
  };

  const handleDeleteClick = () => {
    setShowDelete(true);
  };

  const handleDeleteConfirm = () => {
    if (selectedObj) {
      console.log('Deleting object:', selectedObj);

      // Mỗi object từ API đều có uuid, chỉ cần sử dụng nó trực tiếp
      deleteVatTuSanPham(selectedObj.uuid)
        .then(() => {
          setShowDelete(false);
          clearSelection();
        })
        .catch(error => {
          console.error('Error deleting vat tu san pham:', error);
        });
    } else {
      console.error('No object selected for deletion');
    }
  };

  const handleCloseDelete = () => {
    setShowDelete(false);
  };

  const handleCopyClick = () => {
    if (selectedObj) {
      setIsCopyMode(true);
      handleOpenAddForm();
    }
  };

  const handleTabChange = (tabPath: string) => {
    if (tabPath === pathname) return;
    router.push(`/danh-muc/vat-tu-san-pham-va-kho/${tabPath}`);
  };

  // Transform data for display
  const transformedRows = vatTuSanPhams.map(item => {
    // Log the item to see its structure
    console.log('Item in transformedRows:', item);

    // Mỗi item từ API đều có uuid, chỉ cần sử dụng nó trực tiếp
    // Ensure each row has an id for DataGrid
    return {
      ...item,
      id: (item as any).uuid, // Use uuid as id
      checkbox: false // Default value for checkbox column
    };
  });

  const tables = [
    {
      name: 'Vật tư, sản phẩm',
      rows: transformedRows,
      columns: itemColumns,
      value: 0
    },
    {
      name: 'Lô hàng',
      rows: [],
      columns: [],
      value: 1
    },
    {
      name: 'Kho hàng',
      rows: [],
      columns: [],
      value: 2
    },
    {
      name: 'Vị trí kho hàng',
      rows: [],
      columns: [],
      value: 3
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          formMode={formMode}
          initialData={isCopyMode && selectedObj ? selectedObj : currentObj}
          onSubmit={handleFormSubmit}
          onClose={() => {
            handleCloseForm();
            setIsCopyMode(false);
          }}
          onAdd={handleOpenAddForm}
          onEdit={() => selectedObj && handleOpenEditForm(selectedObj)}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      <DeleteDialog open={showDelete} onClose={handleCloseDelete} onConfirm={handleDeleteConfirm} />

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={handleDeleteClick}
            onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
            onCopyClick={handleCopyClick}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                defaultTabIndex={0}
                onTabChange={(_: any, value: number) => {
                  const tabPaths = ['vat-tu-san-pham', 'lo-hang', 'kho-hang', 'vi-tri-kho-hang'];
                  handleTabChange(tabPaths[value]);
                }}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
