import { khachHangSearchColumns, quyenChungTuSearchColumns } from '@/constants/search-columns';
import { FormField, SearchField, UnitDropdown } from '@/components/custom/arito';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, QuyenChungTu } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';
import { useNgoaiTe } from '@/hooks';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  const { currencies } = useNgoaiTe();

  const currencyOptions = currencies.map(currency => ({
    value: currency.uuid,
    label: currency.ma_nt
  }));
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-[60fr,40fr] lg:space-y-0'>
        {/* Cột trái */}
        {/* C<PERSON>t trái */}
        <div className='space-y-2'>
          <FormField
            className='grid w-full grid-cols-[120px,1fr] items-center'
            type='select'
            label='Giao dịch'
            name='ma_gd'
            disabled={formMode === 'view'}
            options={[
              { value: 'NB', label: 'NB. Xuất nội bộ' },
              { value: 'DC', label: 'DC. Điều chỉnh' },
              { value: 'XK', label: 'SK. Xuất khác' },
              { value: 'X1', label: 'X1. Xuất nguyên vật liệu' },
              { value: 'TS', label: 'TS. Mua tài sản/công cụ' },
              { value: 'XH', label: 'XH. Xuất huỷ' }
            ]}
          />
          <div className='grid w-full grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm text-gray-700'>Mã đối tượng</Label>
            <SearchField<KhachHang>
              name='ma_kh'
              type='text'
              disabled={formMode === 'view'}
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={khachHangSearchColumns}
              dialogTitle='Danh mục tài khoản'
            />
          </div>
          <FormField
            className='grid w-full grid-cols-[120px,1fr] items-center'
            type='text'
            label='Diễn giải'
            name='dien_giai'
            disabled={formMode === 'view'}
          />
        </div>
        {/* Cột phải */}
        <div className='col-span-2 space-y-2 lg:col-span-1'>
          <div className='flex w-full items-center'>
            <Label className='w-32 min-w-32'>Số chứng từ</Label>
            <SearchField<QuyenChungTu>
              className='flex-1'
              type='text'
              disabled={formMode === 'view'}
              columnDisplay='ma_nk'
              searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
              searchColumns={quyenChungTuSearchColumns}
              dialogTitle='Danh mục chứng từ'
            />
          </div>
          <FormField
            label='Ngày chứng từ'
            className='grid grid-cols-[160px,1fr] items-center'
            name='ngay_ct'
            type='date'
            disabled={formMode === 'view'}
          />
          <div className='grid grid-cols-1 gap-y-2 lg:grid-cols-2 lg:gap-x-6'>
            {/* Ngoại tệ */}
            <div className='grid grid-cols-[120px,1fr] items-center'>
              <Label>Ngoại tệ</Label>
              <FormField
                name='ma_nt'
                type='select'
                disabled={formMode === 'view'}
                options={currencyOptions}
                className='w-full'
              />
            </div>

            {/* Tỷ giá */}
            <FormField
              label='Tỷ giá'
              name='ty_gia'
              type='number'
              disabled={formMode === 'view'}
              className='grid grid-cols-[120px,1fr] items-center'
            />
          </div>
          <UnitDropdown formMode={formMode} selectClassName='flex-1' labelClassName='w-32' />
          <FormField
            label='Trạng thái'
            className='items-center'
            name='status'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { value: 'create', label: 'Lập chứng từ' },
              { value: 'pending', label: 'Chờ duyệt' },
              { value: 'approved', label: 'Chuyển vào SC' }
            ]}
          />
          <div className='pl-[160px] lg:pl-[60px]'>
            <FormField
              type='checkbox'
              label='Chuyển dữ liệu'
              name='transfer_yn'
              disabled={formMode === 'view'}
              className='flex items-center'
            />
          </div>
        </div>
      </div>
    </div>
  );
};
