'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  expenseSchema,
  orderDetailSchema,
  orderSchema
} from '../../../../features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/schemas';
import {
  getOrderColumns,
  orderDetailColumns
} from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/cols-definition';
import { MoreInformationTab } from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/components/MoreInformationTab';
import { BasicInfoTab } from '../../../../features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/components/BasicInfoTab';
import { LineItemsTab } from '../../../../features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/components/LineItemsTab';
import { ExpensesTab } from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/components/ExpensesTab';
import { ExpressTab } from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/components/ExpressTab';
import { ActionBar } from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/components/ActionBar';
import { BottomBar } from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/components/BottomBar';
import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';

// Mock data for initial state
const initialOrderRows: any[] = [
  {
    id: 1,
    status: 'Chờ duyệt',
    statusHDDT: 'Đã xuất',
    orderNumber: 'ORD001',
    date: '2024-05-01',
    supplierCode: 'NCC001',
    supplierName: 'Công ty ABC',
    departmentCode: 'BP001',
    description: 'Đơn hàng tháng 5',
    totalAmount: 5000000,
    foreignCurrency: '',
    type: 'Trong nước',
    createdBy: 'User1',
    createdAt: '2024-05-01 08:00',
    updatedBy: '',
    updatedAt: '',
    orderItems: [
      {
        id: 1,
        productCode: 'SP001',
        productName: 'Sản phẩm A',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 10,
        price: 100000,
        amount: 1000000,
        taxCode: 'VAT10',
        taxRate: 10,
        taxAmount: 100000
      }
      // ... other order details
    ]
  }
  // ... other orders
];

export default function Page() {
  // Form state
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [orderRows, setOrderRows] = useState<any[]>(initialOrderRows);
  //Add and Edit
  const [inputOrderDetail, setInputOrderDetail] = useState<any[]>([]);
  const [inputExpenseDetail, setInputExpenseDetail] = useState<any[]>([]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.orderDetails) {
      setInputOrderDetail([...obj.orderDetails]);
    } else {
      setInputOrderDetail([]);
    }
    if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.orderDetails) {
      setInputOrderDetail([...obj.orderDetails]);
    } else {
      setInputOrderDetail([]);
    }
    if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputOrderDetail([]);
    setInputExpenseDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    // console.log(inputOrderDetail);
    // console.log(inputExpenseDetail);
    console.log(data);
    try {
      // if (inputOrderDetail.length === 0) {
      //   Swal.fire({
      //     icon: "error",
      //     title: "Lỗi nhập liệu",
      //     html: `<p class="text-[15px]">Bạn chưa nhập chi tiết đơn hàng</p>`,
      //   });
      //   return;
      // }

      // Validate order detail
      for (const row of inputOrderDetail) {
        await orderDetailSchema.validate(row);
      }

      for (const row of inputExpenseDetail) {
        await expenseSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...orderRows.map(row => row.id || 0)) + 1;
        const newOrder = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          statusHDDT: 'Chưa xuất',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          orderDetails: inputOrderDetail
        };

        setOrderRows([...orderRows, newOrder]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = orderRows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              orderDetails: inputOrderDetail
            };
          }
          return row;
        });

        setOrderRows(updatedRows);
      }

      setShowForm(false);
      setInputOrderDetail([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const convertToDetails = (order: any) => {
    const details = order.orderItems.map((item: any) => ({
      ...item,
      productCode: item.productCode,
      productName: item.productName,
      unit: item.unit,
      warehouseCode: item.warehouseCode
    }));
    return details;
  };

  const handleRowClick = (params: GridRowParams) => {
    let order = params.row as any;
    if (order.orderItems) {
      const details = convertToDetails(order);
      order.orderDetails = details;
    }

    console.log(order);

    setSelectedObj(order);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: orderRows,
      columns: getOrderColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Lập chứng từ',
      rows: orderRows.filter(row => row.status === 'Lập chứng từ'),
      columns: getOrderColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: orderRows.filter(row => row.status === 'Chờ duyệt'),
      columns: getOrderColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Đã duyệt',
      rows: orderRows.filter(row => row.statusHDDT === 'Đã duyệt'),
      columns: getOrderColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#3B82F6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Khác',
      rows: orderRows,
      columns: getOrderColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#000000' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  const from = (
    <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Phiếu nhu cầu vật tư'
      >
        Phiếu nhu cầu vật tư
      </button>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Chọn nhà cung cấp'
      >
        Chọn nhà cung cấp
      </button>
    </div>
  );

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'lineItems',
                label: 'Chi tiết',
                component: <LineItemsTab formMode={formMode} columns={orderDetailColumns} />
              },
              {
                id: 'expenses',
                label: 'Chi phí',
                component: (
                  <ExpensesTab value={inputExpenseDetail} onChange={setInputExpenseDetail} formMode={formMode} />
                )
              },
              {
                id: 'express',
                label: 'Nhà cung cấp & giao hàng',
                component: <ExpressTab formMode={formMode} />
              },
              {
                id: 'more',
                label: 'Khác',
                component: <MoreInformationTab formMode={formMode} />
              },
              {
                id: 'filekem',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Đơn hàng mua trong nước'}
            from={from}
            bottomBar={<BottomBar totalQuantity={0} totalPayment={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable value={selectedObj?.orderDetails || []} columns={orderDetailColumns} mode={formMode} />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
