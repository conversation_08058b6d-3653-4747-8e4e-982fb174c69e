'use client';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Columns, feeColumns } from '../../../cols-definition';
import { Label } from '@/components/ui/label';
import { Bo<PERSON><PERSON> } from '@/types/schemas';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
  boPhan: BoPhan | null;
  setBoPhan: (boPhan: <PERSON>Phan) => void;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode, boPhan, setBoPhan }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        {/* 1. Mã bộ phận */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã bộ phận</Label>
          <FormField type='text' name='ma_bp' className='w-64' disabled={formMode === 'view'} />
        </div>
        {/* 2. Tên bộ phận */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên bộ phận</Label>
          <FormField type='text' name='ten_bp' className='w-96' disabled={formMode === 'view'} />
        </div>
        {/* 3. Tên khác */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên khác</Label>
          <FormField type='text' name='ten_bp2' className='w-96' disabled={formMode === 'view'} />
        </div>
        {/* 4. Mã bộ phận phí */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã bộ phận phí</Label>
          <SearchField<BoPhan>
            type='text'
            displayRelatedField='ten_bp'
            columnDisplay='ma_bp'
            className='w-64'
            dialogTitle='Danh mục bộ phận'
            searchColumns={feeColumns}
            searchEndpoint='/departments/'
            value={boPhan?.ma_bp || ''}
            onRowSelection={setBoPhan}
            disabled={formMode === 'view'}
          />
        </div>

        {/* Ghi chú */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ghi chú</Label>
          <FormField type='text' name='ghi_chu' className='w-96' disabled={formMode === 'view'} />
        </div>
        {/* 5. Trạng thái */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Trạng thái</Label>
          <div className='w-64'>
            <FormField
              name='status'
              type='select'
              options={[
                { value: '1', label: '1. Còn sử dụng' },
                { value: '0', label: '0. Không sử dụng' }
              ]}
              disabled={formMode === 'view'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
