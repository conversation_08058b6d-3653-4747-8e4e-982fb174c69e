import { z } from 'zod';

/**
 * Schema for VatTuSanPham (Product/Material) form data
 *
 * This schema defines the validation rules for the VatTuSanPham form.
 * It is based on the VatTuSanPhamInput type from vat-tu-san-pham.type.ts.
 */
export const vatTuSanPhamSchema = z.object({
  // Basic information
  ma_vt: z.string().min(1, 'Mã vật tư không được để trống'),
  ten_vt: z.string().min(1, 'Tên vật tư không được để trống'),
  ten_vt2: z.string().optional().nullable(),
  dvt: z.string().min(1, 'Đơn vị tính không được để trống'),

  // Classification
  ma_lvt: z.string().min(1, 'Loại vật tư không được để trống'),
  nh_vt1: z.string().optional().nullable(), // Make nh_vt1 optional
  nh_vt2: z.string().optional().nullable(),
  nh_vt3: z.string().optional().nullable(),

  // Inventory tracking
  ton_kho_yn: z.boolean().default(true),
  lo_yn: z.boolean().default(false),
  qc_yn: z.boolean().default(false),

  // Warehouse information
  ma_kho: z.string().optional().nullable(),
  ma_vi_tri: z.string().optional().nullable(),

  // Tax information
  ma_thue: z.string().min(1, 'Mã thuế không được để trống'),
  ma_thue_nk: z.string().optional().nullable(),

  // Barcode and accounts
  ma_barcode: z.string().optional().nullable(),
  tk_vt: z.string().min(1, 'Tài khoản vật tư không được để trống'),
  sua_tk_vt: z.boolean().default(false),
  tk_dt: z.string().min(1, 'Tài khoản doanh thu không được để trống'),
  tk_gv: z.string().min(1, 'Tài khoản giá vốn không được để trống'),
  tk_ck: z.string().optional().nullable(),
  tk_km: z.string().optional().nullable(),
  tk_tl: z.string().optional().nullable(),
  // Add validation for required accounting fields
  tk_spdd: z.string().optional().nullable(),
  tk_cpnvl: z.string().optional().nullable(),

  // Physical attributes
  qc_data: z.string().optional().nullable(),
  qc_sd: z.string().optional().nullable(),
  image_id: z.string().optional().nullable(),
  the_tich: z.number().default(0),
  khoi_luong: z.number().default(0),

  // Origin and characteristics
  nuoc_sx: z.string().optional().nullable(),
  mau_sac: z.string().optional().nullable(),
  kich_co: z.string().optional().nullable(),
  ma_vt2: z.string().default(''),
  kieu_hd: z.union([z.number(), z.string().transform(val => parseInt(val, 10))]).default(1),

  // Department
  ma_bp: z.string().optional().nullable(),

  // Quantity limits
  sl_min: z.number().default(0),
  sl_max: z.number().default(0),

  // Dates and status
  ngay_nhap: z.string().optional().nullable(),
  ngay_xuat: z.string().optional().nullable(),
  ghi_chu: z.string().optional().nullable(),
  status: z.union([z.number(), z.string().transform(val => parseInt(val, 10))]).default(1),

  // Secondary units of measure
  dvt_phu: z
    .array(
      z.object({
        ma_dvt: z.string().min(1, 'Mã đơn vị tính không được để trống'),
        he_so: z.number().min(0.000001, 'Hệ số phải lớn hơn 0')
      })
    )
    .optional()
    .default([])
});

export type VatTuSanPhamFormValues = z.infer<typeof vatTuSanPhamSchema>;

/**
 * Initial values for the VatTuSanPham form
 */
export const initialVatTuSanPhamValues: VatTuSanPhamFormValues = {
  ma_vt: '',
  ten_vt: '',
  ten_vt2: '',
  dvt: '',
  ma_lvt: '',
  nh_vt1: '',
  nh_vt2: '',
  nh_vt3: '',
  ton_kho_yn: true,
  lo_yn: false,
  qc_yn: false,
  ma_kho: '',
  ma_vi_tri: '',
  ma_thue: '',
  ma_thue_nk: '',
  ma_barcode: '',
  tk_vt: '',
  sua_tk_vt: false,
  tk_dt: '',
  tk_gv: '',
  tk_ck: '',
  tk_km: '',
  tk_tl: '',
  tk_spdd: '',
  tk_cpnvl: '',
  qc_data: '',
  qc_sd: '',
  image_id: '',
  the_tich: 0,
  khoi_luong: 0,
  nuoc_sx: '',
  mau_sac: '',
  kich_co: '',
  ma_vt2: '',
  kieu_hd: 1,
  ma_bp: '',
  sl_min: 0,
  sl_max: 0,
  ngay_nhap: null,
  ngay_xuat: null,
  ghi_chu: '',
  status: 1,
  dvt_phu: []
};

/**
 * Format data from API response to form values
 */
export const formatDataFromApi = (data: any): VatTuSanPhamFormValues & { uuid?: string } => {
  // Tạo một đối tượng cơ bản với các trường từ VatTuSanPhamFormValues
  const formattedData: VatTuSanPhamFormValues & { uuid?: string } = {
    ma_vt: data.ma_vt || '',
    ten_vt: data.ten_vt || '',
    ten_vt2: data.ten_vt2 || '',
    dvt: data.dvt || '',
    ma_lvt: data.ma_lvt || '',
    nh_vt1: data.nh_vt1 || '',
    nh_vt2: data.nh_vt2 || '',
    nh_vt3: data.nh_vt3 || '',
    ton_kho_yn: data.ton_kho_yn || false,
    lo_yn: data.lo_yn || false,
    qc_yn: data.qc_yn || false,
    ma_kho: data.ma_kho || '',
    ma_vi_tri: data.ma_vi_tri || '',
    ma_thue: data.ma_thue || '',
    ma_thue_nk: data.ma_thue_nk || '',
    ma_barcode: data.ma_barcode || '',
    tk_vt: data.tk_vt || '',
    sua_tk_vt: data.sua_tk_vt || false,
    tk_dt: data.tk_dt || '',
    tk_gv: data.tk_gv || '',
    tk_ck: data.tk_ck || '',
    tk_km: data.tk_km || '',
    tk_tl: data.tk_tl || '',
    tk_spdd: data.tk_spdd || '',
    tk_cpnvl: data.tk_cpnvl || '',
    qc_data: data.qc_data || '',
    qc_sd: data.qc_sd || '',
    image_id: data.image_id || '',
    the_tich: data.the_tich || 0,
    khoi_luong: data.khoi_luong || 0,
    nuoc_sx: data.nuoc_sx || '',
    mau_sac: data.mau_sac || '',
    kich_co: data.kich_co || '',
    ma_vt2: data.ma_vt2 || '',
    kieu_hd: data.kieu_hd || 1,
    ma_bp: data.ma_bp || '',
    sl_min: data.sl_min || 0,
    sl_max: data.sl_max || 0,
    ngay_nhap: data.ngay_nhap || null,
    ngay_xuat: data.ngay_xuat || null,
    ghi_chu: data.ghi_chu || '',
    status: data.status || 1,
    dvt_phu: Array.isArray(data.dvt)
      ? data.dvt.map((item: any) => ({
          ma_dvt: item.ma_dvt || '',
          he_so: item.he_so || 1
        }))
      : []
  };

  // Thêm trường uuid nếu có trong dữ liệu gốc
  if (data.uuid) {
    formattedData.uuid = data.uuid;
  }

  return formattedData;
};

/**
 * Format form values to API request data
 */
export const formatDataForApi = (formValues: VatTuSanPhamFormValues & { uuid?: string }): any => {
  // Create a copy of the form values as any type to allow indexing
  const apiData: any = { ...formValues };

  // Remove any fields with undefined or null values
  Object.keys(apiData).forEach(key => {
    if (apiData[key] === undefined || apiData[key] === null || apiData[key] === '') {
      delete apiData[key];
    }
  });

  // Make sure dvt_phu is an array
  apiData.dvt_phu = Array.isArray(apiData.dvt_phu) ? apiData.dvt_phu : [];

  // Ensure uuid is preserved if it exists
  if (formValues.uuid) {
    apiData.uuid = formValues.uuid;
  }

  return apiData;
};
