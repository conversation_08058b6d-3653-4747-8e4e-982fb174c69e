import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { NgoaiTe } from '@/types/schemas/ngoai-te.type';

interface UseRowSelectionReturn {
  selectedObj: NgoaiTe | null;
  selectedRowIndex: string | null;
  handleRowClick: (params: GridRowParams) => void;
  clearSelection: () => void;
}

const useRowSelection = (): UseRowSelectionReturn => {
  const [selectedObj, setSelectedObj] = useState<NgoaiTe | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const handleRowClick = (params: GridRowParams) => {
    setSelectedObj(params.row as NgoaiTe);
    setSelectedRowIndex(params.id as string);
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection
  };
};

export { useRowSelection };
