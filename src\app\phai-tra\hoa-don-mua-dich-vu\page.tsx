'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  domesticInvoiceDetailSchema,
  domesticInvoiceExpenseSchema
} from '@/features/phai-tra/hoa-don-mua-hang-trong-nuoc/schemas';
import {
  getServiceInvoiceColumns,
  serviceInvoiceDetailColumns
} from '@/features/phai-tra/hoa-don-mua-dich-vu/cols-definition';
import { TaxesTab } from '@/features/phai-tra/hoa-don-mua-hang-trong-nuoc/components/TaxesTab';
import { BasicInfoTab } from '@/features/phai-tra/hoa-don-mua-dich-vu/components/BasicInfoTab';
import { DetailsTab } from '@/features/phai-tra/hoa-don-mua-dich-vu/components/DetailsTab';
import { ActionBar } from '@/features/phai-tra/hoa-don-mua-dich-vu/components/ActionBar';
import { BottomBar } from '@/features/phai-tra/hoa-don-mua-dich-vu/components/BottomBar';
import { serviceInvoiceSchema } from '@/features/phai-tra/hoa-don-mua-dich-vu/schemas';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';

// Mock data for initial state
const initialInvoiceRows: any[] = [
  {
    id: 1,
    status: 'Mới',
    statusHDDT: 'Chưa xuất',
    orderNumber: 'PO001',
    invoiceNumber: 'HĐ00123',
    date: '2023-07-15',
    supplierCode: 'NCC001',
    supplierName: 'Công ty ABC',
    customerCode: 'KH001',
    departmentCode: 'IT',
    description: 'Hóa đơn mua hàng tháng 7/2023',
    accountCredit: '331',
    totalAmount: ********,
    currency: 'VND',
    foreignCurrency: '',
    type: 'Trong nước',
    createdBy: 'admin',
    createdAt: '2023-07-15',
    updatedBy: 'admin',
    updatedAt: '2023-07-15',
    invoiceDetails: [
      {
        id: 'DET001',
        productCode: 'SP001',
        productName: 'Laptop Dell XPS 13',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 2,
        priceVND: ********,
        amountVND: ********,
        preDiscountAmountVND: ********,
        costVND: 1000000,
        discountRate: 5,
        discountVND: 2500000,
        vatTaxCode: 'VAT10',
        vatRate: 10,
        vatTaxAccount: '1331',
        vatAmountVND: 4750000,
        debitAccount: '156',
        netPrice: ********,
        preDiscountTotal: ********,
        totalGoodsAmount: ********,
        totalCost: 1000000,
        totalDiscount: 2500000,
        totalVat: 4750000,
        department: 'IT',
        additionalFee: 250000,
        receiptNumber: 'PN001',
        receiptLine: '1',
        orderNumber: 'PO001',
        orderLine: '1'
      },
      {
        id: 'DET002',
        productCode: 'SP002',
        productName: 'Màn hình Dell 27"',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 3,
        priceVND: 5000000,
        amountVND: ********,
        preDiscountAmountVND: ********,
        costVND: 150000,
        discountRate: 2,
        discountVND: 300000,
        vatTaxCode: 'VAT10',
        vatRate: 10,
        vatTaxAccount: '1331',
        vatAmountVND: 1470000,
        debitAccount: '156',
        netPrice: 4900000,
        preDiscountTotal: ********,
        totalGoodsAmount: ********,
        totalCost: 150000,
        totalDiscount: 300000,
        totalVat: 1470000,
        department: 'IT',
        additionalFee: 150000,
        receiptNumber: 'PN001',
        receiptLine: '2',
        orderNumber: 'PO001',
        orderLine: '2'
      }
    ],
    expenses: [
      {
        id: 'EXP001',
        expenseCode: 'CP001',
        expenseName: 'Phí vận chuyển',
        amount: 1500000,
        distributeMethod: 'Giá trị',
        supplierCode: 'NCC001',
        supplierName: 'Công ty Vận tải XYZ',
        creditAccount: '331'
      },
      {
        id: 'EXP002',
        expenseCode: 'CP002',
        expenseName: 'Phí bảo hiểm',
        amount: 500000,
        distributeMethod: 'Giá trị',
        supplierCode: 'NCC002',
        supplierName: 'Công ty Bảo hiểm ABC',
        creditAccount: '331'
      }
    ],
    expenseDetails: [
      {
        id: 'EXPD001',
        expenseCode: 'CP001',
        productCode: 'SP001',
        amount: 1000000,
        lineDetail: '1'
      },
      {
        id: 'EXPD002',
        expenseCode: 'CP001',
        productCode: 'SP002',
        amount: 500000,
        lineDetail: '2'
      }
    ],
    taxes: [
      {
        id: 'TAX001',
        taxCode: 'VAT10',
        taxRate: 10,
        invoiceNumber: 'HĐ00123',
        invoiceSymbol: 'AA/23E',
        invoiceDate: '2023-07-10',
        invoiceForm: '01GTKT',
        reportForm: 'BC26',
        natureCode: 'NC01',
        supplierCode: 'NCC001',
        supplierName: 'Công ty ABC',
        address: '123 Đường Lê Lợi, Quận 1, TP.HCM',
        taxNumber: '**********',
        productName: 'Laptop Dell XPS 13',
        amountVND: ********,
        taxAccount: '1331',
        taxAmountVND: 4750000,
        taxDepartment: 'TP.HCM',
        paymentCode: 'TT001',
        note: 'Thanh toán đúng hạn',
        department: 'IT',
        task: 'DH001',
        contract: 'HD001',
        paymentPhase: 'Đợt 1',
        agreement: 'KU001',
        fee: 0,
        product: 'SP001',
        productionOrder: 'LSX001'
      },
      {
        id: 'TAX002',
        taxCode: 'VAT10',
        taxRate: 10,
        invoiceNumber: 'HĐ00123',
        invoiceSymbol: 'AA/23E',
        invoiceDate: '2023-07-10',
        invoiceForm: '01GTKT',
        reportForm: 'BC26',
        natureCode: 'NC01',
        supplierCode: 'NCC001',
        supplierName: 'Công ty ABC',
        address: '123 Đường Lê Lợi, Quận 1, TP.HCM',
        taxNumber: '**********',
        productName: 'Màn hình Dell 27"',
        amountVND: ********,
        taxAccount: '1331',
        taxAmountVND: 1470000,
        taxDepartment: 'TP.HCM',
        paymentCode: 'TT001',
        note: 'Thanh toán đúng hạn',
        department: 'IT',
        task: 'DH001',
        contract: 'HD001',
        paymentPhase: 'Đợt 1',
        agreement: 'KU001',
        fee: 0,
        product: 'SP002',
        productionOrder: 'LSX001'
      }
    ]
  }
];

// Helper functions to calculate totals from invoice data
const calculateTotals = (selectedObj: any) => {
  if (!selectedObj) {
    return {
      totalExpense: 0,
      totalTax: 0,
      total: 0,
      totalQuantity: 0,
      totalPayment: 0
    };
  }

  // Initialize result object
  const result = {
    totalExpense: 0,
    totalTax: 0,
    totalQuantity: 0,
    totalPayment: 0,
    total: 0
  };

  // Calculate totals from invoice details
  if (selectedObj.invoiceDetails && selectedObj.invoiceDetails.length > 0) {
    selectedObj.invoiceDetails.forEach((detail: any) => {
      // Total expense from invoice details (additional fees)
      result.totalExpense += detail.additionalFee || 0;

      // Total tax
      result.totalTax += detail.vatAmountVND || detail.totalVat || 0;

      // Total quantity
      result.totalQuantity += detail.quantity || 0;

      // Total goods amount
      const goodsAmount = detail.totalGoodsAmount || detail.amountVND || 0;
      result.totalPayment += goodsAmount;
    });
  }

  // Add expenses to total expense
  if (selectedObj.expenses && selectedObj.expenses.length > 0) {
    selectedObj.expenses.forEach((expense: any) => {
      result.totalExpense += expense.amount || 0;
    });
  }

  // Also check expense details for any additional expenses
  if (selectedObj.expenseDetails && selectedObj.expenseDetails.length > 0) {
    selectedObj.expenseDetails.forEach((expDetail: any) => {
      // If there are any expense amounts in expense details that are not already counted
      // This depends on your business logic
      if (expDetail.amount) {
        // You may need additional logic to avoid double counting
        // For example, checking if this expense is already counted in the expenses array
        // result.totalExpense += (expDetail.amount || 0);
      }
    });
  }

  // Add tax to total payment
  result.totalPayment += result.totalTax;

  // Total = expense + payment (total goods amount + tax)
  result.total = result.totalExpense + result.totalPayment;

  return result;
};

export default function Page() {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [invoiceRows, setInvoiceRows] = useState<any[]>(initialInvoiceRows);
  //Add and Edit
  const [inputInvoiceDetail, setInputInvoiceDetail] = useState<any[]>([]);
  const [inputExpense, setInputExpense] = useState<any[]>([]);
  const [inputExpenseDetail, setInputExpenseDetail] = useState<any[]>([]);
  const [inputTax, setInputTax] = useState<any[]>([]);

  // Helper function to calculate totals from current form state
  const calculateFormTotals = () => {
    // Initialize result object
    const result = {
      totalExpense: 0,
      totalTax: 0,
      totalQuantity: 0,
      totalPayment: 0,
      total: 0
    };

    // Calculate totals from current invoice details in the form
    if (inputInvoiceDetail && inputInvoiceDetail.length > 0) {
      inputInvoiceDetail.forEach((detail: any) => {
        // Total expense from invoice details (additional fees)
        result.totalExpense += Number(detail.additionalFee) || 0;

        // Total tax
        result.totalTax += Number(detail.vatAmountVND) || Number(detail.totalVat) || 0;

        // Total quantity
        result.totalQuantity += Number(detail.quantity) || 0;

        // Total goods amount
        const goodsAmount = Number(detail.totalGoodsAmount) || Number(detail.amountVND) || 0;
        result.totalPayment += goodsAmount;
      });
    }

    // Add expenses to total expense
    if (inputExpense && inputExpense.length > 0) {
      inputExpense.forEach((expense: any) => {
        result.totalExpense += Number(expense.amount) || 0;
      });
    }

    // Also check expense details for any additional expenses
    if (inputExpenseDetail && inputExpenseDetail.length > 0) {
      inputExpenseDetail.forEach((expDetail: any) => {
        // If there are any expense amounts in expense details that are not already counted
        // This depends on your business logic
        if (Number(expDetail.amount)) {
          // You may need additional logic to avoid double counting
          // For example, checking if this expense is already counted in the expenses array
          // result.totalExpense += (expDetail.amount || 0);
        }
      });
    }

    // Add tax to total payment
    result.totalPayment += result.totalTax;

    // Total = expense + payment (total goods amount + tax)
    result.total = result.totalExpense + result.totalPayment;

    return result;
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.invoiceDetails) {
      setInputInvoiceDetail([...obj.invoiceDetails]);
    } else {
      setInputInvoiceDetail([]);
    }
    if (obj.expenseDetails) {
      setInputExpenseDetail([...obj.expenseDetails]);
    } else if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    if (obj.expenses) {
      setInputExpense([...obj.expenses]);
    } else {
      setInputExpense([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.invoiceDetails) {
      setInputInvoiceDetail([...obj.invoiceDetails]);
    } else {
      setInputInvoiceDetail([]);
    }
    if (obj.expenseDetails) {
      setInputExpenseDetail([...obj.expenseDetails]);
    } else if (obj.invoiceExpense) {
      setInputExpenseDetail([...obj.invoiceExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    if (obj.expenses) {
      setInputExpense([...obj.expenses]);
    } else {
      setInputExpense([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputInvoiceDetail([]);
    setInputExpenseDetail([]);
    setInputExpense([]);
    setInputTax([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    console.log(inputInvoiceDetail);
    console.log(inputExpenseDetail);
    console.log(inputExpense);
    console.log(inputTax);
    console.log(data);
    try {
      if (inputInvoiceDetail.length === 0) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi nhập liệu',
          html: `<p class="text-[15px]">Bạn chưa nhập chi tiết đơn hàng</p>`
        });
        return;
      }

      // Validate order detail
      for (const row of inputInvoiceDetail) {
        await domesticInvoiceDetailSchema.validate(row);
      }

      for (const row of inputExpenseDetail) {
        await domesticInvoiceExpenseSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...invoiceRows.map(row => row.id || 0)) + 1;
        const newInvoice = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          statusHDDT: 'Chưa xuất',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          invoiceDetails: inputInvoiceDetail,
          expenses: inputExpense,
          expenseDetails: inputExpenseDetail
        };

        setInvoiceRows([...invoiceRows, newInvoice]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = invoiceRows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              invoiceDetails: inputInvoiceDetail
            };
          }
          return row;
        });

        setInvoiceRows(updatedRows);
      }

      setShowForm(false);
      setInputInvoiceDetail([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const handleRowClick = (params: GridRowParams) => {
    const order = params.row as any;
    setSelectedObj(order);
    setInputExpense(order.expenses);
    setInputExpenseDetail(order.expenseDetails);
    setInputTax(order.taxes);
    setInputInvoiceDetail(order.invoiceDetails);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: invoiceRows,
      columns: getServiceInvoiceColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Lập chứng từ',
      rows: invoiceRows.filter(row => row.status === 'Lập chứng từ'),
      columns: getServiceInvoiceColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: invoiceRows.filter(row => row.status === 'Chờ duyệt'),
      columns: getServiceInvoiceColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chuyển vào SC',
      rows: invoiceRows.filter(row => row.status === 'Chuyển vào SC'),
      columns: getServiceInvoiceColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#3B82F6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  const from = (
    <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Phiếu nhu cầu vật tư'
      >
        Phiếu nhu cầu vật tư
      </button>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Chọn nhà cung cấp'
      >
        Chọn nhà cung cấp
      </button>
    </div>
  );

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'details',
                label: 'Chi tiết',
                component: (
                  <DetailsTab value={inputInvoiceDetail} onChange={setInputInvoiceDetail} formMode={formMode} />
                )
              },
              {
                id: 'taxes',
                label: 'Thuế',
                component: <TaxesTab value={inputTax} onChange={setInputTax} formMode={formMode} />
              },
              {
                id: 'filekem',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Hóa đơn mua dịch vụ'}
            from={from}
            bottomBar={<BottomBar totalQuantity={0} totalTax={0} totalPayment={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.invoiceDetails || []}
                columns={serviceInvoiceDetailColumns}
                mode={formMode}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
