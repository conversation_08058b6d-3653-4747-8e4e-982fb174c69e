import Checkbox from '@mui/material/Checkbox';
import { Button } from '@mui/material';
import React from 'react';
import { CreateEditCustomerGeneralInfoTab, CreateEditCustomerHeaderTab } from '.';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchColumns } from '../../../../cols-definition';
import { AritoIcon } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import ContraintTab from './ContraintTab';
import SearchField from '../..';

interface HeaderTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const HeaderTab: React.FC<HeaderTabProps> = ({ formMode }) => {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-2 md:space-y-2'>
          {/* 1. <PERSON><PERSON><PERSON> */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tài khoản</Label>
            <FormField type='text' name='accountCode' className='w-[200px]' />
          </div>

          {/* 2. Tên tài khoản */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên tài khoản</Label>
            <FormField type='text' name='accountName' className='w-[400px]' />
          </div>

          {/* 3. Tên tiếng Anh */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên tiếng Anh</Label>
            <FormField type='text' name='accountNameEn' className='w-[400px]' />
          </div>

          {/* 4. Tài khoản mẹ */}
          <SearchField
            name='account'
            label='Tài khoản mẹ'
            formMode={formMode}
            searchColumns={SearchColumns}
            defaultSearchColumn='name'
            actionButtons={['add', 'edit']}
            headerFields={<CreateEditCustomerHeaderTab formMode={formMode} />}
            tabs={[
              {
                id: 'general_info_tab',
                label: 'Thông tin chung',
                component: <CreateEditCustomerGeneralInfoTab />
              },
              {
                id: 'contraint_tab',
                label: 'Ràng buộc',
                component: <ContraintTab />
              }
            ]}
          />

          {/* 5. TK đại diện khác */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>TK đại diện khác</Label>
            <FormField type='text' name='otherAccount' className='w-[200px]' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderTab;
