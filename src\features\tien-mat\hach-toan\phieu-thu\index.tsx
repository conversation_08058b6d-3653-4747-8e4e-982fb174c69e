'use client';

import { useState } from 'react';
import Split from 'react-split';
import {
  ExchangeRateTab,
  PaymentInfoTab,
  BasicInfoTab,
  SearchDialog,
  DetailsTab,
  ActionBar,
  BottomBar,
  OtherTab,
  InputTableActionBar
} from './components';
import { AritoForm, AritoDataTables, LoadingOverlay, InputTable } from '@/components/custom/arito';
import { useFormState, useSearchState, useDataTables, useSearchFieldStates } from './hooks';
import { receiptVoucherSchema, receiptVoucherInitialValues } from './schema';
import { receiptVoucherDetailColumns } from './cols-definition';
import { PhieuThu, PhieuThuInput } from '@/types/schemas';
import { useRows } from '@/hooks';

export default function PhieuThuPage() {
  const { selectedObj, handleRowClick, clearSelection } = useRows<PhieuThu>();

  const { addPhieuThu, updatePhieuThu, deletePhieuThu, refreshPhieuThus, tables, isLoading } = useDataTables();

  const { searchDialogOpen, setSearchDialogOpen, setSearchFilters } = useSearchState();

  const searchFieldStates = useSearchFieldStates();

  const {
    showForm,
    setShowForm,
    formMode,
    inputDetails,
    setInputDetails,
    handleFormSubmit,
    handleAddClick,
    handleEditClick,
    handleCopyClick,
    handleDeleteClick,
    handleSearchClick,
    handleSearchClose,
    handleSearchSubmit,
    handleRefreshClick,
    handlePrintClick,
    handleFixedColumnsClick,
    handleMultiPrintClick,
    handleExportDataClick,
    handleDownloadExcelTemplateClick,
    handleImportFromExcelClick
  } = useFormState({
    selectedObj,
    clearSelection,
    addPhieuThu,
    updatePhieuThu,
    deletePhieuThu,
    refreshPhieuThus,
    setSearchDialogOpen,
    setSearchFilters
  });

  const [editExchangeRate, setEditExchangeRate] = useState(false);

  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
        {showForm && (
          <AritoForm<PhieuThuInput>
            title={formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Chỉnh sửa' : 'Xem'}
            subTitle='Phiếu thu tiền'
            mode={formMode}
            initialData={formMode === 'edit' ? selectedObj || undefined : receiptVoucherInitialValues}
            onSubmit={handleFormSubmit}
            schema={receiptVoucherSchema}
            headerFields={<BasicInfoTab formMode={formMode} searchFieldStates={searchFieldStates} />}
            tabs={[
              {
                id: 'details',
                label: 'Chi tiết',
                component: <DetailsTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
              },
              {
                id: 'payment-info',
                label: 'Thông tin thanh toán',
                component: <PaymentInfoTab formMode={formMode} searchFieldStates={searchFieldStates} />
              },
              {
                id: 'exchange-rate',
                label: 'Tỷ giá',
                component: (
                  <ExchangeRateTab
                    formMode={formMode}
                    editExchangeRate={editExchangeRate}
                    onEditExchangeRateChange={setEditExchangeRate}
                  />
                )
              },
              {
                id: 'other',
                label: 'Khác',
                component: <OtherTab formMode={formMode} />
              }
            ]}
            onClose={() => setShowForm(false)}
            bottomBar={
              <BottomBar
                totalAmount={
                  formMode === 'add'
                    ? inputDetails.reduce((sum: number, detail: any) => sum + parseFloat(detail.tien || '0'), 0)
                    : parseFloat(selectedObj?.t_tien || '0')
                }
                formMode={formMode}
              />
            }
          />
        )}

        {!showForm && (
          <>
            <SearchDialog open={searchDialogOpen} onClose={handleSearchClose} onSearch={handleSearchSubmit} />

            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onPrintClick={handlePrintClick}
              onSearchClick={handleSearchClick}
              onRefreshClick={handleRefreshClick}
              onFixedColumnsClick={handleFixedColumnsClick}
              onMultiPrintClick={handleMultiPrintClick}
              onExportDataClick={handleExportDataClick}
              onDownloadExcelTemplateClick={handleDownloadExcelTemplateClick}
              onImportFromExcelClick={handleImportFromExcelClick}
              isEditDisabled={!selectedObj}
              isDeleteDisabled={!selectedObj}
              isCopyDisabled={!selectedObj}
            />
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
              </div>

              <div className='w-full overflow-hidden'>
                <InputTable
                  rows={selectedObj?.child_items || []}
                  columns={receiptVoucherDetailColumns}
                  mode={formMode}
                  getRowId={row => row?.uuid || ''}
                  className='w-full'
                  actionButtons={<InputTableActionBar mode={formMode} />}
                />
              </div>
            </Split>
          </>
        )}
      </div>

      {}
    </>
  );
}
