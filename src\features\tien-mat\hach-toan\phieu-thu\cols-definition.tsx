import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getReceiptVoucherColumns = (): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    headerAlign: 'center'
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 150,
    headerAlign: 'center'
  },
  {
    field: 'ngay_ct',
    headerName: '<PERSON><PERSON>y chứng từ',
    width: 150,
    headerAlign: 'center'
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 150,
    headerAlign: 'center'
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    headerAlign: 'center'
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250,
    headerAlign: 'center'
  },
  {
    field: 'tk',
    headerName: 'T<PERSON><PERSON> khoản nợ',
    width: 120,
    headerAlign: 'center'
  },
  {
    field: 't_tien',
    headerName: 'Tổng tiền',
    width: 150,
    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.value?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 100,
    headerAlign: 'center'
  },
  {
    field: 'ma_ngv',
    headerName: 'Loại phiếu thu',
    width: 150,
    headerAlign: 'center'
  }
];

export const receiptVoucherDetailColumns: GridColDef[] = [
  {
    field: 'description',
    headerName: 'Diễn giải',
    width: 200,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'customerCode',
    headerName: 'Mã đối tượng',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'customerName',
    headerName: 'Tên đối tượng',
    width: 200,
    editable: false,

    headerAlign: 'center'
  },
  {
    field: 'debtBalance',
    headerName: 'Dư công nợ',
    width: 120,
    editable: false,

    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.value?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'invoice',
    headerName: 'Hóa đơn',
    width: 100,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'invoiceNumber',
    headerName: 'Số hóa đơn',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'invoiceDate',
    headerName: 'Ngày hóa đơn',
    width: 120,
    editable: true,
    type: 'date',

    headerAlign: 'center'
  },
  {
    field: 'bank',
    headerName: 'Ngân hàng',
    width: 150,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'creditAccount',
    headerName: 'Tài khoản có',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'foreignCurrency',
    headerName: 'Ngoại tệ',
    width: 100,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'invoiceExchangeRate',
    headerName: 'Tỷ giá hđ',
    width: 100,
    editable: true,
    type: 'number',

    headerAlign: 'center'
  },
  {
    field: 'invoiceAmount',
    headerName: 'Tiền trên hóa đơn',
    width: 150,
    editable: true,
    type: 'number',

    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.value?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'allocatedAmount',
    headerName: 'Đã phân bổ',
    width: 120,
    editable: false,

    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.value?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'remainingAmount',
    headerName: 'Còn lại',
    width: 120,
    editable: false,

    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.value?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'postingExchangeRate',
    headerName: 'Tỷ giá gs',
    width: 100,
    editable: true,
    type: 'number',

    headerAlign: 'center'
  },
  {
    field: 'percentageAmount',
    headerName: 'Tiền %s',
    width: 120,
    editable: true,
    type: 'number',

    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.value?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'amount',
    headerName: 'Tiền',
    width: 150,
    editable: true,
    type: 'number',

    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.value?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'departmentCode',
    headerName: 'Bộ phận',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'caseCode',
    headerName: 'Vụ việc',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'contractCode',
    headerName: 'Hợp đồng',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'paymentPhase',
    headerName: 'Đợt thanh toán',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'promissoryNote',
    headerName: 'Khế ước',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'fee',
    headerName: 'Phí',
    width: 100,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'product',
    headerName: 'Sản phẩm',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'productionOrder',
    headerName: 'Lệnh sản xuất',
    width: 120,
    editable: true,

    headerAlign: 'center'
  },
  {
    field: 'invalidExpense',
    headerName: 'C/p không h/lệ',
    width: 120,
    editable: true,

    headerAlign: 'center'
  }
];
