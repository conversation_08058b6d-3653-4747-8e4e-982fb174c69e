import { <PERSON><PERSON>, FileText, Pencil, Plus, RefreshCw, Table, Trash, Search, Printer, FileSearch } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddIconClick: () => void;
  onEditIconClick: () => void;
  onDeleteIconClick: () => void;
  onCopyIconClick: () => void;
  onWatchIconClick: () => void;
  onRefreshClick?: () => void;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddIconClick,
  onEditIconClick,
  onDeleteIconClick,
  onCopyIconClick,
  onWatchIconClick,
  onRefreshClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'><PERSON>h mục ngoại tệ</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddIconClick} />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditIconClick} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteIconClick} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyIconClick} />
      <AritoActionButton title='Xem' icon={FileSearch} onClick={onWatchIconClick} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },

          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
