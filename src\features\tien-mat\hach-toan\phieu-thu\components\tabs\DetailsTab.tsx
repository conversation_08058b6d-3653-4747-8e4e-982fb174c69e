import { AritoInputTable } from '@/components/custom/arito/input-table';
import { PhieuThuChiTiet } from '@/types/schemas';

// Define FormMode type to match the AritoForm component
type FormMode = 'add' | 'edit' | 'view';
import { receiptVoucherDetailColumns } from '../../cols-definition';

interface DetailsTabProps {
  value: PhieuThuChiTiet[];
  onChange: (value: PhieuThuChiTiet[]) => void;
  formMode: FormMode;
}

export const DetailsTab: React.FC<DetailsTabProps> = ({ value, onChange, formMode }) => {
  return (
    <AritoInputTable<PhieuThuChiTiet>
      value={value}
      onChange={onChange}
      columns={receiptVoucherDetailColumns}
      mode={formMode}
      tableActionButtons={['add', 'delete', 'copy', 'paste', 'moveUp', 'moveDown', 'export', 'pin']}
    />
  );
};
