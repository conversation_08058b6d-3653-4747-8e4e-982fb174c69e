import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';

export const SearchBasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      <div className='lg:grid-cols-[150px, 1fr] grid grid-cols-1 gap-x-3 space-y-2 lg:space-y-0'>
        <div className='mt-3 flex shrink-0 items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
          Ngày c/từ (từ/đến)
        </div>
        <AritoFormDateRangeDropdown fromDateName='ngay_ctu_tu' toDateName='ngay_ctu_den' />
      </div>
      <div className='grid grid-cols-1 gap-x-3 space-y-2 lg:grid-cols-[150px,1fr,1fr] lg:space-y-0'>
        <div className='mt-3 flex shrink-0 items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
          Số c/từ (từ/đến)
        </div>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          name='so_ctu_tu'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          name='so_ctu_den'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Mã khách hàng'
        name='ma_khach_hang'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Tài khoản có'
        name='tai_khoan_co'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        label='Diễn giải'
        type='text'
        name='dien_giai'
        disabled={formMode === 'view'}
      />
    </div>
  </div>
);
