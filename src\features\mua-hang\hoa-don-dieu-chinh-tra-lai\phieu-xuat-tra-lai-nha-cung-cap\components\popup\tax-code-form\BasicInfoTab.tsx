import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const TaxCodeBasicInformationTab = ({ formMode }: Props) => (
  <div className='p-4'>
    <div className='grid gap-x-6 gap-y-4 lg:grid-cols-1'>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[3fr,1fr,1fr,1fr]'>
        <FormField
          label='Mã nhà cung cấp'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='ma_nha_cung_cap'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='flex items-center gap-x-1'
          name='loai_nha_cung_cap'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 0, label: '<PERSON>anh nghiệp' },
            { value: 1, label: 'Cá nhân' }
          ]}
        />
        <div className='flex items-center gap-x-1'>
          <div className='mt-3 flex shrink-0 items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
            Khách hàng
          </div>
          <FormField
            className='flex items-center gap-x-1'
            name='khach_hang'
            type='checkbox'
            disabled={formMode === 'view'}
          />
        </div>
        <div className='flex items-center gap-x-1'>
          <div className='mt-3 flex shrink-0 items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
            Nhà cung cấp
          </div>
          <FormField
            className='flex items-center gap-x-1'
            name='nha_cung_cap'
            type='checkbox'
            disabled={formMode === 'view'}
          />
        </div>
      </div>

      <FormField
        label='Tên nhà cung cấp'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='ten_nha_cung_cap'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Tên khác'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='ten_khac'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Địa chỉ'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='dia_chi'
        type='text'
        disabled={formMode === 'view'}
      />
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
        <div className='flex items-center gap-x-2'>
          <FormField
            type='text'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            label='Mã số thuế'
            name='taxCode'
            disabled={formMode === 'view'}
          />
          <div className='cursor-pointer'>
            <AritoIcon icon={15} />
          </div>
        </div>
        <FormField
          label='Người liên hệ'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='nguoi_lien_he'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
