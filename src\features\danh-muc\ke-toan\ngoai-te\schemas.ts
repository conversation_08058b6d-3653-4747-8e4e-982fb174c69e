import { z } from 'zod';

// Currency schema definition
export const currencySchema = z.object({
  ma_nt: z.string().min(1, { message: '<PERSON><PERSON> ngoại tệ không được để trống' }),
  ten_nt: z.string().min(1, { message: 'Tên ngoại tệ không được để trống' }),
  ten_nt2: z.string().nullable().optional(),
  tk_pscl_no: z.string().nullable().optional(),
  tk_pscl_co: z.string().nullable().optional(),
  tk_pscl_no_data: z.any().optional(),
  tk_pscl_co_data: z.any().optional(),
  stt: z.preprocess(
    val => (typeof val === 'string' ? parseInt(val, 10) : val),
    z.number({ required_error: 'Số thứ tự không được để trống' })
  ),
  ra_ndec: z.preprocess(
    val => (typeof val === 'string' ? parseInt(val, 10) : val),
    z.number({ required_error: '<PERSON><PERSON> chữ số thập phân không được để trống' })
  ),
  ra_1: z.string().nullable().optional(),
  ra_2: z.string().nullable().optional(),
  ra_3: z.string().nullable().optional(),
  ra_4: z.string().nullable().optional(),
  ra_5: z.string().nullable().optional(),
  ra_12: z.string().nullable().optional(),
  ra_22: z.string().nullable().optional(),
  ra_32: z.string().nullable().optional(),
  ra_42: z.string().nullable().optional(),
  ra_52: z.string().nullable().optional(),
  cach_doc: z.string().nullable().optional(),
  status: z.preprocess(
    val => {
      if (typeof val === 'string') {
        return val === '' ? undefined : parseInt(val, 10);
      }
      return val;
    },
    z.number({ required_error: 'Trạng thái không được để trống' })
  )
});

export const searchSchema = z.object({
  ma_nt: z.string().optional(),
  ten_nt: z.string().optional()
});

export type CurrencyFormattedData = z.infer<typeof currencySchema>;
export type SearchFormValues = z.infer<typeof searchSchema>;
