'use client';

import { ActionBar, DeleteDialog, CurrencyDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useDialogState, useRowSelection } from './hooks';
import { useNgoaiTe } from '@/hooks/queries/useNgoaiTe';
import { getDataTableColumns } from './cols-definition';

export default function DanhMucNgoaiTe() {
  const { currencies, isLoading, addCurrency, updateCurrency, deleteCurrency, refreshCurrencies } = useNgoaiTe();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  } = useDialogState(clearSelection);

  const tables = [
    {
      name: 'Tất cả',
      rows: currencies,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <CurrencyDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedObj && showEditDialog
              ? {
                  ma_nt: selectedObj.ma_nt,
                  ten_nt: selectedObj.ten_nt,
                  ten_nt2: selectedObj.ten_nt2,
                  tk_pscl_no: selectedObj.tk_pscl_no,
                  tk_pscl_co: selectedObj.tk_pscl_co,
                  tk_pscl_no_data: selectedObj.tk_pscl_no_data,
                  tk_pscl_co_data: selectedObj.tk_pscl_co_data,
                  stt: selectedObj.stt,
                  ra_ndec: selectedObj.ra_ndec,
                  ra_1: selectedObj.ra_1,
                  ra_2: selectedObj.ra_2,
                  ra_3: selectedObj.ra_3,
                  ra_4: selectedObj.ra_4,
                  ra_5: selectedObj.ra_5,
                  ra_12: selectedObj.ra_12,
                  ra_22: selectedObj.ra_22,
                  ra_32: selectedObj.ra_32,
                  ra_42: selectedObj.ra_42,
                  ra_52: selectedObj.ra_52,
                  status: parseInt(selectedObj.status)
                }
              : selectedObj && showAddDialog && isCopyMode
                ? {
                    ma_nt: selectedObj.ma_nt,
                    ten_nt: selectedObj.ten_nt,
                    ten_nt2: selectedObj.ten_nt2,
                    tk_pscl_no: selectedObj.tk_pscl_no,
                    tk_pscl_co: selectedObj.tk_pscl_co,
                    tk_pscl_no_data: selectedObj.tk_pscl_no_data,
                    tk_pscl_co_data: selectedObj.tk_pscl_co_data,
                    stt: selectedObj.stt,
                    ra_ndec: selectedObj.ra_ndec,
                    ra_1: selectedObj.ra_1,
                    ra_2: selectedObj.ra_2,
                    ra_3: selectedObj.ra_3,
                    ra_4: selectedObj.ra_4,
                    ra_5: selectedObj.ra_5,
                    ra_12: selectedObj.ra_12,
                    ra_22: selectedObj.ra_22,
                    ra_32: selectedObj.ra_32,
                    ra_42: selectedObj.ra_42,
                    ra_52: selectedObj.ra_52,
                    status: parseInt(selectedObj.status)
                  }
                : undefined
          }
          onClose={showEditDialog ? closeEditDialog : closeAddDialog}
          selectedObj={showEditDialog ? selectedObj : null}
          addCurrency={addCurrency}
          updateCurrency={updateCurrency}
        />
      )}

      {showDeleteDialog && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          selectedObj={selectedObj}
          deleteCurrency={deleteCurrency}
          clearSelection={clearSelection}
        />
      )}

      {showWatchDialog && selectedObj && (
        <CurrencyDialog
          open={showWatchDialog}
          mode='view'
          initialData={{
            ma_nt: selectedObj.ma_nt,
            ten_nt: selectedObj.ten_nt,
            ten_nt2: selectedObj.ten_nt2,
            tk_pscl_no: selectedObj.tk_pscl_no,
            tk_pscl_co: selectedObj.tk_pscl_co,
            tk_pscl_no_data: selectedObj.tk_pscl_no_data,
            tk_pscl_co_data: selectedObj.tk_pscl_co_data,
            stt: selectedObj.stt,
            ra_ndec: selectedObj.ra_ndec,
            ra_1: selectedObj.ra_1,
            ra_2: selectedObj.ra_2,
            ra_3: selectedObj.ra_3,
            ra_4: selectedObj.ra_4,
            ra_5: selectedObj.ra_5,
            ra_12: selectedObj.ra_12,
            ra_22: selectedObj.ra_22,
            ra_32: selectedObj.ra_32,
            ra_42: selectedObj.ra_42,
            ra_52: selectedObj.ra_52,
            status: parseInt(selectedObj.status)
          }}
          onClose={closeWatchDialog}
          selectedObj={selectedObj}
          addCurrency={addCurrency}
          updateCurrency={updateCurrency}
          onAddButtonClick={handleAddButtonClick}
          onEditButtonClick={handleEditButtonClick}
          onDeleteButtonClick={handleDeleteButtonClick}
          onCopyButtonClick={handleCopyButtonClick}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddIconClick={openAddDialog}
          onEditIconClick={() => selectedObj && openEditDialog()}
          onDeleteIconClick={() => selectedObj && openDeleteDialog()}
          onCopyIconClick={() => selectedObj && handleCopyButtonClick()}
          onWatchIconClick={() => selectedObj && openWatchDialog()}
          onRefreshClick={refreshCurrencies}
        />

        {isLoading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
          </div>
        )}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>
    </div>
  );
}
