import * as yup from 'yup';

// Define the form schema for validation
export const taxRateSchema = yup
  .object({
    taxCode: yup.string().required('Bạn chưa nhập mã thuế'),
    taxName: yup.string().required('Bạn chưa nhập tên thuế'),
    taxRate: yup.number().required('Bạn chưa nhập thuế suất').min(0, 'Thuế suất không được âm'),
    outputTaxAccount: yup.string().required('Bạn chưa nhập TK thuế đầu ra'),
    outputTaxReductionAccount: yup.string().required('Bạn chưa nhập TK thuế đầu ra giảm trừ'),
    inputTaxAccount: yup.string().required('Bạn chưa nhập TK thuế đầu vào'),
    inputTaxReductionAccount: yup.string().required('Bạn chưa nhập TK thuế đầu vào giảm trừ'),
    taxGroup: yup.string().required('Bạn chưa nhập nhóm thuế')
  })
  .required();
