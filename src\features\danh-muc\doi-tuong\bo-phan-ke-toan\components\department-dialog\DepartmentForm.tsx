import { Icon<PERSON>utton, Toolt<PERSON> } from '@mui/material';
import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

interface DepartmentFormProps {
  mode: FormMode;
  onChangeCodeClick?: () => void;
}

function DepartmentForm({ mode, onChangeCodeClick }: DepartmentFormProps) {
  const isDisabled = mode === 'view';
  const labelClass = 'w-32 text-right';

  return (
    <div className='flex flex-col gap-1 p-4'>
      <div className='flex items-center gap-2'>
        <FormField
          label='Mã bộ phận'
          name='ma_bp'
          type='text'
          disabled={isDisabled || mode === 'edit'}
          labelClassName={labelClass}
          className='w-full'
        />
        {isDisabled && onChangeCodeClick && (
          <Tooltip title='Đổi mã' arrow>
            <IconButton size='small' onClick={onChangeCodeClick}>
              <AritoIcon icon={127} />
            </IconButton>
          </Tooltip>
        )}
      </div>
      <FormField label='Tên bộ phận' name='ten_bp' type='text' disabled={isDisabled} labelClassName={labelClass} />
      <FormField label='Tên khác' name='ten_bp2' type='text' disabled={isDisabled} labelClassName={labelClass} />
      <FormField label='Ghi chú' name='ghi_chu' type='text' disabled={isDisabled} labelClassName={labelClass} />
      <FormField
        label='Trạng thái'
        name='status'
        type='select'
        disabled={isDisabled}
        labelClassName={labelClass}
        className='w-2/3'
        options={[
          { value: '1', label: '1. Còn sử dụng' },
          { value: '0', label: '0. Không sử dụng' }
        ]}
      />
    </div>
  );
}

export default DepartmentForm;
