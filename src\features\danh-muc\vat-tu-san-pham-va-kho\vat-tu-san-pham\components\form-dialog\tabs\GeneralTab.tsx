import { useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';
import {
  itemGroupSearchColumns,
  KhoHangSearchColBasicInfo,
  thueSearchColBasicInfo,
  uomSearchColumns,
  viTriSearchColBasicInfo,
  loaiVatTuOptions,
  cachTinhGiaTonKhoOptions
} from '../../../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { DonViTinh } from '@/types/schemas/don-vi-tinh.type';
import { ViTriKho } from '@/types/schemas/vi-tri-kho.type';
import { VatTuSanPhamFormValues } from '../../../schema';
import { KhoHang } from '@/types/schemas/kho-hang.type';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Group } from '@/types/schemas/group.type';
import { Tax } from '@/types/schemas/tax.type';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const GeneralTab = ({ formMode }: Props) => {
  const { setValue, getValues } = useFormContext<VatTuSanPhamFormValues>();
  const values = getValues();
  const isViewMode = formMode === 'view';

  // State for selected values with proper typing
  const [selectedDonViTinh, setSelectedDonViTinh] = useState<DonViTinh | null>(null);
  const [selectedNhom1, setSelectedNhom1] = useState<Group | null>(null);
  const [selectedNhom2, setSelectedNhom2] = useState<Group | null>(null);
  const [selectedNhom3, setSelectedNhom3] = useState<Group | null>(null);
  const [selectedKhoMacDinh, setSelectedKhoMacDinh] = useState<KhoHang | null>(null);
  const [selectedViTriMacDinh, setSelectedViTriMacDinh] = useState<ViTriKho | null>(null);
  const [selectedThueMacDinh, setSelectedThueMacDinh] = useState<Tax | null>(null);
  const [selectedThueNhapKhau, setSelectedThueNhapKhau] = useState<Tax | null>(null);

  // Initialize selected values from form values on component mount
  useEffect(() => {
    if (values.dvt) {
      setSelectedDonViTinh({
        uuid: values.dvt,
        dvt: values.dvt,
        ten_dvt: '',
        status: 1,
        entity_model: '',
        ten_khac: null,
        dvt2: null,
        created: '',
        updated: ''
      });
    }

    if (values.nh_vt1) {
      setSelectedNhom1({
        uuid: values.nh_vt1,
        ma_nhom: values.nh_vt1,
        ten_phan_nhom: '',
        ten2: null,
        trang_thai: '1',
        loai_nhom: 'VT1',
        entity_model: '',
        created: '',
        updated: ''
      });
    }

    if (values.nh_vt2) {
      setSelectedNhom2({
        uuid: values.nh_vt2,
        ma_nhom: values.nh_vt2,
        ten_phan_nhom: '',
        ten2: null,
        trang_thai: '1',
        loai_nhom: 'VT1',
        entity_model: '',
        created: '',
        updated: ''
      });
    }

    if (values.nh_vt3) {
      setSelectedNhom3({
        uuid: values.nh_vt3,
        ma_nhom: values.nh_vt3,
        ten_phan_nhom: '',
        ten2: null,
        trang_thai: '1',
        loai_nhom: 'VT1',
        entity_model: '',
        created: '',
        updated: ''
      });
    }

    if (values.ma_kho) {
      setSelectedKhoMacDinh({
        uuid: values.ma_kho,
        ma_kho: values.ma_kho,
        ten_kho: '',
        entity_model: '',
        ma_unit: '',
        dia_chi: '',
        dien_thoai: '',
        status: '1',
        vi_tri_yn: false,
        dai_ly_yn: false,
        created: '',
        updated: ''
      });
    }

    if (values.ma_vi_tri) {
      setSelectedViTriMacDinh({
        uuid: values.ma_vi_tri,
        ma_vi_tri: values.ma_vi_tri,
        ten_vi_tri: '',
        ma_kho: '',
        ma_kho_data: {
          uuid: '',
          ma_kho: '',
          ten_kho: '',
          dia_chi: '',
          status: '1'
        },
        status: '1',
        created: '',
        updated: ''
      });
    }

    if (values.ma_thue) {
      setSelectedThueMacDinh({
        uuid: values.ma_thue,
        ma_thue: values.ma_thue,
        ten_thue: '',
        entity_model: '',
        ten_thue2: '',
        thue_suat: 0,
        thue_suat_hddt: 0,
        tk_thue_dau_ra: '',
        tk_thue_dau_vao: '',
        tk_thue_dau_vao_duoc_gia: '',
        stt: 0,
        loai_thue: '',
        nhom_thue: '',
        status: '1',
        created: '',
        updated: ''
      });
    }

    if (values.ma_thue_nk) {
      setSelectedThueNhapKhau({
        uuid: values.ma_thue_nk,
        ma_thue: values.ma_thue_nk,
        ten_thue: '',
        entity_model: '',
        ten_thue2: '',
        thue_suat: 0,
        thue_suat_hddt: 0,
        tk_thue_dau_ra: '',
        tk_thue_dau_vao: '',
        tk_thue_dau_vao_duoc_gia: '',
        stt: 0,
        loai_thue: '',
        nhom_thue: '',
        status: '1',
        created: '',
        updated: ''
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update form values when selections change
  useEffect(() => {
    if (selectedDonViTinh && selectedDonViTinh.uuid) {
      setValue('dvt', selectedDonViTinh.uuid);
    }
  }, [selectedDonViTinh, setValue]);

  useEffect(() => {
    if (selectedNhom1) {
      setValue('nh_vt1', selectedNhom1.uuid);
    }
  }, [selectedNhom1, setValue]);

  useEffect(() => {
    if (selectedNhom2) {
      setValue('nh_vt2', selectedNhom2.uuid);
    }
  }, [selectedNhom2, setValue]);

  useEffect(() => {
    if (selectedNhom3) {
      setValue('nh_vt3', selectedNhom3.uuid);
    }
  }, [selectedNhom3, setValue]);

  useEffect(() => {
    if (selectedKhoMacDinh) {
      setValue('ma_kho', selectedKhoMacDinh.uuid);
    }
  }, [selectedKhoMacDinh, setValue]);

  useEffect(() => {
    if (selectedViTriMacDinh) {
      setValue('ma_vi_tri', selectedViTriMacDinh.uuid);
    }
  }, [selectedViTriMacDinh, setValue]);

  useEffect(() => {
    if (selectedThueMacDinh) {
      setValue('ma_thue', selectedThueMacDinh.uuid);
    }
  }, [selectedThueMacDinh, setValue]);

  useEffect(() => {
    if (selectedThueNhapKhau) {
      setValue('ma_thue_nk', selectedThueNhapKhau.uuid);
    }
  }, [selectedThueNhapKhau, setValue]);

  return (
    <div className='p-4'>
      <div className='grid gap-x-4 gap-y-4 lg:grid-cols-1'>
        <div className='flex items-center gap-x-1'>
          <div className='min-w-[160px]'>Đơn vị tính</div>
          <SearchField<DonViTinh>
            type='text'
            displayRelatedField='ten_dvt'
            columnDisplay='dvt'
            className='w-80'
            searchEndpoint={`/${QUERY_KEYS.DON_VI_TINH}`}
            searchColumns={uomSearchColumns}
            dialogTitle='Danh mục đơn vị tính'
            value={selectedDonViTinh?.dvt || ''}
            onRowSelection={row => {
              setSelectedDonViTinh({
                ...row,
                uuid: row.uuid || '',
                entity_model: row.entity_model || '',
                status: row.status || 1,
                created: row.created || '',
                updated: row.updated || ''
              });
            }}
            disabled={isViewMode}
          />
        </div>

        <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
          <FormField
            label='Cách tính giá tồn kho'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            name='cach_tinh_gia_ton_kho'
            disabled={isViewMode}
            type='select'
            options={cachTinhGiaTonKhoOptions}
          />
          <a className='text-blue-700 hover:underline' href='/'>
            (Khai báo trên đơn vị)
          </a>
        </div>

        <FormField
          label='Loại vật tư'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='loai_vat_tu'
          type='select'
          options={loaiVatTuOptions}
          defaultValue={51}
          disabled={isViewMode}
        />

        <div className='flex items-center gap-x-1'>
          <div className='min-w-[160px]'>Nhóm 1</div>
          <SearchField<Group>
            type='text'
            displayRelatedField='ten_phan_nhom'
            columnDisplay='ma_nhom'
            className='w-80'
            searchEndpoint={`/${QUERY_KEYS.NHOM}`}
            searchColumns={itemGroupSearchColumns}
            dialogTitle='Danh mục nhóm vật tư'
            value={selectedNhom1?.ma_nhom || ''}
            onRowSelection={row => {
              setSelectedNhom1({
                uuid: row.uuid || '',
                ma_nhom: row.ma_nhom || '',
                ten_phan_nhom: row.ten_phan_nhom || '',
                ten2: row.ten2 || null,
                trang_thai: row.trang_thai || '1',
                loai_nhom: row.loai_nhom || 'VT1',
                entity_model: row.entity_model || '',
                created: row.created || '',
                updated: row.updated || ''
              });
            }}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center gap-x-1'>
          <div className='min-w-[160px]'>Nhóm 2</div>
          <SearchField<Group>
            type='text'
            displayRelatedField='ten_phan_nhom'
            columnDisplay='ma_nhom'
            className='w-80'
            searchEndpoint={`/${QUERY_KEYS.NHOM}`}
            searchColumns={itemGroupSearchColumns}
            dialogTitle='Danh mục nhóm vật tư'
            value={selectedNhom2?.ma_nhom || ''}
            onRowSelection={row => {
              setSelectedNhom2({
                uuid: row.uuid || '',
                ma_nhom: row.ma_nhom || '',
                ten_phan_nhom: row.ten_phan_nhom || '',
                ten2: row.ten2 || null,
                trang_thai: row.trang_thai || '1',
                loai_nhom: row.loai_nhom || 'VT1',
                entity_model: row.entity_model || '',
                created: row.created || '',
                updated: row.updated || ''
              });
            }}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center gap-x-1'>
          <div className='min-w-[160px]'>Nhóm 3</div>
          <SearchField<Group>
            type='text'
            displayRelatedField='ten_phan_nhom'
            columnDisplay='ma_nhom'
            className='w-80'
            searchEndpoint={`/${QUERY_KEYS.NHOM}`}
            searchColumns={itemGroupSearchColumns}
            dialogTitle='Danh mục nhóm vật tư'
            value={selectedNhom3?.ma_nhom || ''}
            onRowSelection={row => {
              setSelectedNhom3({
                uuid: row.uuid || '',
                ma_nhom: row.ma_nhom || '',
                ten_phan_nhom: row.ten_phan_nhom || '',
                ten2: row.ten2 || null,
                trang_thai: row.trang_thai || '1',
                loai_nhom: row.loai_nhom || 'VT1',
                entity_model: row.entity_model || '',
                created: row.created || '',
                updated: row.updated || ''
              });
            }}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center gap-x-1'>
          <div className='min-w-[160px]'>Mã kho mặc định</div>
          <SearchField<KhoHang>
            type='text'
            displayRelatedField='ten_kho'
            columnDisplay='ma_kho'
            className='w-80'
            searchEndpoint={`/${QUERY_KEYS.KHO_HANG}`}
            searchColumns={KhoHangSearchColBasicInfo}
            dialogTitle='Danh mục kho'
            value={selectedKhoMacDinh?.ma_kho || ''}
            onRowSelection={row => {
              setSelectedKhoMacDinh({
                uuid: row.uuid || row.ma_kho || '',
                ma_kho: row.ma_kho || '',
                ten_kho: row.ten_kho || '',
                entity_model: '',
                ma_unit: '',
                dia_chi: '',
                dien_thoai: '',
                status: '1',
                vi_tri_yn: false,
                dai_ly_yn: false,
                created: '',
                updated: ''
              });
            }}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center gap-x-1'>
          <div className='min-w-[160px]'>Vị trí mặc định</div>
          <SearchField<ViTriKho>
            type='text'
            displayRelatedField='ten_vi_tri'
            columnDisplay='ma_vi_tri'
            className='w-80'
            searchEndpoint={`/${QUERY_KEYS.VI_TRI_KHO_HANG}`}
            searchColumns={viTriSearchColBasicInfo}
            dialogTitle='Danh mục vị trí'
            value={selectedViTriMacDinh?.ma_vi_tri || ''}
            onRowSelection={row => {
              setSelectedViTriMacDinh({
                uuid: row.uuid || row.ma_vi_tri || '',
                ma_vi_tri: row.ma_vi_tri || '',
                ten_vi_tri: row.ten_vi_tri || '',
                ma_kho: '',
                ma_kho_data: {
                  uuid: '',
                  ma_kho: '',
                  ten_kho: '',
                  dia_chi: '',
                  status: '1'
                },
                status: '1',
                created: '',
                updated: ''
              });
            }}
            disabled={isViewMode}
          />
        </div>

        <div className='flex gap-x-2'>
          <div className='min-w-[160px]'>Mã thuế mặc định</div>
          <SearchField<Tax>
            type='text'
            displayRelatedField='ten_thue'
            columnDisplay='ma_thue'
            className='flex-1'
            searchEndpoint={`/${QUERY_KEYS.THUE}`}
            searchColumns={thueSearchColBasicInfo}
            dialogTitle='Danh mục thuế'
            value={selectedThueMacDinh?.ma_thue || ''}
            onRowSelection={row => {
              setSelectedThueMacDinh({
                uuid: row.uuid || row.ma_thue || '',
                ma_thue: row.ma_thue || '',
                ten_thue: row.ten_thue || '',
                entity_model: '',
                ten_thue2: '',
                thue_suat: 0,
                thue_suat_hddt: 0,
                tk_thue_dau_ra: '',
                tk_thue_dau_vao: '',
                tk_thue_dau_vao_duoc_gia: '',
                stt: 0,
                loai_thue: '',
                nhom_thue: '',
                status: '1',
                created: '',
                updated: ''
              });
            }}
            disabled={isViewMode}
          />
          <SearchField<Tax>
            type='text'
            displayRelatedField='ten_thue'
            columnDisplay='ma_thue'
            className='flex-1'
            searchEndpoint={`/${QUERY_KEYS.THUE}`}
            searchColumns={thueSearchColBasicInfo}
            dialogTitle='Danh mục thuế'
            value={selectedThueNhapKhau?.ma_thue || ''}
            onRowSelection={row => {
              setSelectedThueNhapKhau({
                uuid: row.uuid || row.ma_thue || '',
                ma_thue: row.ma_thue || '',
                ten_thue: row.ten_thue || '',
                entity_model: '',
                ten_thue2: '',
                thue_suat: 0,
                thue_suat_hddt: 0,
                tk_thue_dau_ra: '',
                tk_thue_dau_vao: '',
                tk_thue_dau_vao_duoc_gia: '',
                stt: 0,
                loai_thue: '',
                nhom_thue: '',
                status: '1',
                created: '',
                updated: ''
              });
            }}
            disabled={isViewMode}
          />
        </div>
      </div>
    </div>
  );
};
