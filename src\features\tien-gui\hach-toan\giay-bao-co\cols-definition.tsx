import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getChangingValueCreditAdviceColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 100 },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 100 },
  { field: 'ma_kh', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_kh', headerName: 'Tên đối tượng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'tknh', headerName: 'Tk ngân hàng', width: 120 },
  { field: 'tk', headerName: 'Tài khoản', width: 100 },
  { field: 't_tien_nt', headerName: 'Tổng tiền', width: 120 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 120 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 100 }
];

export const exportCreditAdviceDetailColumns: GridColDef[] = [
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  { field: 'ma_dt', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_dt', headerName: 'Tên đối tượng', width: 200 },
  { field: 'du_no', headerName: 'Dư công nợ', type: 'number', width: 120 },
  { field: 'tk_co', headerName: 'Tài khoản có', width: 120 },
  { field: 'ten_tk', headerName: 'Tên tài khoản', width: 200 }
];
