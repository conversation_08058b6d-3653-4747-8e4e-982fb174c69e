import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
}

function ConfirmDialog({ open, onClose, onConfirm, title, message }: ConfirmDialogProps) {
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='sm'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={3} />}
      actions={
        <>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            onClick={onConfirm}
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            <PERSON><PERSON>ng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <p className='p-4 text-base font-medium'>{message}</p>
    </AritoDialog>
  );
}

export default ConfirmDialog;
