import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ open, onClose, onConfirm }) => {
  return (
    <AritoDialog open={open} onClose={onClose} title='Xác nhận' maxWidth='sm' titleIcon={<AritoIcon icon={12} />}>
      <div className='p-4'>
        <p className='mb-4'>Bạn có chắc chắn muốn đóng form này? Các thay đổi chưa lưu sẽ bị mất.</p>
        <div className='flex justify-end gap-2'>
          <Button variant='outlined' onClick={onClose}>
            Hủy
          </Button>
          <Button variant='contained' color='primary' onClick={onConfirm}>
            Xá<PERSON> nhận
          </Button>
        </div>
      </div>
    </AritoDialog>
  );
};

export default ConfirmDialog;
