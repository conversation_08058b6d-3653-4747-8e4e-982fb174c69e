import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { z } from 'zod';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { useDepartment } from '@/hooks/queries/useDepartment';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import ChangeCodeForm from './ChangeCodeForm';

interface ChangeCodeDialogProps {
  open: boolean;
  onClose: () => void;
  currentCode: string;
  onSuccess?: () => void;
}

export const changeCodeSchema = z.object({
  ma_bp: z.string(),
  ma_bp_moi: z.string(),
  uuid: z.string(),
  reason: z.string(),
  changeImmediately: z.boolean(),
  mergeCode: z.boolean()
});

export type ChangeCodeFormValues = z.infer<typeof changeCodeSchema>;

export const ChangeCodeDialog: React.FC<ChangeCodeDialogProps> = ({ open, onClose, currentCode, onSuccess }) => {
  const { updateDepartment, refreshDepartments, fetchDepartmentsById, departments } = useDepartment();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (values: ChangeCodeFormValues) => {
    console.log('Form values:', values);
    if (!values.uuid) {
      console.error('Missing departmentUuid, cannot update department');
      return;
    }

    setIsSubmitting(true);

    try {
      const existingDept = await fetchDepartmentsById(values.uuid);

      if (!existingDept) {
        console.error('Could not find department with UUID:', values.uuid);
        return;
      }

      const codeExists = departments.some(dept => dept.ma_bp === values.ma_bp_moi && dept.uuid !== values.uuid);

      const isMergeOperation = values.mergeCode || codeExists;

      if (isMergeOperation) {
        console.log('Performing merge operation');
      }

      const updateData = {
        ma_bp: values.ma_bp_moi,
        ten_bp: existingDept.ten_bp,
        ten_bp2: existingDept.ten_bp2 || null,
        ghi_chu: existingDept.ghi_chu,
        status: existingDept.status || '1'
      };

      if (values.changeImmediately || !isMergeOperation) {
        await updateDepartment(values.uuid, updateData);
        await refreshDepartments();
      }

      if (onSuccess) onSuccess();
      onClose();
    } catch (error) {
      console.error('Lỗi khi đổi mã:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Tạo yêu cầu đổi mã'
      maxWidth='sm'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={156} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={changeCodeSchema}
        onSubmit={handleSubmit}
        initialData={{
          ma_bp: currentCode,
          ma_bp_moi: '',
          reason: '',
          changeImmediately: true,
          mergeCode: false
        }}
        className='min-w-[600px]'
        headerFields={<ChangeCodeForm />}
        classNameBottomBar='relative w-full flex justify-end gap-2 bg-[#f9fcfd] border-t border-gray-200'
        bottomBar={
          <div className='flex justify-end gap-2 p-4'>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Lưu
            </Button>
            <Button variant='outlined' onClick={onClose} disabled={isSubmitting}>
              <AritoIcon icon={885} className='mr-2' />
              Huỷ
            </Button>
          </div>
        }
      />
    </AritoDialog>
  );
};

export default ChangeCodeDialog;
