import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

function HeaderTab() {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-2 md:space-y-2'>
          {/* Group Code Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã nhóm</Label>
            <FormField type='text' name='groupCode' className='w-full' />
          </div>

          {/* Group Name Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên phân nhóm</Label>
            <FormField type='text' name='groupName' className='w-full' />
          </div>

          {/* Secondary Name Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên 2</Label>
            <FormField type='text' name='secondaryName' className='w-full' />
          </div>

          {/* Status Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Trạng thái</Label>
            <FormField
              type='select'
              name='status'
              className='w-full'
              options={[
                { label: '1. Còn sử dụng', value: 'active' },
                { label: '2. Không sử dụng', value: 'inactive' }
              ]}
              defaultValue='active'
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default HeaderTab;
