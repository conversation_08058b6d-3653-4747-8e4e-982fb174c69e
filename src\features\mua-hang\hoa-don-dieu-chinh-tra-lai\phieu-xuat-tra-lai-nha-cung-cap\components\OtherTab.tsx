import { UploadCloud } from 'lucide-react';
import { ChangeEvent } from 'react';
import { useState } from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface Props {
  value: any[];
  onChange?: (newValue: any[]) => void;
  onFileChange?: (file: File | null) => void;
  formMode: 'add' | 'edit' | 'view';
}
export const OtherTab = ({ value, onChange, onFileChange, formMode }: Props) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);
  };
  return (
    <div className='min-w-[800px] p-4'>
      <FormField
        className='w-[250px] items-center'
        label='Giao dịch'
        type='select'
        name='giaoDich'
        disabled={formMode === 'view'}
        options={[{ value: 'TL. Trả lại nhà cung cấp', label: 'TL. Trả lại nhà cung cấp' }]}
      />
      <div className='flex items-center gap-4'>
        <Label htmlFor='file-upload' className='min-w-[100px] text-sm font-medium'>
          Chọn file
        </Label>
        <div className='flex items-center gap-2'>
          <Label
            htmlFor='file-upload'
            className='flex cursor-pointer items-center gap-2 rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90'
          >
            <UploadCloud className='h-4 w-4' />
            <span>Chọn file</span>
          </Label>
          <Input
            id='file-upload'
            type='file'
            className='hidden'
            onChange={handleFileChange}
            disabled={formMode === 'view'}
          />
          {selectedFile && <span className='text-sm text-gray-600'>{selectedFile.name}</span>}
        </div>
      </div>
    </div>
  );
};
