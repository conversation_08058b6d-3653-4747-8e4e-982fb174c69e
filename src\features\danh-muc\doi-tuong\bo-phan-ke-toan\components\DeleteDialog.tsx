import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { Bo<PERSON>han } from '@/types/schemas/bo-phan.type';
import { AritoIcon } from '@/components/custom/arito';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: BoPhan | null;
  deleteDepartment: (uuid: string) => Promise<void>;
  clearSelection: () => void;
}

function DeleteDialog({ onClose, open, selectedObj, deleteDepartment, clearSelection }: DeleteDialogProps) {
  const handleDelete = async () => {
    if (selectedObj) {
      try {
        await deleteDepartment(selectedObj.uuid);
        clearSelection();
        onClose();
      } catch (error) {
        console.error('Error deleting department:', error);
      }
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Xóa bộ phận kế toán'
      maxWidth='sm'
      titleIcon={<AritoIcon icon={12} />}
    >
      <div className='flex flex-col gap-4 p-4'>
        <p className='text-center'>
          Bạn có chắc chắn muốn xóa bộ phận kế toán <strong>{selectedObj?.ten_bp}</strong> không?
        </p>
        <div className='flex justify-end gap-2'>
          <Button variant='outlined' onClick={onClose}>
            Hủy
          </Button>
          <Button variant='contained' color='error' onClick={handleDelete}>
            Xóa
          </Button>
        </div>
      </div>
    </AritoDialog>
  );
}

export default DeleteDialog;
