'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { LyDoTangGiamTSCD } from '@/types/schemas/ly-do-tang-giam-tscd.type';
import { AssetChangeReasonActionBar } from './AssetChangeReasonActionBar';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AssetChangeReasonDialog } from './AssetChangeReasonDialog';
import { getAssetChangeReasonColumns } from '../cols-definition';
import { useLyDoTangGiamTSCD } from '@/hooks/queries';

export const AssetChangeReasonList = () => {
  // State for selected reason and dialog visibility
  const [selectedReason, setSelectedReason] = useState<LyDoTangGiamTSCD | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [isCopyMode, setIsCopyMode] = useState(false);

  // Use the custom hook for API integration
  const {
    assetChangeReasons,
    isLoading,
    addAssetChangeReason,
    updateAssetChangeReason,
    deleteAssetChangeReason,
    refreshAssetChangeReasons
  } = useLyDoTangGiamTSCD();

  const handleRowClick = (params: GridRowParams) => {
    setSelectedReason(params.row as LyDoTangGiamTSCD);
  };

  const handleCellClick = (uuid: string) => {
    const row = assetChangeReasons.find(r => r.uuid === uuid);
    if (row) {
      setSelectedReason(row);
      setShowViewDialog(true);
    }
  };
  const handleAddClick = () => {
    setIsCopyMode(false);
    setShowAddDialog(true);
  };

  const handleEditClick = () => {
    if (selectedReason) {
      setShowEditDialog(true);
    }
  };

  const handleViewClick = () => {
    if (selectedReason) {
      setShowViewDialog(true);
    }
  };

  const handleCopyClick = () => {
    if (selectedReason) {
      setIsCopyMode(true);
      setShowAddDialog(true);
    }
  };

  const handleCloseDialog = () => {
    setShowAddDialog(false);
    setShowEditDialog(false);
    setShowViewDialog(false);
  };

  const handleDelete = async () => {
    if (selectedReason) {
      try {
        await deleteAssetChangeReason(selectedReason.uuid);
        setSelectedReason(null);
      } catch (error) {
        console.error('Error deleting asset change reason:', error);
      }
    }
  };
  const tables = [
    {
      name: '',
      rows: assetChangeReasons,
      columns: getAssetChangeReasonColumns(handleCellClick, handleEditClick)
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <AssetChangeReasonDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedReason && (showEditDialog || isCopyMode)
              ? {
                  action: selectedReason.action,
                  param: selectedReason.param || '',
                  loai_tg_ts: selectedReason.loai_tg_ts,
                  ma_tg_ts: selectedReason.ma_tg_ts,
                  ten_tg_ts: selectedReason.ten_tg_ts,
                  ten_tg_ts2: selectedReason.ten_tg_ts2,
                  status: selectedReason.status
                }
              : undefined
          }
          onClose={handleCloseDialog}
          selectedObj={showEditDialog ? selectedReason : null}
          addAssetChangeReason={addAssetChangeReason}
          updateAssetChangeReason={updateAssetChangeReason}
        />
      )}

      {showViewDialog && selectedReason && (
        <AssetChangeReasonDialog
          open={showViewDialog}
          mode='view'
          initialData={{
            action: selectedReason.action,
            param: selectedReason.param || '',
            loai_tg_ts: selectedReason.loai_tg_ts,
            ma_tg_ts: selectedReason.ma_tg_ts,
            ten_tg_ts: selectedReason.ten_tg_ts,
            ten_tg_ts2: selectedReason.ten_tg_ts2,
            status: selectedReason.status
          }}
          onClose={handleCloseDialog}
          selectedObj={selectedReason}
          addAssetChangeReason={addAssetChangeReason}
          updateAssetChangeReason={updateAssetChangeReason}
          onAddButtonClick={handleAddClick}
          onEditButtonClick={handleEditClick}
          onDeleteButtonClick={handleDelete}
          onCopyButtonClick={handleCopyClick}
        />
      )}

      <div className='w-full'>
        <AssetChangeReasonActionBar
          onAddClick={handleAddClick}
          onEditClick={handleEditClick}
          isEditDisabled={!selectedReason}
        />

        {isLoading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
          </div>
        )}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedReason?.uuid} onRowClick={handleRowClick} />
        )}
      </div>
    </div>
  );
};

export default AssetChangeReasonList;
