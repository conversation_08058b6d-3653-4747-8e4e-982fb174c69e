# Contexts

This directory contains React Context implementations used throughout the application.

## Auth Context

Provides global access to authenticated user profile, entity and CoA data.

### Usage

```typescript
import { useAuth } from "@/contexts/auth-context";

function MyComponent() {
  const { profile, entity, coa, loading } = useAuth();
  
  if (loading) return <div>Loading...</div>;
  
  return (
    <div>
      <h1>Welcome {profile?.nickname}</h1>
      <p>Entity: {entity?.name}</p>
      <p>CoA: {coa?.name}</p>
    </div>
  );
}
```

### Data Flow
1. On mount, AuthProvider fetches profile data
2. Once profile is received, uses profile.entity_slug to fetch entity data
3. Once entity is received, uses entity.default_coa_slug to fetch CoA data
4. All data is stored in context state and accessible through useAuth hook

### Provider Setup
AuthProvider should be included in the app's providers:

```typescript
import { AuthProvider } from "@/contexts/auth-context";

function Providers({ children }) {
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
}
```

### Available Context Values

- `profile`: Current user profile data
- `entity`: Current entity data
- `coa`: Current CoA (Chart of Accounts) data
- `loading`: Loading state
- `error`: Error state if any request fails
- `refetchProfile`: Method to manually refresh all auth data

### Error Handling
- Uses api interceptors for 401 errors (automatic redirect to /xac-thuc)
- Provides error state for any API failures
- Console logs detailed error messages in development

### SSR Considerations
- Uses "use client" directive to avoid server-side rendering
- All data fetching happens on the client side
- Handles loading states appropriately to prevent hydration mismatches
