import { useEffect, useState } from 'react';
import { FeeFormattedData } from '@/features/danh-muc/ke-toan/phi/schemas';
import { PhiResponse, Phi } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseFeeReturn {
  fees: Phi[];
  loading: boolean;
  addFee: (data: FeeFormattedData) => Promise<void>;
  updateFee: (uuid: string, data: FeeFormattedData) => Promise<void>;
  deleteFee: (uuid: string) => Promise<void>;
  refreshFee: () => Promise<void>;
}

export const useFee = (intialFee: Phi[] = []): UseFeeReturn => {
  const [fees, setFees] = useState<Phi[]>(intialFee);
  const [loading, setLoading] = useState<boolean>(false);

  const { entity } = useAuth();

  const fetchFee = async () => {
    if (!entity?.slug) return;
    setLoading(true);

    try {
      const response = await api.get<PhiResponse>(`entities/${entity?.slug}/erp/fees/`);
      const mappedData: Phi[] = response.data.results;
      setFees(mappedData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching fees:', error);
    }
  };

  const addFee = async (data: FeeFormattedData) => {
    if (!entity?.slug) return;
    setLoading(true);
    try {
      const response = await api.post(`entities/${entity?.slug}/erp/fees/`, {
        ma_phi: data.ma_phi,
        ten_phi: data.ten_phi,
        ten_khac: data.ten_khac || null,
        nhom_phi_1: data.nhom_phi_1,
        nhom_phi_2: data.nhom_phi_2 || null,
        nhom_phi_3: data.nhom_phi_3 || null,
        bo_phan: data.bo_phan,
        trang_thai: data.trang_thai || '1'
      });

      const newFee: Phi = response.data;
      setFees(prev => [...prev, newFee]);
    } catch (error) {
      console.error('Error adding fee:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateFee = async (uuid: string, data: FeeFormattedData) => {
    if (!entity?.slug) return;
    setLoading(true);
    try {
      const response = await api.patch(`entities/${entity?.slug}/erp/fees/${uuid}/`, {
        ma_phi: data.ma_phi,
        ten_phi: data.ten_phi,
        ten_khac: data.ten_khac || null,
        nhom_phi_1: data.nhom_phi_1,
        nhom_phi_2: data.nhom_phi_2 || null,
        nhom_phi_3: data.nhom_phi_3 || null,
        bo_phan: data.bo_phan,
        trang_thai: data.trang_thai || '1'
      });

      const updatedFee: Phi = response.data;
      setFees(prev => prev.map(item => (item.uuid === uuid ? updatedFee : item)));
    } catch (error) {
      console.error('Error updating fee:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteFee = async (uuid: string) => {
    if (!entity?.slug) return;
    setLoading(true);
    try {
      await api.delete(`entities/${entity?.slug}/erp/fees/${uuid}/`);
      setFees(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting fee:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFee();
  }, [entity?.slug]);

  return {
    fees,
    loading,
    addFee,
    updateFee,
    deleteFee,
    refreshFee: fetchFee
  };
};
