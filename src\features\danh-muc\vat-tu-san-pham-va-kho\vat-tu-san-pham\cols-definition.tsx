import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import AritoCheckboxCellRenderer from '@/components/custom/arito/cell-renderers/arito-checkbox-cell-renderer';
import { VatTuSanPham, DonViTinhPhu } from '@/types/schemas/vat-tu-san-pham.type';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { DonViTinh } from '@/types/schemas/don-vi-tinh.type';
import { Checkbox } from '@/components/ui/checkbox';

// Define interfaces for other entities
interface ViTriKho {
  uuid: string;
  ma_vi_tri: string;
  ten_vi_tri: string;
}

export const itemColumns: GridColDef<VatTuSanPham>[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    renderHeader: () => <Checkbox onChange={() => {}} />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.value} />
  },
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 100 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 200 },
  { field: 'dvt', headerName: 'Đvt', width: 90 }, // Sửa từ don_vi_tinh thành dvt để khớp với API
  { field: 'ma_lvt', headerName: 'Loại vật tư', width: 90 },
  { field: 'nh_vt1', headerName: 'Nhóm 1', width: 100 },
  { field: 'nh_vt2', headerName: 'Nhóm 2', width: 100 },
  { field: 'nh_vt3', headerName: 'Nhóm 3', width: 100 },
  {
    field: 'lo_yn',
    headerName: 'Theo dõi lô',
    width: 90,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.value} />
  },
  {
    field: 'qc_data',
    headerName: 'Quy cách',
    width: 90
  },
  { field: 'tk_vt', headerName: 'Tk kho', width: 100 },
  { field: 'tk_dt', headerName: 'Tk doanh thu', width: 100 },
  { field: 'tk_gv', headerName: 'Tk giá vốn', width: 100 },
  { field: 'image_id', headerName: 'Hình ảnh', width: 100 },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 140,
    valueGetter: (params: any) => (params.row?.status === 1 ? 'Hoạt động' : 'Không hoạt động')
  }
];

// Unit conversion table columns
export const unitConversionColumns: GridColDef<DonViTinhPhu>[] = [
  { field: 'ma_dvt', headerName: 'Đvt', width: 100 },
  { field: 'he_so', headerName: 'Hệ số', width: 100, type: 'number' }
];

// Price list table columns
export const priceListColumns: GridColDef<any>[] = [
  {
    field: 'effective_date',
    headerName: 'Ngày hiệu lực',
    width: 130,
    type: 'date'
  },
  { field: 'price', headerName: 'Giá bán', width: 120, type: 'number' },
  { field: 'customer_code', headerName: 'Mã khách hàng', width: 150 }
];

// Product code table columns
export const productCodeColumns: GridColDef<any>[] = [{ field: 'product_code', headerName: 'Mã hàng hoá', width: 150 }];

export const uomSearchColumns: GridColDef<DonViTinh>[] = [
  { field: 'dvt', headerName: 'Đvt', width: 150 },
  { field: 'ten_dvt', headerName: 'Tên đơn vị tính', width: 200 }
];

export const itemGroupSearchColumns: GridColDef<any>[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 150 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
];

export const KhoHangSearchColBasicInfo: GridColDef<any>[] = [
  { field: 'ma_kho', headerName: 'Mã kho', flex: 1 },
  { field: 'ten_kho', headerName: 'Tên kho', flex: 2 },
  { field: 'don_vi', headerName: 'Đơn vị', flex: 1 },
  { field: 'theo_doi_vi_tri', headerName: 'Theo dõi vị trí', flex: 1 }
];

export const viTriSearchColBasicInfo: GridColDef<ViTriKho>[] = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', flex: 1 },
  { field: 'ten_vi_tri', headerName: 'Tên vị trí', flex: 2 }
];

export const thueSearchColBasicInfo: GridColDef<any>[] = [
  { field: 'ma_thue', headerName: 'Mã thuế', flex: 1 },
  { field: 'ten_thue', headerName: 'Tên thuế', flex: 2 }
];

export const boPhanSearchColumns: GridColDef<any>[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', flex: 2 }
];

export const DonViSearchColBasicInfo: GridColDef<any>[] = [
  { field: 'ma_don_vi', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'ten_don_vi', headerName: 'Tên đơn vị', flex: 2 },
  { field: 'id', headerName: 'ID', flex: 1 }
];

export const quocGiaSearchColumns: GridColDef<any>[] = [
  {
    field: 'checkbox',
    flex: 1,
    width: 50,
    renderHeader: () => <Checkbox onChange={() => {}} />
  },
  { field: 'ma_qg', headerName: 'Mã quốc gia', flex: 1 },
  { field: 'ten_qg', headerName: 'Tên quốc gia', flex: 2 }
];

export const mauSacSearchColumns: GridColDef<any>[] = [
  {
    field: 'checkbox',
    flex: 1,
    width: 50,
    renderHeader: () => <Checkbox onChange={() => {}} />
  },
  { field: 'ma_mau_sac', headerName: 'Mã màu sắc', flex: 1 },
  { field: 'ten_mau_sac', headerName: 'Tên màu sắc', flex: 2 },
  { field: 'mau', headerName: 'Màu', flex: 2 }
];

export const kichCoSearchColumns: GridColDef<any>[] = [
  {
    field: 'checkbox',
    flex: 1,
    width: 50,
    renderHeader: () => <Checkbox onChange={() => {}} />
  },
  { field: 'ma_kich_co', headerName: 'Mã kích cỡ', flex: 1 },
  { field: 'ten_kich_co', headerName: 'Tên kích cỡ', flex: 2 }
];

export const ConstraintColumns: GridColDef<any>[] = [
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 100 },
  {
    field: 'bat_buoc_nhap',
    headerName: 'Bắt buộc nhập',
    width: 120,
    renderCell: params => <Checkbox checked={params.value} />
  }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 1 },
  { field: 'parent_account_code', headerName: 'Tài khoản mẹ', flex: 1 },
  {
    field: 'tk_so_cai',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

// Options for select fields
export const loaiVatTuOptions = [
  { value: 0, label: '00. Dịch vụ' },
  { value: 21, label: '21. Vật tư' },
  { value: 22, label: '22. Phụ tùng' },
  { value: 31, label: '31. CCLĐ' },
  { value: 41, label: '41. Bán thành phẩm' },
  { value: 51, label: '51. Thành phẩm' },
  { value: 61, label: '61. Hàng hóa' },
  { value: 71, label: '71. Hàng gia công' }
];

export const cachTinhGiaTonKhoOptions = [
  { value: 0, label: '0. Theo khai báo đơn vị' },
  { value: 1, label: '1. Giá trung bình' },
  { value: 2, label: '2. Nhập trước xuất trước' },
  { value: 3, label: '3. Giá đích danh' },
  { value: 4, label: '4. Giá trung bình di động' }
];
