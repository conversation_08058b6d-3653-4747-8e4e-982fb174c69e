import * as yup from 'yup';

export const serviceInvoiceSchema = yup
  .object({
    // supplierCode: yup.string().required('Bạn chưa chọn nhà cung cấp'),
    // Add validation rules as needed
  })
  .required();

export const serviceInvoiceDetailSchema = yup
  .object({
    // productCode: yup.string().required("Bạn chưa nhập mã vật tư"),
    // Add other validation rules
  })
  .required();

export const serviceInvoiceExpenseSchema = yup
  .object({
    // expenseCode: yup.string().required("Bạn chưa nhập mã chi phí"),
    // Add other validation rules
  })
  .required();
