import { FormField } from '@/components/custom/arito/form/form-field';

export const BasicInformationTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      {/* Thông tin nhóm */}
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='text'
          label='Mã nhóm'
          name='groupCode'
          disabled={formMode === 'view'}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='text'
          label='Tên phân nhóm'
          name='subGroupName'
          disabled={formMode === 'view'}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='text'
          label='Tên 2'
          name='name2'
          disabled={formMode === 'view'}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          options={[
            { value: 1, label: '1. Còn sử dụng' },
            { value: 0, label: '0. Không sử dụng' }
          ]}
          label='Trạng thái'
          name='status'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
