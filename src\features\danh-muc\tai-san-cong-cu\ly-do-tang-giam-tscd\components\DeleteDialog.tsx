import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: any | null;
  deleteAssetChangeReason: (uuid: string) => Promise<void>;
  clearSelection: () => void;
}

export function DeleteDialog({
  onClose,
  open,
  selectedObj,
  deleteAssetChangeReason,
  clearSelection
}: DeleteDialogProps) {
  const handleDelete = async () => {
    if (selectedObj) {
      try {
        await deleteAssetChangeReason(selectedObj.uuid);
        clearSelection();
        onClose();
      } catch (error) {
        console.error('Error deleting asset change reason:', error);
      }
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Xác nhận xóa'
      maxWidth='sm'
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' color='error' onClick={handleDelete}>
            Xóa
          </Button>
          <Button variant='outlined' onClick={onClose}>
            Hủy
          </Button>
        </>
      }
    >
      <div className='p-4'>
        <p>
          Bạn có chắc chắn muốn xóa lý do tăng giảm <strong>{selectedObj?.ten_tg_ts}</strong> không?
        </p>
      </div>
    </AritoDialog>
  );
}
