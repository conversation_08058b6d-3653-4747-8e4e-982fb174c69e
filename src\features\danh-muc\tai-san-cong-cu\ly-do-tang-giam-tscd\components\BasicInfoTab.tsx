import { FormField } from '@/components/custom/arito/form';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
      {/* Cột trái */}
      <div className='col-span-4 space-y-2'>
        <FormField
          label='Loại tăng giảm'
          className='grid grid-cols-[160px,1fr] items-center'
          name='type'
          type='select'
          options={[
            { value: '1', label: '1. Tăng tài sản' },
            { value: '2', label: '2. Giảm tài sản' }
          ]}
          disabled={formMode === 'view'}
        />
        <FormField
          label='Mã tăng giảm'
          className='grid grid-cols-[160px,1fr] items-center'
          name='code'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tên tăng giảm'
          className='grid grid-cols-[160px,1fr] items-center'
          name='name'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tên khác'
          className='grid grid-cols-[160px,1fr] items-center'
          name='alias'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Trạng thái'
          className='grid grid-cols-[160px,1fr] items-center'
          name='status'
          type='select'
          options={[
            { value: '0', label: '0. Không sử dụng' },
            { value: '1', label: '1. Còn sử dụng' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
