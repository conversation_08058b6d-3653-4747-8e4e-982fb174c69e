import { useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { AccountModel } from '@/types/schemas/account.type';
import { NhanVien } from '@/types/schemas/nhan-vien.type';
import { NhaCungCapFormData } from '../../../../schema';
import { KhuVuc } from '@/types/schemas/khu-vuc.type';
import { Group } from '@/types/schemas/group.type';

interface UseGeneralTabStateProps {
  formMode: 'add' | 'edit' | 'view';
  selectedObj?: any; // Data from selectedObj when copying
}

interface GeneralTabState {
  selectedEmployee: NhanVien | null;
  selectedDefaultAccount: AccountModel | null;
  selectedPayableAccount: AccountModel | null;
  selectedPaymentMethod: AccountModel | null;
  selectedGroup1: Group | null;
  selectedGroup2: Group | null;
  selectedGroup3: Group | null;
  selectedArea: KhuVuc | null;
}

interface GeneralTabActions {
  setSelectedEmployee: (employee: <PERSON><PERSON><PERSON><PERSON> | null) => void;
  setSelectedDefaultAccount: (account: AccountModel | null) => void;
  setSelectedPayableAccount: (account: AccountModel | null) => void;
  setSelectedPaymentMethod: (account: AccountModel | null) => void;
  setSelectedGroup1: (group: Group | null) => void;
  setSelectedGroup2: (group: Group | null) => void;
  setSelectedGroup3: (group: Group | null) => void;
  setSelectedArea: (area: KhuVuc | null) => void;
  handleEmployeeSelection: (row: NhanVien) => void;
  handleDefaultAccountSelection: (row: AccountModel) => void;
  handlePayableAccountSelection: (row: AccountModel) => void;
  handlePaymentMethodSelection: (row: AccountModel) => void;
  handleGroup1Selection: (row: Group) => void;
  handleGroup2Selection: (row: Group) => void;
  handleGroup3Selection: (row: Group) => void;
  handleAreaSelection: (row: KhuVuc) => void;
  getFormData: () => any;
}

export const useGeneralTabState = ({
  formMode,
  selectedObj
}: UseGeneralTabStateProps): GeneralTabState & GeneralTabActions => {
  const { setValue, getValues } = useFormContext<NhaCungCapFormData>();

  // State for selected values
  const [selectedEmployee, setSelectedEmployee] = useState<NhanVien | null>(null);
  const [selectedDefaultAccount, setSelectedDefaultAccount] = useState<AccountModel | null>(null);
  const [selectedPayableAccount, setSelectedPayableAccount] = useState<AccountModel | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<AccountModel | null>(null);
  const [selectedGroup1, setSelectedGroup1] = useState<Group | null>(null);
  const [selectedGroup2, setSelectedGroup2] = useState<Group | null>(null);
  const [selectedGroup3, setSelectedGroup3] = useState<Group | null>(null);
  const [selectedArea, setSelectedArea] = useState<KhuVuc | null>(null);

  // Helper function to create AccountModel object
  const createAccountModel = (row: AccountModel): AccountModel => ({
    uuid: row.uuid || '',
    code: row.code || '',
    name: row.name || '',
    role: row.role || '',
    balance_type: row.balance_type || '',
    locked: row.locked || false,
    active: row.active || true,
    created: row.created || '',
    updated: row.updated || ''
  });

  // Helper function to create Group object
  const createGroupObject = (row: Group): Group => ({
    uuid: row.uuid || '',
    ma_nhom: row.ma_nhom || '',
    ten_phan_nhom: row.ten_phan_nhom || '',
    ten2: row.ten2 || null,
    trang_thai: row.trang_thai || '1',
    loai_nhom: row.loai_nhom || 'VT1',
    entity_model: row.entity_model || '',
    created: row.created || '',
    updated: row.updated || ''
  });

  // Selection handlers
  const handleEmployeeSelection = (row: NhanVien) => {
    setSelectedEmployee(row);
  };

  const handleDefaultAccountSelection = (row: AccountModel) => {
    const account = createAccountModel(row);
    setSelectedDefaultAccount(account);
  };

  const handlePayableAccountSelection = (row: AccountModel) => {
    const account = createAccountModel(row);
    setSelectedPayableAccount(account);
  };

  const handlePaymentMethodSelection = (row: AccountModel) => {
    const account = createAccountModel(row);
    setSelectedPaymentMethod(account);
  };

  const handleGroup1Selection = (row: Group) => {
    const group = createGroupObject(row);
    setSelectedGroup1(group);
  };

  const handleGroup2Selection = (row: Group) => {
    const group = createGroupObject(row);
    setSelectedGroup2(group);
  };

  const handleGroup3Selection = (row: Group) => {
    const group = createGroupObject(row);
    setSelectedGroup3(group);
  };

  const handleAreaSelection = (row: KhuVuc) => {
    setSelectedArea(row);
  };

  // Function to collect form data from state
  const getFormData = () => {
    return {
      sales_rep: selectedEmployee?.uuid || '',
      account: selectedDefaultAccount?.uuid || '',
      payment_method: selectedPayableAccount?.uuid || '',
      customer_group1: selectedGroup1?.uuid || '',
      customer_group2: selectedGroup2?.uuid || '',
      customer_group3: selectedGroup3?.uuid || '',
      region: selectedArea?.uuid || '',
      // Include related data for display
      sales_rep_data: selectedEmployee,
      account_data: selectedDefaultAccount,
      payment_term_data: selectedPayableAccount,
      payment_method_data: selectedPaymentMethod,
      customer_group1_data: selectedGroup1,
      customer_group2_data: selectedGroup2,
      customer_group3_data: selectedGroup3,
      region_data: selectedArea
    };
  };

  // Load data from API response when in edit mode OR from selectedObj when copying
  useEffect(() => {
    if (formMode === 'edit') {
      const values = getValues();

      if (values && Object.keys(values).length > 0) {
        // Load region data
        if ((values as any).region_data) {
          setSelectedArea((values as any).region_data);
        }

        // Load customer group data
        if ((values as any).customer_group1_data) {
          setSelectedGroup1((values as any).customer_group1_data);
        }
        if ((values as any).customer_group2_data) {
          setSelectedGroup2((values as any).customer_group2_data);
        }
        if ((values as any).customer_group3_data) {
          setSelectedGroup3((values as any).customer_group3_data);
        }

        // Load sales rep data
        if ((values as any).sales_rep_data) {
          setSelectedEmployee((values as any).sales_rep_data);
        }

        // Load account data
        if ((values as any).account_data) {
          setSelectedDefaultAccount((values as any).account_data);
        }

        // Load payment term data
        if ((values as any).payment_term_data) {
          setSelectedPayableAccount((values as any).payment_term_data);
        }

        // Load payment method data
        if ((values as any).payment_method_data) {
          setSelectedPaymentMethod((values as any).payment_method_data);
        }
      }
    }
  }, [formMode, getValues]);

  // Load data from selectedObj when copying (formMode === 'add' with selectedObj)
  useEffect(() => {
    if (formMode === 'add' && selectedObj) {
      // Load region data
      if (selectedObj.region_data) {
        setSelectedArea(selectedObj.region_data);
      }

      // Load customer group data
      if (selectedObj.customer_group1_data) {
        setSelectedGroup1(selectedObj.customer_group1_data);
      }
      if (selectedObj.customer_group2_data) {
        setSelectedGroup2(selectedObj.customer_group2_data);
      }
      if (selectedObj.customer_group3_data) {
        setSelectedGroup3(selectedObj.customer_group3_data);
      }

      // Load sales rep data
      if (selectedObj.sales_rep_data) {
        setSelectedEmployee(selectedObj.sales_rep_data);
      }

      // Load account data
      if (selectedObj.account_data) {
        setSelectedDefaultAccount(selectedObj.account_data);
      }

      // Load payment term data
      if (selectedObj.payment_term_data) {
        setSelectedPayableAccount(selectedObj.payment_term_data);
      }

      // Load payment method data
      if (selectedObj.payment_method_data) {
        setSelectedPaymentMethod(selectedObj.payment_method_data);
      }
    }
  }, [formMode, selectedObj]);

  // Sync state với form values khi state thay đổi
  useEffect(() => {
    if (selectedEmployee) {
      setValue('sales_rep', selectedEmployee.uuid || '');
    }
  }, [selectedEmployee, setValue]);

  useEffect(() => {
    if (selectedDefaultAccount) {
      setValue('account', selectedDefaultAccount.uuid || '');
    }
  }, [selectedDefaultAccount, setValue]);

  useEffect(() => {
    if (selectedPayableAccount) {
      setValue('payment_method', selectedPayableAccount.uuid || '');
    }
  }, [selectedPayableAccount, setValue]);

  useEffect(() => {
    if (selectedGroup1) {
      setValue('customer_group1', selectedGroup1.uuid || '');
    }
  }, [selectedGroup1, setValue]);

  useEffect(() => {
    if (selectedGroup2) {
      setValue('customer_group2', selectedGroup2.uuid || '');
    }
  }, [selectedGroup2, setValue]);

  useEffect(() => {
    if (selectedGroup3) {
      setValue('customer_group3', selectedGroup3.uuid || '');
    }
  }, [selectedGroup3, setValue]);

  useEffect(() => {
    if (selectedArea) {
      setValue('region', selectedArea.uuid || '');
    }
  }, [selectedArea, setValue]);

  return {
    // State
    selectedEmployee,
    selectedDefaultAccount,
    selectedPayableAccount,
    selectedPaymentMethod,
    selectedGroup1,
    selectedGroup2,
    selectedGroup3,
    selectedArea,
    // Actions
    setSelectedEmployee,
    setSelectedDefaultAccount,
    setSelectedPayableAccount,
    setSelectedPaymentMethod,
    setSelectedGroup1,
    setSelectedGroup2,
    setSelectedGroup3,
    setSelectedArea,
    // Handlers
    handleEmployeeSelection,
    handleDefaultAccountSelection,
    handlePayableAccountSelection,
    handlePaymentMethodSelection,
    handleGroup1Selection,
    handleGroup2Selection,
    handleGroup3Selection,
    handleAreaSelection,
    // Data collection
    getFormData
  };
};
