# Schemas Module

## Overview
The Schemas module provides validation schemas for common data structures used throughout the application. These schemas define the structure, constraints, and validation rules for data, ensuring data integrity and consistency across the application.

## Key Components
- `field-schemas.ts`: Schemas for common field types used in forms
- `server-env-schema.ts`: Schema for validating server environment variables

## Mermaid Diagrams

### Flowchart
```mermaid
flowchart TD
    A[Schemas] --> B[Field Schemas]
    A --> C[Environment Schemas]
    
    B --> B1[String Field Schema]
    B --> B2[Number Field Schema]
    B --> B3[Date Field Schema]
    B --> B4[Boolean Field Schema]
    B --> B5[Object Field Schema]
    B --> B6[Array Field Schema]
    
    C --> C1[Server Environment Schema]
```

### Component Diagram
```mermaid
classDiagram
    class ValidationSchema {
        +fields: Field[]
        +validate: function
        +parse: function
    }
    
    class FieldSchema {
        +type: string
        +required: boolean
        +min: number
        +max: number
        +pattern: RegExp
        +validate: function
    }
    
    class StringFieldSchema {
        +type: "string"
        +minLength: number
        +maxLength: number
        +pattern: RegExp
        +validate()
    }
    
    class NumberFieldSchema {
        +type: "number"
        +min: number
        +max: number
        +validate()
    }
    
    class ServerEnvSchema {
        +apiUrl: string
        +apiKey: string
        +environment: string
        +validate()
    }
    
    ValidationSchema <|-- FieldSchema
    FieldSchema <|-- StringFieldSchema
    FieldSchema <|-- NumberFieldSchema
    ValidationSchema <|-- ServerEnvSchema
```

### Sequence Diagram
```mermaid
sequenceDiagram
    participant Component
    participant Form
    participant ValidationSchema
    participant API
    
    Component->>Form: Render form
    Form->>ValidationSchema: Get field schemas
    ValidationSchema-->>Form: Return field schemas
    
    Form->>Form: User inputs data
    Form->>ValidationSchema: Validate form data
    ValidationSchema->>ValidationSchema: Apply validation rules
    ValidationSchema-->>Form: Return validation result
    
    alt Validation Passes
        Form->>API: Submit valid data
        API-->>Form: Confirm success
        Form-->>Component: Show success message
    else Validation Fails
        ValidationSchema-->>Form: Return validation errors
        Form-->>Component: Display validation errors
    end
