import * as yup from 'yup';

export const exportRequestSchema = yup.object().shape({
  requestNumber: yup.string().required('<PERSON>ui lòng nhập số phiếu'),
  date: yup.string().required('<PERSON><PERSON> lòng nhập ngày yêu cầu'),
  department: yup.string().required('<PERSON><PERSON> lòng chọn bộ phận'),
  warehouseCode: yup.string().required('Vui lòng chọn kho')
});

export const exportRequestDetailSchema = yup.object().shape({
  productCode: yup.string().required('<PERSON>ui lòng nhập mã sản phẩm'),
  quantity: yup.number().required('<PERSON>ui lòng nhập số lượng').min(1),
  warehouseCode: yup.string().required('Vui lòng chọn kho'),
  expectedDate: yup.string().required('<PERSON>ui lòng nhập ngày cần')
});
