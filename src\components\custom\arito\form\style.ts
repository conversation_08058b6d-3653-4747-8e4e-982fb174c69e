import { SxProps } from '@mui/material';

export const headerTabStyle: SxProps = {
  borderBottom: 1,
  borderColor: 'divider',
  height: '32px',
  display: 'flex',
  alignItems: 'center',
  textTransform: 'none',
  '& .MuiTabs-root': {
    minHeight: '32px'
  },
  '& .MuiTab-root': {
    minHeight: '32px',
    padding: '6px 16px',
    fontWeight: '1000',
    fontSize: '0.875rem',
    '&.Mui-selected': {
      color: '#2563EB',
      borderBottom: '1px solid #0b87c9'
    }
  },
  '& .MuiTabs-indicator': {
    backgroundColor: '#0b87c9'
  }
};

export const mainTabStyle: SxProps = {
  borderBottom: 1,
  borderColor: 'divider',
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  textTransform: 'none',
  '& .MuiTabs-root': {
    minHeight: '32px'
  },
  '& .MuiTab-root': {
    minHeight: '32px',
    padding: '2px 16px',
    fontWeight: '1000',
    fontSize: '11px',
    '&.Mui-selected': {
      color: '#0b87c9',
      borderBottom: '1px solid #0b87c9'
    }
  },
  '& .MuiTabs-indicator': {
    backgroundColor: '#0b87c9'
  }
};
