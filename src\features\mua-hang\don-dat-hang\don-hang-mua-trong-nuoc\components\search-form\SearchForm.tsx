import React from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import AritoModal from '@/components/custom/arito/modal';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';

const SearchForm = ({
  open,
  onClose,
  formMode,
  initialData,
  handleSubmit
}: {
  open: boolean;
  onClose: (show: boolean) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData: any;
  handleSubmit: (data: any) => void;
}) => {
  return (
    <AritoModal
      open={open}
      onClose={() => onClose(false)}
      title={'Lọc đơn hàng mua trong nước'}
      titleIcon={<AritoIcon icon={699} />}
      maxWidth='xl'
    >
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
          <AritoForm<any>
            mode={formMode}
            initialData={initialData || {}}
            hasAritoActionBar={false}
            className='!static !w-full'
            onSubmit={handleSubmit}
            onClose={() => onClose(false)}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'chi-tiet',
                label: 'Chi tiết',
                component: <DetailTab formMode={formMode} />
              }
            ]}
          />
        </div>

        <BottomBar mode={formMode} onSubmit={() => {}} onClose={() => onClose(false)} />
      </div>
    </AritoModal>
  );
};

export default SearchForm;
