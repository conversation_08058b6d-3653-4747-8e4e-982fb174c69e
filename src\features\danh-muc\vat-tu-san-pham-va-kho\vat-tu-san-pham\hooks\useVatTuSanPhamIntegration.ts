import { useState, useEffect } from 'react';
import { formatDataFromApi, formatDataForApi, VatTuSanPhamFormValues } from '../schema';
import { VatTuSanPhamInput } from '@/types/schemas/vat-tu-san-pham.type';
import { useVatTuSanPham } from '@/hooks/queries/useVatTuSanPham';

/**
 * Hook for integrating VatTuSanPham data with the UI
 *
 * This hook provides formatted data and operations for the VatTuSanPham feature.
 */
export const useVatTuSanPhamIntegration = () => {
  const {
    vatTuSanPhams,
    isLoading,
    addVatTuSanPham: addGlobalVatTuSanPham,
    updateVatTuSanPham: updateGlobalVatTuSanPham,
    deleteVatTuSanPham: deleteGlobalVatTuSanPham,
    refreshVatTuSanPhams,
    getVatTuSanPhamById,
    searchVatTuSanPham,
    getVatTuSanPhamByCode,
    getVatTuSanPhamsByFilter
  } = useVatTuSanPham();

  const [formattedVatTuSanPhams, setFormattedVatTuSanPhams] = useState<VatTuSanPhamFormValues[]>([]);

  // Format data when vatTuSanPhams changes
  useEffect(() => {
    console.log('Raw data from API:', vatTuSanPhams);

    const formatted = vatTuSanPhams.map(item => {
      // Mỗi item từ API đều có uuid, chỉ cần sử dụng formatDataFromApi để chuyển đổi
      const formattedItem = formatDataFromApi(item);
      console.log('Formatted item:', formattedItem);
      return formattedItem;
    });

    setFormattedVatTuSanPhams(formatted);
  }, [vatTuSanPhams]);

  /**
   * Add a new VatTuSanPham
   */
  const addVatTuSanPham = async (data: VatTuSanPhamFormValues) => {
    try {
      console.log('Adding VatTuSanPham with data:', data);

      const apiData = formatDataForApi(data);
      console.log('Add data after formatting:', apiData);

      await addGlobalVatTuSanPham(apiData as VatTuSanPhamInput);
      await refreshVatTuSanPhams();
    } catch (error: any) {
      console.error('Error adding VatTuSanPham:', error);
      console.error('Error details:', error.response?.data || error.message);
      throw error;
    }
  };

  /**
   * Update an existing VatTuSanPham
   */
  const updateVatTuSanPham = async (uuid: string, data: VatTuSanPhamFormValues) => {
    try {
      console.log('Updating VatTuSanPham with UUID:', uuid);
      console.log('Update data before formatting:', data);

      const apiData = formatDataForApi(data);
      console.log('Update data after formatting:', apiData);

      await updateGlobalVatTuSanPham(uuid, apiData as VatTuSanPhamInput);
      await refreshVatTuSanPhams();
    } catch (error: any) {
      console.error('Error updating VatTuSanPham:', error);
      console.error('Error details:', error.response?.data || error.message);
      throw error;
    }
  };

  /**
   * Delete a VatTuSanPham
   */
  const deleteVatTuSanPham = async (uuid: string) => {
    try {
      console.log('Deleting VatTuSanPham with UUID:', uuid);
      await deleteGlobalVatTuSanPham(uuid);
      await refreshVatTuSanPhams();
    } catch (error: any) {
      console.error('Error deleting VatTuSanPham:', error);
      console.error('Error details:', error.response?.data || error.message);
      throw error;
    }
  };

  /**
   * Copy a VatTuSanPham
   */
  const copyVatTuSanPham = async (originalData: VatTuSanPhamFormValues) => {
    try {
      console.log('Original data for copy:', originalData);

      // Create a copy of the data without the UUID
      // Use type assertion to handle the uuid property
      const { uuid, ...dataCopy } = originalData as any;

      console.log('Data copy after removing UUID:', dataCopy);

      // Modify the code to indicate it's a copy
      const newData = {
        ...dataCopy,
        ma_vt: `${dataCopy.ma_vt}-COPY`,
        ten_vt: `${dataCopy.ten_vt} (Copy)`
      };

      // Add the new item
      await addVatTuSanPham(newData);
    } catch (error: any) {
      console.error('Error copying VatTuSanPham:', error);
      console.error('Error details:', error.response?.data || error.message);
      throw error;
    }
  };

  /**
   * Get a VatTuSanPham by its UUID and format it
   */
  const getFormattedVatTuSanPhamById = async (uuid: string): Promise<VatTuSanPhamFormValues | null> => {
    try {
      const vatTuSanPham = await getVatTuSanPhamById(uuid);
      return vatTuSanPham ? formatDataFromApi(vatTuSanPham) : null;
    } catch (error) {
      console.error(`Error fetching VatTuSanPham with ID ${uuid}:`, error);
      return null;
    }
  };

  /**
   * Search for VatTuSanPham by a search term and format the results
   */
  const searchFormattedVatTuSanPham = async (searchTerm: string): Promise<VatTuSanPhamFormValues[]> => {
    try {
      const results = await searchVatTuSanPham(searchTerm);
      return results.map(item => formatDataFromApi(item));
    } catch (error) {
      console.error('Error searching VatTuSanPham:', error);
      return [];
    }
  };

  /**
   * Get a VatTuSanPham by its code and format it
   */
  const getFormattedVatTuSanPhamByCode = async (code: string): Promise<VatTuSanPhamFormValues | null> => {
    try {
      const vatTuSanPham = await getVatTuSanPhamByCode(code);
      return vatTuSanPham ? formatDataFromApi(vatTuSanPham) : null;
    } catch (error) {
      console.error(`Error fetching VatTuSanPham with code ${code}:`, error);
      return null;
    }
  };

  /**
   * Get VatTuSanPham by filter criteria and format the results
   */
  const getFormattedVatTuSanPhamsByFilter = async (filters: Record<string, any>): Promise<VatTuSanPhamFormValues[]> => {
    try {
      const results = await getVatTuSanPhamsByFilter(filters);
      return results.map(item => formatDataFromApi(item));
    } catch (error) {
      console.error('Error filtering VatTuSanPham:', error);
      return [];
    }
  };

  return {
    vatTuSanPhams: formattedVatTuSanPhams,
    isLoading,
    addVatTuSanPham,
    updateVatTuSanPham,
    deleteVatTuSanPham,
    copyVatTuSanPham,
    refreshVatTuSanPhams,
    getVatTuSanPhamById: getFormattedVatTuSanPhamById,
    searchVatTuSanPham: searchFormattedVatTuSanPham,
    getVatTuSanPhamByCode: getFormattedVatTuSanPhamByCode,
    getVatTuSanPhamsByFilter: getFormattedVatTuSanPhamsByFilter
  };
};
