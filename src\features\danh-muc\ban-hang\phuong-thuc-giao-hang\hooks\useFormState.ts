import { useState } from 'react';

type FormMode = 'add' | 'edit' | 'view';

const useFormState = () => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [showDelete, setShowDelete] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<FormMode>('add');

  const handleCloseForm = () => setShowForm(false);
  const handleCloseDelete = () => setShowDelete(false);
  const handleCloseCopy = () => setShowForm(false);

  const handleAddClick = () => {
    setShowForm(true);
    setFormMode('add');
  };

  const handleEditClick = () => {
    setShowForm(true);
    setFormMode('edit');
  };

  const handleDeleteClick = () => {
    setShowDelete(true);
    setShowForm(false);
  };

  const handleCopyClick = () => {
    setShowForm(true);
    setFormMode('add');
  };

  const handleViewClick = () => {
    setShowForm(true);
    setFormMode('view');
  };

  return {
    showForm,
    showDelete,
    formMode,
    handleCloseForm,
    handleCloseDelete,
    handleCloseCopy,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  };
};

export default useFormState;
