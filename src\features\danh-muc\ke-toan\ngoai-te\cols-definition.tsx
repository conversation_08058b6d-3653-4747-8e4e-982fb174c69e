import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const getDataTableColumns = (): GridColDef[] => {
  return [
    {
      field: 'ma_nt',
      headerName: 'Ngoại tệ',
      width: 150,
      editable: false
    },
    {
      field: 'ten_nt',
      headerName: 'Tên ngoại tệ',
      width: 200,
      flex: 1,
      editable: false
    }
  ];
};

export const TaiKhoanSearchColumns: GridColDef[] = [
  {
    field: 'code',
    headerName: 'Mã tài khoản',
    width: 150,
    editable: false
  },
  {
    field: 'name',
    headerName: 'Tên tài khoản',
    width: 200,
    flex: 1,
    editable: false
  },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    width: 150,
    editable: false,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.parent_account_code_data?.code || '';
    }
  },
  {
    field: 'is_parent_account',
    headerName: 'TK sổ cái',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'TK chi tiết',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'bac_tk',
    headerName: 'Bậc TK',
    width: 100,
    editable: false,
    type: 'number'
  }
];
