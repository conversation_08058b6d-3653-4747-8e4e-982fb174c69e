import { useState } from 'react';

interface UseDialogStateReturn {
  showAddDialog: boolean;
  showEditDialog: boolean;
  showDeleteDialog: boolean;
  showWatchDialog: boolean;
  isCopyMode: boolean;

  openAddDialog: () => void;
  closeAddDialog: () => void;
  openEditDialog: () => void;
  closeEditDialog: () => void;
  openDeleteDialog: () => void;
  closeDeleteDialog: () => void;
  openWatchDialog: () => void;
  closeWatchDialog: () => void;

  handleAddButtonClick: () => void;
  handleEditButtonClick: () => void;
  handleDeleteButtonClick: () => void;
  handleCopyButtonClick: () => void;
}

export const useDialogState = (clearSelection?: () => void): UseDialogStateReturn => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showWatchDialog, setShowWatchDialog] = useState(false);
  const [isCopyMode, setIsCopyMode] = useState(false);

  const openAddDialog = () => setShowAddDialog(true);
  const closeAddDialog = () => {
    setShowAddDialog(false);
    setIsCopyMode(false);
  };
  const openEditDialog = () => setShowEditDialog(true);
  const closeEditDialog = () => setShowEditDialog(false);
  const openDeleteDialog = () => setShowDeleteDialog(true);
  const closeDeleteDialog = () => setShowDeleteDialog(false);

  const openWatchDialog = () => setShowWatchDialog(true);
  const closeWatchDialog = () => setShowWatchDialog(false);

  const handleAddButtonClick = () => {
    closeWatchDialog();
    if (clearSelection) clearSelection();
    setIsCopyMode(false);
    openAddDialog();
  };

  const handleEditButtonClick = () => {
    closeWatchDialog();
    openEditDialog();
  };

  const handleDeleteButtonClick = () => {
    closeWatchDialog();
    openDeleteDialog();
  };

  const handleCopyButtonClick = () => {
    closeWatchDialog();
    setIsCopyMode(true);
    openAddDialog();
  };

  return {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  };
};
