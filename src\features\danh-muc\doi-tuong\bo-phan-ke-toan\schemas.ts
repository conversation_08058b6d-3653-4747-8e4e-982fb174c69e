import { z } from 'zod';

export const searchSchema = z.object({
  ma_bp: z.string().min(1, 'Mã bộ phận là bắt buộc'),
  ten_bp: z.string().min(1, 'Tên bộ phận là bắt buộc'),
  ten_bp2: z.string().optional(),
  ghi_chu: z.string().optional(),
  status: z.string().min(1, 'Trạng thái là bắt buộc')
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export interface DepartmentFormattedData {
  ma_bp: string;
  ten_bp: string;
  ten_bp2?: string | null;
  ghi_chu?: string | null;
  status: string | number;
  entity_model?: string;
  change_reason?: string;
  is_merge?: boolean;
  change_immediately?: boolean;
}
