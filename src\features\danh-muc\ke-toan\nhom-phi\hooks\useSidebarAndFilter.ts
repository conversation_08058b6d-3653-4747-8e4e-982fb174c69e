import { useState } from 'react';
import { Group, GroupType } from '@/types/schemas';

interface UseSidebarAndFilterProps {
  fetchGroupsByType?: (groupType: GroupType | null) => Promise<void>;
}

export const useSidebarAndFilter = ({ fetchGroupsByType }: UseSidebarAndFilterProps = {}) => {
  const [showSidebar, setShowSidebar] = useState<boolean>(true);
  const [activeGroup, setActiveGroup] = useState<GroupType | null>(GroupType.FEE1);

  const toggleSidebar = () => {
    setShowSidebar(prev => !prev);
  };

  const handleFilterChange = (groupType: GroupType | null) => {
    setActiveGroup(groupType);

    if (fetchGroupsByType) {
      if (groupType) {
        fetchGroupsByType(groupType);
      } else {
        fetchGroupsByType(GroupType.FEE1);
      }
    }
  };

  return {
    showSidebar,
    activeGroup,
    toggleSidebar,
    handleFilterChange
  };
};
