import { FormField } from '@/components/custom/arito/form/form-field';

export const EInvoiceTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='flex flex-col justify-between lg:flex-row'>
      <div className='w-2/3 space-y-2'>
        {/* Trạng thái HĐĐT */}
        <FormField
          label='Trạng thái HĐĐT'
          className='items-center lg:grid lg:grid-cols-[160px,1fr]'
          name='trang_thai_hddt'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 0, label: 'Không sử dụng' },
            { value: 1, label: 'Chờ phát hành' }
          ]}
        />

        {/* Thanh toán */}
        <FormField
          label='Thanh toán'
          className='items-center lg:grid lg:grid-cols-[160px,1fr]'
          name='thanh_toan'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 1, label: 'Tiền mặt' },
            { value: 2, label: 'Chuyển khoản' },
            { value: 3, label: 'Tiền mặt/ Chuyển khoản' },
            { value: 4, label: 'Xuất hàng cho chi nhánh' },
            { value: 5, label: 'Hàng biếu tặng' },
            { value: 6, label: 'Đối trừ công nợ' }
          ]}
        />

        {/* Số hóa đơn */}
        <FormField
          label='Số hóa đơn'
          className='items-center lg:grid lg:grid-cols-[160px,1fr]'
          name='so_hoa_don'
          type='text'
          disabled={formMode === 'view'}
        />

        {/* Ngày hóa đơn */}
        <FormField
          label='Ngày hóa đơn'
          className='items-center lg:grid lg:grid-cols-[160px,1fr]'
          name='ngay_hoa_don'
          type='date'
          disabled={formMode === 'view'}
        />

        {/* Ký hiệu */}
        <FormField
          label='Ký hiệu'
          className='items-center lg:grid lg:grid-cols-[160px,1fr]'
          name='ky_hieu'
          type='text'
          disabled={formMode === 'view'}
        />

        {/* Mẫu hóa đơn */}
        <FormField
          label='Mẫu hóa đơn'
          className='items-center lg:grid lg:grid-cols-[160px,1fr]'
          name='mau_hoa_don'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
