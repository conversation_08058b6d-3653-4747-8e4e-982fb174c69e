'use client';

import React from 'react';
import { DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { LoaiTSCDCCDC, LoaiTSCDCCDCInput } from '@/types/schemas';
import { useFormState, useLoaiTSCDCCDC, useRows } from '@/hooks';
import { ActionBar, FormDialog } from './components';
import { Columns } from './cols-definition';

export default function LoaiTSCDCCDCPage() {
  const { loaiTSCDCCDCs, isLoading, addLoaiTSCDCCDC, updateLoaiTSCDCCDC, deleteLoaiTSCDCCDC, refreshLoaiTSCDCCDCs } =
    useLoaiTSCDCCDC();

  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState<LoaiTSCDCCDC>();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows<LoaiTSCDCCDC>();

  const tables = [
    {
      name: '',
      rows: loaiTSCDCCDCs,
      columns: Columns
    }
  ];

  const handleFormSubmit = async (data: LoaiTSCDCCDCInput) => {
    try {
      if (formMode === 'add') {
        await addLoaiTSCDCCDC(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateLoaiTSCDCCDC(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={() => selectedObj && handleEditClick()}
        onDeleteClick={handleDeleteClick}
        onCopyClick={handleCopyClick}
        onViewClick={handleViewClick}
        onRefreshClick={() => refreshLoaiTSCDCCDCs()}
      />

      <div className='w-full overflow-hidden'>
        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <AritoDataTables tables={tables} onRowClick={handleRowClick} selectedRowId={selectedRowIndex || undefined} />
        )}
      </div>

      {showForm && (
        <FormDialog
          open={showForm}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onSubmit={handleFormSubmit}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
          onClose={handleCloseForm}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteLoaiTSCDCCDC}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
