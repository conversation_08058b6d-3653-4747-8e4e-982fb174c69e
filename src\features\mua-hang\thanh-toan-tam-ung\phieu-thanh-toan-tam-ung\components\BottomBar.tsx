import Link from 'next/link';
import React from 'react';
import { FormField } from '@/components/custom/arito/form';

export interface BottomBarProps {
  totalAmount: number;
  totalPayment: number;
  totalTax: number;
  formMode: 'add' | 'edit' | 'view';
}

export const BottomBar: React.FC<BottomBarProps> = ({ totalAmount, totalPayment, totalTax, formMode }) => {
  return (
    <div className='bottom-0 w-full bg-white px-5 pb-2 shadow-md lg:fixed lg:flex lg:items-center lg:justify-between lg:pb-1'>
      {/* Left side - Total quantity & Total payment */}
      <div className='flex w-full flex-col lg:w-1/2 lg:pl-5'>
        <div className='grid w-full grid-cols-[160px,1fr]'>
          <div className='text-sm font-bold'>Tổng tiền</div>
          <div className='flex items-center justify-end border-b-[1px] border-gray-200 text-right text-sm font-bold lg:text-left'>
            {totalAmount}
          </div>
        </div>
        <div className='grid w-full grid-cols-[160px,1fr]'>
          <div className='text-sm font-bold'>Tổng thuế</div>
          <div className='flex items-center justify-end border-b-[1px] border-gray-200 text-right text-sm font-bold lg:text-left'>
            {totalTax}
          </div>
        </div>
        <div className='grid w-full grid-cols-[160px,1fr]'>
          <div className='text-sm font-bold'>Tổng thanh toán</div>
          <div className='flex items-center justify-end border-b-[1px] border-gray-200 text-right text-sm font-bold lg:text-left'>
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND'
            })
              .format(totalPayment)
              .replace('₫', 'VND')}
          </div>
        </div>
      </div>

      {/* Right side - Checkbox */}
      <div className='flex flex-col'>
        <FormField
          type='checkbox'
          label='Quyết toán các lần tạm ứng'
          name='settlement'
          disabled={formMode === 'view'}
          className='flex flex-grow items-center pl-[160px] lg:pl-[60px]'
        />
      </div>
    </div>
  );
};
