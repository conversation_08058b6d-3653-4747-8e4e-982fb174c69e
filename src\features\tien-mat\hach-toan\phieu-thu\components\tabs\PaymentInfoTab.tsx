import React from 'react';
import { PaymentInfoTabSearchFieldStates } from '../../hooks/useSearchFieldStates';
import { QUERY_KEYS, hanThanhToanSearchColumns } from '@/constants';
import { SearchField, FormField } from '@/components/custom/arito';
import { HanThanhToan } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface PaymentInfoTabProps {
  formMode: FormMode;
  searchFieldStates: PaymentInfoTabSearchFieldStates;
}

export const PaymentInfoTab: React.FC<PaymentInfoTabProps> = ({ formMode, searchFieldStates }) => {
  const disabled = formMode === 'view';

  return (
    <div className='flex flex-col gap-2 p-4'>
      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'><PERSON> dõi thanh toán</Label>
        <FormField name='theo_doi_thanh_toan' type='checkbox' disabled={disabled} />
      </div>

      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Số chứng từ</Label>
        <FormField name='so_ct0' type='text' disabled={disabled} />
      </div>

      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Ngày chứng từ</Label>
        <FormField name='ngay_ct0' type='date' disabled={disabled} />
      </div>

      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Mã thanh toán</Label>
        <SearchField<HanThanhToan>
          type='text'
          disabled={disabled}
          dialogTitle='Danh mục hạn thanh toán'
          searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}`}
          searchColumns={hanThanhToanSearchColumns}
          displayRelatedField='ten_tt'
          columnDisplay='ma_tt'
          value={searchFieldStates.paymentTerm?.ma_tt || ''}
          relatedFieldValue={searchFieldStates.paymentTerm?.ten_tt || ''}
          onRowSelection={searchFieldStates.setPaymentTerm}
        />
      </div>
    </div>
  );
};
