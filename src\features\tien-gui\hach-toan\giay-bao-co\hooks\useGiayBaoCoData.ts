import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';

// Mock data for giay-bao-co with clickable document numbers
const mockGiayBaoCoData = [
  {
    id: 1,
    status: 'Chờ duyệt',
    so_ct: 'GBC001',
    ngay_ct: '2024-01-15',
    ma_kh: 'KH001',
    ten_kh: 'Công ty ABC',
    dien_giai: 'Gi<PERSON>y báo có thanh toán hóa đơn',
    tknh: 'VCB-112',
    sten_tknh: 'VCB - Tiền gửi ngân hàng',
    tk: '112',
    t_tien_nt: 15000000,
    ma_nt: 'VND',
    ma_ngv: 'NGV001',
    unit_id: 1,
    ma_unit: 'UNIT001',

    // Form fields for binding
    docType: 'hoa_don',
    dia_chi: '123 Đường ABC, Quận 1, TP.HCM', // SearchField
    ong_ba: '<PERSON><PERSON><PERSON><PERSON>', // SearchField
    date: '2024-01-15',
    foreignCurrency: 'vnd',
    exchangeRate: 1,
    dataReceived: true,
    chung_tu_goc: 'CTG001' // SearchField
  },
  {
    id: 2,
    status: 'Đã ghi sổ',
    so_ct: 'GBC002',
    ngay_ct: '2024-01-16',
    ma_kh: 'KH002',
    ten_kh: 'Công ty XYZ',
    dien_giai: 'Giấy báo có thu tiền bán hàng',
    tknh: 'ACB-112',
    sten_tknh: 'ACB - Tiền gửi ngân hàng',
    tk: '112',
    t_tien_nt: 22000000,
    ma_nt: 'VND',
    ma_ngv: 'NGV002',
    unit_id: 1,
    ma_unit: 'UNIT001',

    // Form fields for binding
    docType: 'doi_tuong',
    dia_chi: '456 Đường XYZ, Quận 2, TP.HCM', // SearchField
    ong_ba: 'Trần Thị B', // SearchField
    date: '2024-01-16',
    foreignCurrency: 'vnd',
    exchangeRate: 1,
    dataReceived: false,
    chung_tu_goc: 'CTG002' // SearchField
  },
  {
    id: 3,
    status: 'Chưa ghi sổ',
    so_ct: 'GBC003',
    ngay_ct: '2024-01-17',
    ma_kh: 'KH003',
    ten_kh: 'Công ty DEF',
    dien_giai: 'Giấy báo có ứng trước',
    tknh: 'BIDV-112',
    sten_tknh: 'BIDV - Tiền gửi ngân hàng',
    tk: '112',
    t_tien_nt: 5000000,
    ma_nt: 'VND',
    ma_ngv: 'NGV003',
    unit_id: 1,
    ma_unit: 'UNIT001',

    // Form fields for binding
    docType: 'thu_khac',
    dia_chi: '789 Đường DEF, Quận 3, TP.HCM', // SearchField
    ong_ba: 'Lê Văn C', // SearchField
    date: '2024-01-17',
    foreignCurrency: 'usd',
    exchangeRate: 24000,
    dataReceived: true,
    chung_tu_goc: 'CTG003' // SearchField
  }
];

/**
 * Hook for managing data and table interactions in the giay-bao-co feature
 * @returns Data state and handlers
 */
export const useGiayBaoCoData = () => {
  const [rows, setRows] = useState<any[]>(mockGiayBaoCoData);

  const createHandleRowClick = (setSelectedObj: (obj: any) => void, setInputDetails: (details: any[]) => void) => {
    return (params: GridRowParams) => {
      const obj = params.row as any;
      setSelectedObj(obj);
      // Create simple detail data for the selected row
      const details = [
        {
          id: 1,
          dien_giai: `Chi tiết cho ${obj.so_ct}`,
          ma_dt: obj.ma_kh,
          ten_dt: obj.ten_kh,
          du_no: obj.t_tien_nt,
          tk_co: obj.tk,
          ten_tk: obj.sten_tknh
        }
      ];
      setInputDetails(details);
    };
  };

  const handleFormSubmit = async (data: any) => {
    // TODO: Implement form submission logic
    console.log('Form submitted:', data);
  };

  return {
    // State
    rows,

    // Setters
    setRows,

    // Handlers
    createHandleRowClick,
    handleFormSubmit
  };
};
