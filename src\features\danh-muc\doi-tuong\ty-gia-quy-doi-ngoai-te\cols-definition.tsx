import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => {
  return [
    {
      field: 'nt_code',
      headerName: 'Mã ngoại tệ',
      width: 150,
      editable: false
    },
    {
      field: 'nt_name',
      headerName: 'Tên ngoại tệ',
      width: 200,
      editable: false
    },
    {
      field: 'ngay_hl',
      headerName: 'Ngày',
      width: 150,
      editable: false
    },
    {
      field: 'ty_gia',
      headerName: 'Tỷ giá',
      width: 150,
      editable: false
    }
  ];
};

export const currencyColumns: GridColDef[] = [
  { field: 'ma_nt', headerName: 'Mã ngoại tệ', width: 150 },
  { field: 'ten_nt', headerName: 'Tên ngoại tệ', width: 200 }
];
