import { InputTable } from '@/components/custom/arito/custom-input-table';
import { getInputTableColumns } from '../../cols-definition';
import { DetailType } from '../../schema';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  formMode: FormMode;
  detailRows: DetailType[];
}

export default function DetailTab({ formMode, detailRows }: DetailTabProps) {
  return (
    <div className='h-screen overflow-hidden'>
      <InputTable<DetailType>
        rows={detailRows}
        columns={getInputTableColumns()}
        mode={formMode}
        actionButtons={['add', 'delete', 'copy', 'paste', 'moveUp', 'moveDown', 'export', 'pin']}
        className='h-full'
      />
    </div>
  );
}
