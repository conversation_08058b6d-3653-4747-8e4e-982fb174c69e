import { DetailType, formSchema, FormValues } from '../../schema';
import { HoaDonBanHangDichVuInput } from '@/types/schemas';
import { AritoForm } from '@/components/custom/arito';
import { useSearchFieldStates } from '../../hooks';
import BasicInfoTab from './BasicInfoTab';
import EInvoiceTab from './EInvoiceTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import DetailTab from './DetailTab';
import OtherTab from './OtherTab';

interface FormDialogProps {
  formMode: FormMode;
  detailRows: DetailType[];
  totalAmount: number;
  totalDiscount: number;
  totalTax: number;
  onClose: () => void;
  addHoaDonBanDichVu: (data: HoaDonBanHangDichVuInput) => Promise<void>;
}

export default function FormDialog({
  formMode,
  detailRows,
  totalAmount,
  totalDiscount,
  totalTax,
  onClose,
  addHoaDonBanDichVu
}: FormDialogProps) {
  const searchFieldStates = useSearchFieldStates();

  const handleSubmit = (data: FormValues) => {
    const formattedData: HoaDonBanHangDichVuInput = {
      ma_tt: data.ma_tt || '',
      ma_kh: searchFieldStates.customer?.uuid || '',
      tk: searchFieldStates.account?.uuid || '',
      ma_nk: searchFieldStates.quyenChungTu?.uuid || '',
      ma_nvbh: searchFieldStates.employee?.uuid || '',
      ma_tthddt: data.ma_tthddt || '',

      ma_so_thue: data.ma_so_thue || '',
      ong_ba: data.ong_ba || '',
      e_mail: data.e_mail || '',
      dien_giai: data.dien_giai || '',
      ngay_ct: data.ngay_ct || '',
      ma_nt: data.ma_nt || 'VND',
      status: data.status || '',
      ly_do_huy: data.ly_do_huy || '',
      ly_do: data.ly_do || '',
      ten_vt_thue: data.ten_vt_thue || '',
      ghi_chu: data.ghi_chu || '',
      so_ct_hddt: data.so_ct_hddt || '',
      ngay_ct_hddt: data.ngay_ct_hddt || '',
      so_ct2_hddt: data.so_ct2_hddt || '',
      ma_mau_ct_hddt: data.ma_mau_ct_hddt || '',

      pt_tao_yn: false,
      ma_httt: data.ma_httt || '',
      ten_kh_thue: searchFieldStates.customer?.customer_name || '',
      dia_chi: searchFieldStates.customer?.address || '',
      ma_ngv: '',
      unit_id: 0,
      i_so_ct: '',
      so_ct: searchFieldStates.quyenChungTu?.uuid || '',
      ngay_lct: data.ngay_ct || '',
      so_ct2: '',
      ty_gia: 1,
      transfer_yn: false,
      ma_kh9: searchFieldStates.taxDepartment?.uuid || '',
      ma_pttt: data.ma_pttt || '',
      t_tien_nt2: 0,
      t_tien2: 0,
      t_thue_nt: 0,
      t_thue: 0,
      t_ck_nt: 0,
      t_ck: 0,
      t_tt_nt: 0,
      t_tt: 0
    };

    addHoaDonBanDichVu(formattedData);
  };

  return (
    <AritoForm
      mode={formMode}
      title={formMode === 'add' ? 'Mới' : undefined}
      subTitle='Hóa đơn bán dịch vụ'
      schema={formSchema}
      onSubmit={handleSubmit}
      onClose={onClose}
      headerFields={<BasicInfoTab formMode={formMode} searchFieldStates={searchFieldStates} />}
      tabs={[
        {
          id: 'details',
          label: 'Chi tiết',
          component: <DetailTab formMode={formMode} detailRows={detailRows} />
        },
        {
          id: 'other',
          label: 'Khác',
          component: <OtherTab formMode={formMode} searchFieldStates={searchFieldStates} />
        },
        {
          id: 'e-invoice',
          label: 'HĐĐT(Không sử dụng)',
          component: <EInvoiceTab formMode={formMode} />
        }
      ]}
      bottomBar={<BottomBar totalAmount={totalAmount} totalDiscount={totalDiscount} totalTax={totalTax} />}
    />
  );
}
