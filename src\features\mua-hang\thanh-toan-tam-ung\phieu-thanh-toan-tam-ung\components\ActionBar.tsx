import {
  ArrowLeftRight,
  Binoculars,
  FileDown,
  FileText,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Search,
  Table,
  Trash,
  View
} from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onSearchClick: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export const ActionBar = ({
  className,
  onAddClick,
  onEditClick,
  onSearchClick,
  isEditDisabled = true
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Phiếu thanh toán tạm ứng</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} disabled={isEditDisabled} />
    <AritoMenuButton
      title='In ấn'
      icon={Printer}
      items={[
        {
          title: 'Phiếu thanh toán tạm ứng',
          icon: <AritoIcon icon={20} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Phiếu thanh toán tạm ứng - TT200',
          icon: <AritoIcon icon={20} />,
          onClick: () => {},
          group: 0
        },

        {
          title: 'Chứng từ hạch toán',
          icon: <AritoIcon icon={20} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Chứng từ hạch toán (ngoại tệ)',
          icon: <AritoIcon icon={20} />,
          onClick: () => {},
          group: 1
        },

        {
          title: 'Chứng từ hạch toán (song ngữ)',
          icon: <AritoIcon icon={20} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Chứng từ hạch toán (ngoại tệ, song ngữ)',
          icon: <AritoIcon icon={20} />,
          onClick: () => {},
          group: 2
        }
      ]}
    />
    <AritoActionButton title='Xem' icon={View} onClick={() => {}} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Tìm kiếm',
          icon: <AritoIcon icon={12} />,
          onClick: onSearchClick,
          group: 0
        },
        {
          title: 'Xử lý chênh lệch',
          icon: <AritoIcon icon={772} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'In nhiều',
          icon: <AritoIcon icon={883} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={18} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Xóa',
          icon: <AritoIcon icon={8} />,
          onClick: () => {},
          group: 3
        }
      ]}
    />
  </AritoActionBar>
);
