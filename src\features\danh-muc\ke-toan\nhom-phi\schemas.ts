import { z } from 'zod';
import { GroupType } from '@/types/schemas';

export interface FeeGroupFormattedData {
  ma_nhom: string;
  ten_phan_nhom: string;
  ten2?: string | null;
  trang_thai: string;
  loai_nhom: GroupType;
}

export const searchSchema = z.object({
  ma_nhom: z.string().min(1, 'Mã nhóm là bắt buộc'),
  ten_phan_nhom: z.string().min(1, 'Tên nhóm là bắt buộc'),
  ten2: z.string().optional().nullable(),
  trang_thai: z.number().default(1)
});
export type SearchFormValues = z.infer<typeof searchSchema>;
