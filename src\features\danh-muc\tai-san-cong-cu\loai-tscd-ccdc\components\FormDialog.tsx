import { useState } from 'react';
import { AccountModel, LoaiTSCDCCDC, LoaiTSCDCCDCInput, Phi } from '@/types/schemas';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import BasicInfoTab from './form-fields/tax-form/BasicInfoTab';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { FormSchema, initialFormValues } from '../schema';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { ConfirmDialog } from '.';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: LoaiTSCDCCDCInput) => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  initialData?: LoaiTSCDCCDC;
  formMode: 'add' | 'edit' | 'view';
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  formMode,
  initialData
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [tkTs, setTkTs] = useState<AccountModel | null>(initialData?.tk_ts_data || null);
  const [tkKh, setTkKh] = useState<AccountModel | null>(initialData?.tk_kh_data || null);
  const [tkCp, setTkCp] = useState<AccountModel | null>(initialData?.tk_cp_data ? initialData.tk_cp_data : null);
  const [maPhi, setMaPhi] = useState<Phi | null>(initialData?.ma_phi_data ? initialData.ma_phi_data : null);

  const handleSubmit = (data: LoaiTSCDCCDCInput) => {
    const updatedData = {
      ...data,
      tk_ts: tkTs?.uuid || '',
      tk_kh: tkKh?.uuid || '',
      tk_cp: tkCp?.uuid || '',
      ma_phi: maPhi?.uuid || ''
    };

    onSubmit(updatedData);
    onClose();
  };

  const handleClose = () => {
    if (formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={`${formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Danh mục phương thức thanh toán'}`}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={FormSchema}
          onSubmit={handleSubmit}
          initialData={initialData || initialFormValues}
          className='w-[800px]'
          headerFields={
            <BasicInfoTab
              formMode={formMode}
              tkTs={tkTs}
              tkKh={tkKh}
              tkCp={tkCp}
              maPhi={maPhi}
              setTkTs={setTkTs}
              setTkKh={setTkKh}
              setTkCp={setTkCp}
              setMaPhi={setMaPhi}
            />
          }
          bottomBar={
            <BottomBar
              mode={formMode}
              onSubmit={() => {}}
              onClose={handleClose}
              onAdd={onAdd}
              onEdit={onEdit}
              onDelete={onDelete}
              onCopy={onCopy}
            />
          }
          classNameBottomBar='relative w-full flex justify-end gap-2'
        />
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
