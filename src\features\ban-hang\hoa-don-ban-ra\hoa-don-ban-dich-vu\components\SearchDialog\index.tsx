import { Button } from '@mui/material';
import React from 'react';
import { z } from 'zod';
import { AritoHeaderTabs, AritoIcon, AritoForm, AritoDialog, BottomBar } from '@/components/custom/arito';
import BasicInfoTab from './BasicInfoTab';
import FilterTab from './FilterTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

// Define the schema for the search form
const searchSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  voucherNumberStart: z.string().optional(),
  voucherNumberEnd: z.string().optional(),
  debitAccount: z.string().optional(),
  customerCode: z.string().optional(),
  employeeCode: z.string().optional(),
  description: z.string().optional(),
  unit: z.string().optional(),
  status: z.string().optional(),
  filterByUser: z.string().optional(),
  revenueAccount: z.string().optional()
});

type SearchFormValues = z.infer<typeof searchSchema>;

const SearchDialog = ({ open, onClose, onSearch }: SearchDialogProps) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọc hóa đơn bán dịch vụ'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: <FilterTab formMode='add' />
                }
              ]}
              className='border-b border-b-gray-200'
            />
          </div>
        }
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
        classNameBottomBar='relative w-full flex justify-end gap-2'
      />
    </AritoDialog>
  );
};

export default SearchDialog;
