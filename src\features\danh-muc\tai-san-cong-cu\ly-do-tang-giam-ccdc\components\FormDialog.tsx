import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { ToolEquipmentChangeReasonFormValues, toolEquipmentChangeReasonSchema } from '../schema';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { BasicInfoTab } from './BasicInfoTab';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: ToolEquipmentChangeReasonFormValues;
  onClose: () => void;
  selectedObj?: any;
  addToolEquipmentChangeReason: (data: ToolEquipmentChangeReasonFormValues) => Promise<void>;
  updateToolEquipmentChangeReason: (uuid: string, data: ToolEquipmentChangeReasonFormValues) => Promise<void>;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

export function FormDialog({
  open,
  mode,
  initialData,
  onClose,
  selectedObj,
  addToolEquipmentChangeReason,
  updateToolEquipmentChangeReason,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const title = mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Danh mục lý do tăng giảm CCDC';

  const handleSubmit = async (data: ToolEquipmentChangeReasonFormValues) => {
    try {
      setError(null);

      const formData = {
        ...data,
        ten_tg_cc2: data.ten_tg_cc2 || ''
      };

      if (mode === 'add') {
        await addToolEquipmentChangeReason(formData);
      } else if (mode === 'edit' && selectedObj) {
        await updateToolEquipmentChangeReason(selectedObj.uuid, formData);
      }
      onClose();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      setError(error.message || 'Có lỗi xảy ra khi lưu dữ liệu');
    }
  };

  const handleCloseConfirm = () => {
    setShowConfirmDialog(false);
  };

  const handleCancelClick = () => {
    if (mode !== 'view') {
      setShowConfirmDialog(true);
    } else {
      onClose();
    }
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={toolEquipmentChangeReasonSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<BasicInfoTab formMode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}

              {mode !== 'view' && (
                <div className='flex w-full justify-end gap-2'>
                  <Button variant='contained' color='primary' type='submit' className='bg-teal-600 hover:bg-teal-700'>
                    {mode === 'add' ? 'Đồng ý' : 'Lưu'}
                  </Button>
                  <Button variant='outlined' color='primary' onClick={handleCancelClick}>
                    Hủy
                  </Button>
                </div>
              )}
            </>
          }
        />
        {error && <div className='mt-2 rounded bg-red-50 p-4 text-red-600'>{error}</div>}
      </AritoDialog>

      {showConfirmDialog && (
        <ConfirmDialog
          open={showConfirmDialog}
          onClose={handleCloseConfirm}
          onConfirm={onClose}
          title='Xác nhận hủy'
          message='Bạn có chắc chắn muốn hủy thao tác này? Dữ liệu đã nhập sẽ không được lưu.'
        />
      )}
    </>
  );
}
