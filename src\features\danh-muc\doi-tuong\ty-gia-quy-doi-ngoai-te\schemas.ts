import { z } from 'zod';

export const searchSchema = z.object({
  ngay_hl: z.preprocess(
    val => {
      if (val instanceof Date) return val;
      if (typeof val === 'string') return new Date(val);
      return new Date();
    },
    z.date({ required_error: '<PERSON><PERSON><PERSON> hi<PERSON> lự<PERSON> là bắt buộc' })
  ),
  ma_nt: z.string().min(1, 'Mã ngoại tệ là bắt buộc'),
  ty_gia: z.preprocess(
    val => (typeof val === 'string' ? parseFloat(val) : val),
    z.number({ required_error: 'Tỷ giá là bắt buộc' }).min(0, 'Tỷ giá phải lớn hơn hoặc bằng 0')
  )
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export interface ExchangeRateFormattedData {
  ngay_hl: Date;
  ma_nt: string;
  ty_gia: number;
}
