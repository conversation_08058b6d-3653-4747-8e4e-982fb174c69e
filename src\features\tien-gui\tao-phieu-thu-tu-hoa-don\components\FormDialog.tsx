import { Check, X } from 'lucide-react';
import { Button } from '@mui/material';
import React from 'react';
import { createReceiptFromInvoiceSchema, initialValues, CreateReceiptFromInvoiceFormValues } from '../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { BasicInfo } from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: CreateReceiptFromInvoiceFormValues) => void;
}

export const FormDialog: React.FC<FormDialogProps> = ({ open, onClose, onSubmit }) => {
  const handleSubmit = (data: CreateReceiptFromInvoiceFormValues) => {
    onSubmit(data);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Tạ<PERSON> phiếu thu từ hóa đơn'
      maxWidth='md'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={createReceiptFromInvoiceSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-full overflow-y-auto'>
            <BasicInfo />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2 py-1'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};
