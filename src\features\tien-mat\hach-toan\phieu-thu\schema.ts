import { z } from 'zod';

// PhieuThu form schema matching exact FormField names used in components
export const receiptVoucherSchema = z.object({
  // Basic information (InfoTab)
  ma_ngv: z.string().optional(), // Receipt type (select)
  dia_chi: z.string().optional(), // Address (text)
  ong_ba: z.string().optional(), // Person name (text)
  dien_giai: z.string().optional(), // Description (textarea)

  // Account field (SearchField - excluded from schema as it uses useState)
  // tk: managed by SearchField component

  // Document information (InfoTab)
  ngay_ct: z.string().optional(), // Document date
  ngay_lct: z.string().optional(), // Last change date

  // Currency and exchange rate (InfoTab)
  ma_nt: z.string().optional(), // Currency (select)
  ty_gia: z.string().optional(), // Exchange rate (string for backend compatibility)

  // Status (InfoTab)
  status: z.string().optional(), // Status (select)
  unit_id: z.string().optional(), // Unit ID (required for backend)

  // Payment information (PaymentInfoTab)
  theo_doi_thanh_toan: z.boolean().optional(), // Track payment checkbox
  so_ct0: z.string().optional(), // Document number (text)
  ngay_ct0: z.string().optional(), // Document date

  // Other information (OtherTab)
  ma_kh: z.string().optional(), // Customer code (text)
  so_ct_goc: z.coerce.number().optional(), // Attachment count (number)
  dien_giai_ct_goc: z.string().optional(), // Original document (text)

  // Total amounts (for backend compatibility)
  t_tien_nt: z.string().optional(), // Total amount in foreign currency
  t_tien: z.string().optional() // Total amount in VND
});

// TypeScript type inferred from schema
export type ReceiptVoucherFormData = z.infer<typeof receiptVoucherSchema>;

// Initial values for the form
export const receiptVoucherInitialValues: ReceiptVoucherFormData = {
  // Basic information (InfoTab)
  ma_ngv: '1', // Default to first receipt type
  dia_chi: '',
  ong_ba: '',
  dien_giai: '',

  // Document information (InfoTab)
  ngay_ct: new Date().toISOString().split('T')[0], // Today's date as string
  ngay_lct: new Date().toISOString().split('T')[0], // Today's date as string

  // Currency and exchange rate (InfoTab)
  ma_nt: 'VND', // Default currency
  ty_gia: '1', // Default exchange rate as string

  // Status (InfoTab)
  status: 'unposted', // Default status
  unit_id: '', // Unit ID as string

  // Payment information (PaymentInfoTab)
  theo_doi_thanh_toan: false, // Track payment checkbox
  so_ct0: '',
  ngay_ct0: new Date().toISOString().split('T')[0], // Today's date as string

  // Other information (OtherTab)
  ma_kh: '',
  so_ct_goc: 0,
  dien_giai_ct_goc: '',

  // Total amounts (for backend compatibility)
  t_tien_nt: '0',
  t_tien: '0'
};
