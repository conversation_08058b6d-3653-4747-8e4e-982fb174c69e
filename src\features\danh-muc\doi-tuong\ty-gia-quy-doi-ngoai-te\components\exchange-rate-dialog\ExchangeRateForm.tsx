import { useFormContext } from 'react-hook-form';
import React, { useEffect } from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { currencyColumns } from '../../cols-definition';
import { FormMode } from '@/types/form';

interface ExchangeRateFormProps {
  mode: FormMode;
  selectedObj?: any;
}

const ExchangeRateForm: React.FC<ExchangeRateFormProps> = ({ mode, selectedObj }) => {
  const isDisabled = mode === 'view';
  const { setValue } = useFormContext();

  const currencyCode = selectedObj?.ma_nt_data?.ma_nt || selectedObj?.ma_nt;
  const currencyName = selectedObj?.ma_nt_data?.ten_nt || selectedObj?.ten_nt;
  const currencyUuid = selectedObj?.ma_nt_data?.uuid || selectedObj?.uuid;

  useEffect(() => {
    if (currencyUuid) {
      setValue('ma_nt', currencyUuid);
    }
  }, [currencyUuid, setValue]);

  const handleCurrencyChange = (value: string) => {
    setValue('ma_nt', value);
  };

  const handleRowSelection = (row: any) => {
    setValue('ma_nt', row.uuid);
  };

  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
        <div className='col-span-5 space-y-2'>
          <div className='grid grid-cols-[160px,1fr] items-center'>
            <label className='text-sm font-medium'>Ngoại tệ</label>
            <SearchField
              type='text'
              displayRelatedField='ten_nt'
              columnDisplay='ma_nt'
              className='w-[43%]'
              searchEndpoint='/currencies'
              searchColumns={currencyColumns}
              dialogTitle='Danh mục ngoại tệ'
              disabled={isDisabled}
              value={currencyCode}
              relatedFieldValue={currencyName}
              onValueChange={handleCurrencyChange}
              onRowSelection={handleRowSelection}
            />
          </div>

          <FormField
            label='Tỷ giá'
            className='grid grid-cols-[160px,1fr] items-center'
            name='ty_gia'
            type='number'
            disabled={isDisabled}
          />

          <FormField
            label='Ngày hiệu lực'
            className='grid grid-cols-[160px,1fr] items-center'
            name='ngay_hl'
            type='date'
            disabled={isDisabled}
          />
        </div>
      </div>
    </div>
  );
};

export default ExchangeRateForm;
