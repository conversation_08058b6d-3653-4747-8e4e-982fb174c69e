'use client';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfoTab: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        {/* 1. Mã phương thức */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã phương thức</Label>
          <FormField type='text' name='ma_bo_phan' className='w-64' />
        </div>
        {/* 2. Tên phương thức */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên phương thức</Label>
          <FormField type='text' name='ten_bo_phan' className='w-96' />
        </div>
        {/* 3. Tên khác */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên khác</Label>
          <FormField type='text' name='ten_khac' className='w-96' />
        </div>
        {/* 4. Trạng thái */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Trạng thái</Label>
          <div className='w-64'>
            <FormField
              name='detail'
              type='select'
              options={[
                { value: '1', label: '1. Còn sử dụng' },
                { value: '0', label: '0. Không sử dụng' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
