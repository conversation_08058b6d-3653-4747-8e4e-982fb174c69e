import { I<PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from '@mui/material';
import React, { useState, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { FormField } from '@/components/custom/arito/form/form-field';
import DepartmentForm from '../department-dialog/DepartmentForm';
import { useDepartment } from '@/hooks/queries/useDepartment';
import { BoPhanSearchColumns } from './cols-definition';
import { Bo<PERSON>han } from '@/types/schemas/bo-phan.type';
import { AritoIcon } from '@/components/custom/arito';

function ChangeCodeForm() {
  const { setValue, getValues } = useFormContext();
  const [showAddDepartmentDialog, setShowAddDepartmentDialog] = useState(false);
  const { departments } = useDepartment();

  useEffect(() => {
    const oldCode = getValues('ma_bp');
    if (oldCode) {
      const selectedDepartment = departments.find(dept => dept.ma_bp === oldCode);
      if (selectedDepartment) {
        setValue('uuid', selectedDepartment.uuid);
      }
    }
  }, [departments, getValues, setValue]);

  const handleMethodChange = (value: string) => {
    setValue('mergeCode', value === 'merge');
  };

  return (
    <div className='flex flex-col gap-4 p-4'>
      <div className='flex flex-col gap-2'>
        <div className='relative'>
          <FormField
            label='Mã cũ đã tồn tại'
            name='ma_bp'
            type='text'
            labelClassName='w-40 text-right'
            searchColumns={BoPhanSearchColumns()}
            withSearch={true}
            searchEndpoint='/departments'
            actionButtons={['add', 'edit']}
            headerFields={<DepartmentForm mode='add' />}
            className='w-full'
          />
          <Tooltip title='Thêm bộ phận' arrow>
            <IconButton
              size='small'
              onClick={() => setShowAddDepartmentDialog(true)}
              sx={{
                position: 'absolute',
                right: '8px',
                top: '0px',
                color: '#2563EB'
              }}
            >
              <AritoIcon icon={884} />
            </IconButton>
          </Tooltip>
        </div>
        <FormField label='Đổi/gộp sang mã mới' name='ma_bp_moi' type='text' labelClassName='w-40 text-right' />
        <FormField label='Ghi chú, lý do đổi/gộp' name='reason' type='text' labelClassName='w-40 text-right' />
      </div>

      <div className='mt-2 flex items-center gap-x-6'>
        <div className='text-sm'>Phương thức</div>
        <RadioButton
          name='method'
          options={[
            { value: 'change', label: 'Đổi mã' },
            { value: 'merge', label: 'Gộp mã' }
          ]}
          defaultValue={getValues('mergeCode') ? 'merge' : 'change'}
          onChange={handleMethodChange}
        />
        <div className='text-xs italic text-blue-600'>
          (Nếu mã cần đổi sang đã tồn tại thì hệ thống sẽ tự động gộp mã)
        </div>
      </div>

      <div className='mt-2 flex items-center gap-x-6'>
        <div className='text-sm'>Trạng thái</div>
        <FormField
          name='trang_thai'
          type='text'
          disabled={true}
          labelClassName='w-20 text-right'
          defaultValue={'Chờ thực hiện'}
        />
        <div className='flex items-center gap-2'>
          <FormField type='checkbox' name='changeImmediately' label='Đổi mã ngay sau khi lưu' />
        </div>
      </div>
    </div>
  );
}

export default ChangeCodeForm;
