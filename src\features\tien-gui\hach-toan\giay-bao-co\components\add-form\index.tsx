import { Printer, Plus, Pencil, Trash, Copy, LogOut, RefreshCw, Table, Save } from 'lucide-react';
import { useState } from 'react';
import { BankFeeTab, BasicInfoTab, BottomBar, DetailItemTab, ExchangeRateTab, OtherTab, PaymentInfoTab } from './tabs';
import { AritoForm, AritoHeaderTabs, AritoActionButton } from '@/components/custom/arito';
import { TaiKhoan, TaiKhoanNganHang } from '@/types/schemas';
import { exportCreditAdviceSchema } from '../../schemas';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';

export const AddForm = ({
  formMode,
  currentObj,
  inputDetails,
  setInputDetails,
  handleFormSubmit,
  handleCloseForm,
  calculateTotals
}: {
  formMode: FormMode;
  currentObj: any;
  inputDetails: any[];
  setInputDetails: (details: any[]) => void;
  handleFormSubmit: (data: any) => void;
  handleCloseForm: () => void;
  calculateTotals: (details: any[]) => number;
}) => {
  const [selectedTaiKhoanNo, setSelectedTaiKhoanNo] = useState<TaiKhoan | null>(null);
  const [selectTaiKhoanNganHang, setSelectedTaiKhoanNganHang] = useState<TaiKhoanNganHang | null>(null);
  const [activeTab, setActiveTab] = useState('info');

  // Determine the title based on active tab and form mode
  const getTitle = () => {
    if (formMode !== 'view') {
      return formMode === 'add' ? 'Mới' : 'Sửa';
    }

    switch (activeTab) {
      case 'history':
        return 'Lịch sử';
      default:
        return 'Giấy báo có';
    }
  };

  // Determine action buttons based on active tab
  const getActionButtons = () => {
    if (formMode !== 'view') {
      return (
        <>
          <AritoActionButton title='Lưu' icon={Save} variant='secondary' type='submit' />
          <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseForm} />
        </>
      );
    }

    switch (activeTab) {
      case 'history':
        return (
          <>
            <AritoActionButton title='Refresh' icon={RefreshCw} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Cố định cột' icon={Table} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseForm} />
          </>
        );
      default:
        return (
          <>
            <AritoActionButton title='In' icon={Printer} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Thêm' icon={Plus} variant='primary' onClick={() => {}} />
            <AritoActionButton title='Sửa' icon={Pencil} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Xóa' icon={Trash} variant='destructive' onClick={() => {}} />
            <AritoActionButton title='Sao chép' icon={Copy} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseForm} />
          </>
        );
    }
  };

  return (
    <div className='h-full flex-1 overflow-auto'>
      <AritoForm
        mode={formMode}
        hasAritoActionBar={true}
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        onClose={handleCloseForm}
        schema={exportCreditAdviceSchema}
        title={getTitle()}
        subTitle='Giấy báo có'
        headerFields={
          <AritoHeaderTabs
            defaultTabIndex={0}
            onTabChange={tabId => setActiveTab(tabId)}
            tabs={[
              {
                id: 'info',
                label: 'Thông tin',
                component: (
                  <BasicInfoTab
                    formMode={formMode}
                    selectedTaiKhoanNo={selectedTaiKhoanNo}
                    setSelectedTaiKhoanNo={setSelectedTaiKhoanNo}
                    selectedTaiKhoanNganHang={selectTaiKhoanNganHang}
                    setSelectedTaiKhoanNganHang={setSelectedTaiKhoanNganHang}
                  />
                )
              },
              ...(formMode !== 'add'
                ? [{ id: 'history', label: 'Lịch sử', component: <HistoryTab formMode={formMode} /> }]
                : [])
            ]}
          />
        }
        tabs={
          activeTab === 'info'
            ? [
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailItemTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'paymentInfo',
                  label: 'Thông tin thanh toán',
                  component: <PaymentInfoTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'exchangeRate',
                  label: 'Tỷ giá',
                  component: <ExchangeRateTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'bankFee',
                  label: 'Phí ngân hàng',
                  component: <BankFeeTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                }
              ]
            : []
        }
        actionButtons={getActionButtons()}
        bottomBar={<BottomBar totalMoney={calculateTotals(inputDetails)} totalPayment={0} formMode={formMode} />}
      />
    </div>
  );
};
