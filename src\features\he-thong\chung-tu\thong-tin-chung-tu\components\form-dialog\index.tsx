'use client';

import { <PERSON><PERSON>, LogOut, <PERSON><PERSON>l, Pin, Plus, Refresh<PERSON>c<PERSON>, Trash, FileSearch } from 'lucide-react';
import { useState, useEffect } from 'react';
import { AritoMenuButton, AritoActionButton, AritoHeaderTabs, AritoIcon, AritoForm } from '@/components/custom/arito';
import { GeneralInfoTab, TransactionTab, BasicInfoTab, DocumentTab, ObjectTab, StatusTab } from './tabs';
import { useStatusDialog, useDocumentDialog } from '../../hooks';
import { FormValues, FormSchema } from '../../schema';
import ConfirmDialog from '../ConfirmDialog';
import { ChungTu } from '@/types/schemas';
import { FormMode } from '@/types/form';
import { useObjectRows } from './hooks';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: ChungTu | null;
  onSubmit?: (data: any) => void;
  onClose: () => void;
}

const FormDialog = ({ open, onClose, onSubmit, formMode, initialData }: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');

  const { rows: objectRows, handleCellValueChange } = useObjectRows();
  const { openEditDialog: openEditStatusDialog } = useStatusDialog();
  const { openAddDialog: openAddDocumentDialog, openEditDialog: openEditDocumentDialog } = useDocumentDialog();

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const getTitle = () => {
    if (formMode !== 'view') {
      return formMode === 'add' ? 'Mới' : 'Sửa';
    }

    switch (activeTab) {
      case 'transaction':
        return `Giao dịch - ${initialData?.ten_ct}`;
      case 'status':
        return `Trạng thái - ${initialData?.ten_ct}`;
      case 'document':
        return `Quyển/số chứng từ - ${initialData?.ten_ct}`;
      default:
        return initialData?.ten_ct;
    }
  };

  const getActionButtons = () => {
    if (formMode !== 'view') {
      return null;
    }

    switch (activeTab) {
      case 'transaction':
        return (
          <>
            {/* <AritoActionButton title='Thêm' icon={Plus} onClick={openAddTransactionDialog} /> */}
            {/* <AritoActionButton title='Sửa' icon={Pencil} onClick={openEditTransactionDialog} /> */}
            {/* <AritoActionButton title='Xóa' icon={Trash} onClick={() => console.log('delete')} /> */}
            {/* <AritoActionButton title='Sao chép' icon={Copy} onClick={openAddTransactionDialog} /> */}
            {/* <AritoActionButton title='Xem' icon={FileSearch} onClick={openViewTransactionDialog} /> */}
            <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
            <AritoMenuButton
              title='Khác'
              items={[
                {
                  title: 'Refresh',
                  icon: <AritoIcon icon={15} />,
                  onClick: () => console.log('refresh'),
                  group: 0
                },
                {
                  title: 'Cố định cột',
                  icon: <AritoIcon icon={16} />,
                  onClick: () => console.log('pin'),
                  group: 0
                },
                {
                  title: 'Kết xuất dữ liệu',
                  icon: <AritoIcon icon={555} />,
                  onClick: () => console.log('export'),
                  group: 1
                }
              ]}
            />
          </>
        );
      case 'status':
        return (
          <>
            <AritoActionButton title='Sửa' variant='secondary' icon={Pencil} onClick={openEditStatusDialog} />
            <AritoActionButton
              title='Refresh'
              variant='secondary'
              icon={RefreshCcw}
              onClick={() => console.log('refresh')}
            />
            <AritoActionButton title='Cố định cột' variant='secondary' icon={Pin} onClick={() => console.log('pin')} />
            <AritoActionButton
              title='Kết xuất dữ liệu'
              variant='secondary'
              icon={FileSearch}
              onClick={() => console.log('export')}
            />
            <AritoActionButton title='Đóng' variant='secondary' icon={LogOut} onClick={handleClose} />
          </>
        );
      case 'document':
        return (
          <>
            <AritoActionButton title='Thêm' icon={Plus} onClick={openAddDocumentDialog} />
            <AritoActionButton title='Sửa' icon={Pencil} onClick={openEditDocumentDialog} />
            <AritoActionButton title='Xóa' icon={Trash} onClick={() => console.log('delete')} />
            <AritoActionButton title='Sao chép' icon={Copy} onClick={openAddDocumentDialog} />
            <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
            <AritoMenuButton
              title='Khác'
              items={[
                {
                  title: 'Tìm kiếm',
                  icon: <AritoIcon icon={12} />,
                  onClick: () => console.log('search'),
                  group: 0
                },
                {
                  title: 'Refresh',
                  icon: <AritoIcon icon={15} />,
                  onClick: () => console.log('refresh'),
                  group: 1
                },
                {
                  title: 'Cố định cột',
                  icon: <AritoIcon icon={16} />,
                  onClick: () => console.log('pin'),
                  group: 1
                },
                {
                  title: 'Kết xuất dữ liệu',
                  icon: <AritoIcon icon={555} />,
                  onClick: () => console.log('export'),
                  group: 2
                }
              ]}
            />
          </>
        );
      default:
        return (
          <>
            <AritoActionButton title='Thêm' icon={Plus} onClick={() => console.log('add')} />
            <AritoActionButton title='Sửa' icon={Pencil} onClick={() => console.log('edit')} />
            <AritoActionButton title='Xóa' icon={Trash} onClick={() => console.log('delete')} />
            <AritoActionButton title='Sao chép' icon={Copy} onClick={() => console.log('copy')} />
            <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
          </>
        );
    }
  };

  return (
    <>
      <AritoForm<ChungTu>
        mode={formMode}
        hasAritoActionBar={true}
        onClose={handleClose}
        className='w-full'
        title={getTitle() || 'Chứng từ'}
        subTitle='Danh mục chứng từ'
        initialData={initialData || undefined}
        actionButtons={getActionButtons()}
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'transaction',
                        label: 'Giao dịch',
                        component: <TransactionTab formMode={formMode} />
                      },
                      {
                        id: 'status',
                        label: 'Trạng thái',
                        component: <StatusTab formMode={formMode} />
                      },
                      {
                        id: 'document',
                        label: 'Quyển/số chứng từ',
                        component: <DocumentTab formMode={formMode} />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 2}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Thông tin chung',
              component: <GeneralInfoTab formMode={formMode} />
            },
            {
              id: 'objects',
              label: 'Đối tượng theo dõi',
              component: (
                <ObjectTab
                  mode={formMode}
                  rows={objectRows}
                  onCellValueChange={handleCellValueChange}
                  onPin={() => console.log('Pin clicked')}
                />
              )
            }
          ]
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
