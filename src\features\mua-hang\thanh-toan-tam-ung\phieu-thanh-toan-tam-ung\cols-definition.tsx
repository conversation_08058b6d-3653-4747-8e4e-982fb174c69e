import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getAdvancePaymentVoucherColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 100 },
  {
    field: 'orderNumber',
    headerName: 'Số c/từ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'orderDate', headerName: 'Ngày c/từ', width: 120 },
  { field: 'customerCode', headerName: 'Mã khách hàng', width: 120 },
  { field: 'customerName', headerName: 'Tên khách hàng', width: 200 },
  { field: 'description', headerName: '<PERSON>ễn gi<PERSON>', width: 200 },
  { field: 'accountCredit', headerName: 'Tk có', width: 100 },
  { field: 'totalAmount', headerName: 'Tổng tiền', width: 150 },
  { field: 'settlementAmount', headerName: 'Quyết toán', width: 150 },
  {
    field: 'settlementDifference',
    headerName: 'Chênh lệch quyết toán',
    width: 150
  },
  {
    field: 'settlementDocument',
    headerName: 'Chứng từ quyết toán',
    width: 150
  },
  { field: 'currency', headerName: 'Ngoại tệ', width: 100 },
  { field: 'advanceType', headerName: 'Loại tạm ứng', width: 100 }
];

export const getAdvancePaymentVoucherSubColumns: GridColDef[] = [
  { field: 'debitAccount', headerName: 'Tài khoản nợ', width: 120 },
  { field: 'accountName', headerName: 'Tên tài khoản', width: 150 },
  { field: 'customerCode', headerName: 'Mã khách hàng', width: 120 },
  { field: 'customerName', headerName: 'Tên khách hàng', width: 150 },
  { field: 'description', headerName: 'Diễn giải', width: 200 },
  { field: 'invoiceType', headerName: 'Loại hóa đơn', width: 150 },
  { field: 'reportTemplate', headerName: 'Mẫu báo cáo', width: 120 },
  { field: 'natureCode', headerName: 'Mã tính chất', width: 120 }
];

export const advancePaymentVoucherDetailColumns: GridColDef[] = [
  { field: 'debitAccount', headerName: 'Tài khoản nợ', width: 120 },
  { field: 'accountName', headerName: 'Tên tài khoản', width: 150 },
  { field: 'customerCode', headerName: 'Mã khách hàng', width: 120 },
  { field: 'customerName', headerName: 'Tên khách hàng', width: 150 },
  { field: 'amountVND', headerName: 'Tiền VND', width: 120 },
  { field: 'description', headerName: 'Diễn giải', width: 200 },
  { field: 'invoiceType', headerName: 'Loại hóa đơn', width: 150 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'case', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 150 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 120 },
  { field: 'fee', headerName: 'Phí', width: 100 },
  { field: 'product', headerName: 'Sản phẩm', width: 150 },
  { field: 'manufacturingOrder', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'invalidField', headerName: 'C/p không h/lệ', width: 150 }
];

export const advancePaymentVoucherTaxColumns: GridColDef[] = [
  { field: 'invoiceNumber', headerName: 'Số hóa đơn', width: 120 },
  { field: 'invoiceSymbol', headerName: 'Ký hiệu', width: 100 },
  { field: 'invoiceDate', headerName: 'Ngày hóa đơn', width: 120 },
  { field: 'taxRate', headerName: 'Thuế suất', width: 100 },
  { field: 'invoiceForm', headerName: 'Mẫu hóa đơn', width: 120 },
  { field: 'reportForm', headerName: 'Mẫu báo cáo', width: 120 },
  { field: 'natureCode', headerName: 'Mã tính chất', width: 120 },
  { field: 'supplierCode', headerName: 'Mã ncc', width: 100 },
  { field: 'supplierName', headerName: 'Tên nhà cung cấp', width: 200 },
  { field: 'address', headerName: 'Địa chỉ', width: 250 },
  { field: 'taxNumber', headerName: 'Mã số thuế', width: 150 },
  { field: 'productName', headerName: 'Tên hàng hóa - dịch vụ', width: 200 },
  { field: 'amountVND', headerName: 'Tiền hàng VND', width: 150 },
  { field: 'taxAccount', headerName: 'Tk thuế', width: 100 },
  { field: 'taxAmountVND', headerName: 'Thuế VND', width: 120 },
  { field: 'taxDepartment', headerName: 'Cục thuế', width: 120 },
  { field: 'paymentCode', headerName: 'Mã thanh toán', width: 120 },
  { field: 'note', headerName: 'Ghi chú', width: 200 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'task', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 120 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 120 },
  { field: 'fee', headerName: 'Phí', width: 100 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'productionOrder', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'invalidField', headerName: 'C/p không h/lệ', width: 150 }
];
