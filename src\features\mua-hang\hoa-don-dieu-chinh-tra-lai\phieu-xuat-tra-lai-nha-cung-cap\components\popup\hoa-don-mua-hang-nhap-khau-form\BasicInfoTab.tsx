import React from 'react';
import { NhaCungCapSearchColDef, SoHoaDonSearchColDef } from '../../../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { FormField } from '@/components/custom/arito/form/form-field';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const HoaDonMuaHangNhapKhauBasicInfoTab = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <div className='grid grid-cols-[150px,1fr,1fr] items-center gap-x-4'>
        <div className='mt-3 flex items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
          <PERSON><PERSON>y c/từ từ/đến
        </div>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='date'
          name='ngay_ctu_tu'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='date'
          name='ngay_ctu_den'
          disabled={formMode === 'view'}
        />
      </div>
      {/* <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Mã nhà cung cấp'
        name='ma_nha_cung_cap'
        disabled={formMode === 'view'}
        withSearch={true}
      /> */}

      <div className='flex items-center gap-x-1'>
        <Label className='min-w-[160px]'>Mã nhà cung cấp</Label>
        <div className='w-full'>
          <SearchField
            className='w-full'
            disabled={formMode === 'view'}
            searchEndpoint={`/erp/${QUERY_KEYS.NHA_CUNG_CAP}/`}
            searchColumns={NhaCungCapSearchColDef}
            displayRelatedField='ten_nha_cung_cap'
            columnDisplay='ma_nha_cung_cap'
          />
        </div>
      </div>

      {/* <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='text'
        label='Số hóa đơn'
        name='so_hoa_don'
        disabled={formMode === 'view'}
        withSearch={true}
      /> */}

      <div className='flex items-center gap-x-1'>
        <Label className='min-w-[160px]'>Số hóa đơn</Label>
        <div className='w-full'>
          <SearchField
            className='w-full'
            disabled={formMode === 'view'}
            searchEndpoint={`/erp/${QUERY_KEYS.HOA_DON}/`}
            searchColumns={SoHoaDonSearchColDef}
            displayRelatedField='so_hoa_don'
            columnDisplay='so_hoa_don'
          />
        </div>
      </div>

      {/* <FormField
        className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
        type='select'
        label='Xử lý'
        name='xu_ly'
        disabled={formMode === 'view'}
        options={[
          { value: 0, label: 'Thêm vào chi tiết' },
          { value: 1, label: 'Xóa hết chi tiết và thêm mới' }
        ]}
      /> */}
      <div className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'>
        <Label className='text-sm font-normal'>Xử lý</Label>
        <RadioButton
          name='xu_ly'
          options={[
            { value: 'them_vao_chi_tiet', label: 'Thêm vào chi tiết' },
            { value: 'xoa_het_va_them_moi', label: 'Xóa hết chi tiết và thêm mới' }
          ]}
          defaultValue='xoa_het_va_them_moi'
          orientation='horizontal'
          onChange={() => {}}
        />
      </div>

      <div className='grid grid-cols-[150px,1fr] items-center gap-x-4'>
        <div className='mt-3 flex items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
          Chọn tất cả
        </div>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='checkbox'
          name='chon_tat_ca'
          disabled={formMode === 'view'}
          withSearch={true}
        />
      </div>
    </div>
  );
};

export default HoaDonMuaHangNhapKhauBasicInfoTab;
