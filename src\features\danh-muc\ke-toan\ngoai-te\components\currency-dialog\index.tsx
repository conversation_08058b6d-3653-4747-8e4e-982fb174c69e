import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';

import { currencySchema, CurrencyFormattedData } from '../../schemas';

import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import ConfirmDialog from './ConfirmDialog';
import { NgoaiTe } from '@/types/schemas';
import CurrencyForm from './CurrencyForm';
import { FormMode } from '@/types/form';

interface CurrencyDialogProps {
  open: boolean;
  mode: 'add' | 'edit' | 'view';
  initialData?: CurrencyFormattedData;
  onClose: () => void;
  selectedObj: NgoaiTe | null;
  addCurrency: (data: CurrencyFormattedData) => Promise<void>;
  updateCurrency: (uuid: string, data: CurrencyFormattedData) => Promise<void>;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

const CurrencyDialog: React.FC<CurrencyDialogProps> = ({
  open,
  mode,
  initialData,
  onClose,
  selectedObj,
  addCurrency,
  updateCurrency,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}) => {
  const [showConfirm, setShowConfirm] = useState(false);
  const [formData, setFormData] = useState<CurrencyFormattedData | null>(null);

  const handleSubmit = async (data: CurrencyFormattedData) => {
    setFormData(data);

    // If in view mode, don't show confirm dialog
    if (mode === 'view') {
      return;
    }

    setShowConfirm(true);
  };

  const handleConfirm = async () => {
    if (!formData) return;

    try {
      if (mode === 'edit' && selectedObj) {
        await updateCurrency(selectedObj.uuid, formData);
      } else {
        await addCurrency(formData);
      }
      setShowConfirm(false);
      onClose();
    } catch (error) {
      console.error('Error saving currency:', error);
    }
  };

  const handleCancel = () => {
    setShowConfirm(false);
    onClose();
  };

  const title = mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem chi tiết';

  return (
    <>
      <AritoDialog
        open={open}
        onClose={() => setShowConfirm(true)}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={currencySchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='w-full md:min-w-[500px] lg:min-w-[800px]'
          headerFields={<CurrencyForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}

              {mode !== 'view' && (
                <div className='flex w-full justify-end gap-2 p-4'>
                  <Button
                    className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
                    type='submit'
                    variant='contained'
                  >
                    <AritoIcon icon={884} marginX='4px' />
                    Đồng ý
                  </Button>

                  <Button onClick={handleCancel} variant='outlined'>
                    <AritoIcon icon={885} marginX='4px' />
                    Huỷ
                  </Button>
                </div>
              )}
            </>
          }
        />
      </AritoDialog>

      <ConfirmDialog
        open={showConfirm}
        onClose={handleCancel}
        onCloseConfirmDialog={() => setShowConfirm(false)}
        onConfirm={handleConfirm}
      />
    </>
  );
};

export default CurrencyDialog;
