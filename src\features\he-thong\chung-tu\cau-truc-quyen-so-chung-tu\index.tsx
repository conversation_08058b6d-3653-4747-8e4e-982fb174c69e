'use client';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { LoadingOverlay } from '@/components/custom/arito';
import { FormDialog, DeleteDialog } from './components';
import { getDataTableColumns } from './cols-definition';
import { ActionBar } from './components/ActionBar';
import { initialFormValues } from './schemas';
import { useCustomFormState } from './hooks';
import { useQuyenChungTu } from '@/hooks';

export default function CauTrucQuyenSoChungTuPage() {
  const { quyenChungTus, isLoading, addQuyenChungTu, updateQuyenChungTu, deleteQuyenChungTu } = useQuyenChungTu();

  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    selectedObj,
    selectedRowIndex,
    clearSelection,
    handleRowClick,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useCustomFormState();

  // Handle form submission in the parent component
  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addQuyenChungTu(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateQuyenChungTu(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  let tables = [
    {
      name: 'Cập nhật giá bán',
      rows: quyenChungTus,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <div className='w-full'>
        <ActionBar
          onAddIconClick={handleAddClick}
          onEditIconClick={() => selectedObj && handleEditClick()}
          onDeleteIconClick={() => selectedObj && handleDeleteClick()}
          onCopyIconClick={() => selectedObj && handleCopyClick()}
          onWatchIconClick={() => selectedObj && handleViewClick()}
          selectedObj={selectedObj}
          isEditDisabled={!selectedObj}
        />
        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <AritoDataTables
            tables={tables}
            selectedRowId={selectedRowIndex || undefined}
            onRowClick={handleRowClick}
            onTabChange={() => {
              clearSelection();
            }}
          />
        )}
      </div>

      {showForm && (
        <FormDialog
          mode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleSubmit}
          onAddButtonClick={() => {
            handleCloseForm();
            clearSelection();
            handleAddClick();
          }}
          onEditButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
          onWatchButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleViewClick();
            }
          }}
          initialData={formMode === 'add' && !isCopyMode ? initialFormValues : selectedObj}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
