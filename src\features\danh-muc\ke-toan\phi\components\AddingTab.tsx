import { FormField } from '@/components/custom/arito/form/form-field';

export const AddingTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='text'
          label='Mã phí'
          name='feeCode'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Tên phí'
          name='feeName'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Tên khác'
          name='feeAlias'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Nhóm phí 1'
          name='feeGroup1'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Nhóm phí 2'
          name='feeGroup2'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Nhóm phí 3'
          name='feeGroup3'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Bộ phận'
          name='department'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='select'
          label='Trạng thái'
          name='status'
          disabled={formMode === 'view'}
          options={[
            { label: '1. Còn sử dụng', value: 'active' },
            { label: '2. Không sử dụng', value: 'inactive' }
          ]}
        />
      </div>
    </div>
  </div>
);
