import * as yup from 'yup';

// Define the form schema for validation
export const soChiTietCongNoSchema = yup
  .object({
    don_vi: yup.string().required('Bạn chưa nhập đơn vị'),
    ngay_ctu: yup.date().required('Bạn chưa nhập ngày c/từ'),
    so_ctu: yup.string().required('Bạn chưa nhập số c/từ'),
    ngay_hoa_don: yup.date().required('Bạn chưa nhập ngày hóa đơn'),
    so_hoa_don: yup.string().required('Bạn chưa nhập số hóa đơn'),
    tai_khoan: yup.string().required('Bạn chưa nhập tài khoản'),
    tk_doi_ung: yup.string().required('Bạn chưa nhập tk đối ứng'),
    dien_giai: yup.string().required('<PERSON><PERSON><PERSON> chưa nhập diễn g<PERSON>'),
    ps_no: yup.number().required('Bạn chưa nhập ps nợ').min(0, '<PERSON>s nợ không được âm'),
    ps_co: yup.number().required('Bạn chưa nhập ps có').min(0, 'Ps có không được âm'),
    ma_ctu: yup.string().required('Bạn chưa nhập mã c/từ')
  })
  .required();
