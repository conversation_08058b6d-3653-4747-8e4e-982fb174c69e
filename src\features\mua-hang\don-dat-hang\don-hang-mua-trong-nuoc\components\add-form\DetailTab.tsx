import { AritoInputTable } from '@/components/custom/arito/input-table';
import { DetailItemColumns } from './cols-definition';

interface Props {
  value: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'add' | 'edit' | 'view';
}

export const DetailTab = ({ value, onChange, formMode }: Props) => {
  return (
    <div className='h-[500px] w-full'>
      <AritoInputTable
        value={value}
        columns={DetailItemColumns}
        onChange={onChange}
        mode={formMode}
        tableActionButtons={['add', 'delete', 'copy', 'paste', 'moveUp', 'moveDown', 'export', 'pin']}
      />
    </div>
  );
};
