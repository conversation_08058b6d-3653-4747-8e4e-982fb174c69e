import React from 'react';
import { KhoHangSearchColBasicInfo } from '@/components/cac-loai-form/popup-form-type-3/cols-definition';
import BasicInfoTabType3 from '@/components/cac-loai-form/popup-form-type-3/BasicInfoTabType3';
import { NoiNhanColDef, PhuongThucThanhToanColDef } from './cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const DeliveryInfoTab = ({ formMode }: Props) => {
  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <div className='grid grid-cols-[1fr,1fr] gap-x-4'>
        <FormField
          className='max-w-[400px] items-start gap-y-1 sm:items-center'
          label='Điện thoại'
          name='dien_thoai'
          labelClassName='min-w-[100px]'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='max-w-[400px] items-start gap-y-1 sm:items-center'
          label='Fax'
          name='fax'
          labelClassName='min-w-[100px]'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Nơi nhận'
        name='noi_nhan'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/nhan-vien'
        searchColumns={NoiNhanColDef}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Kho nhận'
        name='kho_nhan'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/kho'
        actionButtons={['add', 'edit']}
        searchColumns={KhoHangSearchColBasicInfo}
        headerFields={<BasicInfoTabType3 formMode={formMode} />}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Ph/th th.toán'
        name='ph/th_thanh_toan'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/kho'
        searchColumns={PhuongThucThanhToanColDef}
      />
    </div>
  );
};

export default DeliveryInfoTab;
