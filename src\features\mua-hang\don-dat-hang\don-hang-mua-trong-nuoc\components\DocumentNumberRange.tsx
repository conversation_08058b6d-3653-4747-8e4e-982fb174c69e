import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';

interface DocumentNumberRangeProps {
  fromDocumentNumberName: string;
  toDocumentNumberName: string;
  className?: string;
}

const DocumentNumberRange: React.FC<DocumentNumberRangeProps> = ({
  fromDocumentNumberName,
  toDocumentNumberName,
  className
}) => {
  return (
    <div className={`flex w-full gap-2 ${className}`}>
      <FormField name={fromDocumentNumberName} type='text' />
      <FormField name={toDocumentNumberName} type='text' />
    </div>
  );
};

export default DocumentNumberRange;
