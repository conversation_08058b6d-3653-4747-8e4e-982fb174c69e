import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormProvider, useForm } from 'react-hook-form';
import { createContext, ReactNode, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Tab, Tabs } from '@mui/material';
import { LogOut, Save } from 'lucide-react';
import { z } from 'zod';
import { AritoActionButton, AritoActionBar } from '@/components/custom/arito';
import { headerTabStyle, mainTabStyle } from '../style';
import { FormMode } from '@/types/form';
import { cn } from '@/lib/utils';

export * from '../hooks';

export const AritoFormContext = createContext<any>(null);

export type TabItem = {
  id: string;
  label: string;
  component: ReactNode;
};

export interface AritoFormProps<TFormData extends Record<string, any>> {
  mode?: FormMode;
  hasAritoActionBar?: boolean;
  title?: string;
  subTitle?: string;
  initialData?: Partial<TFormData>;
  onSubmit?: (data: TFormData) => void;
  schema?: z.ZodType<any>;
  headerFields?: ReactNode | TabItem[];
  tabs?: ReactNode | TabItem[];
  onClose?: () => void;
  from?: ReactNode;
  headerBar?: ReactNode;
  bottomBar?: ReactNode;
  actionButtons?: ReactNode;
  className?: string;
  classNameBottomBar?: string;
  classNameHeaderBar?: string;
}

export const AritoForm = <TFormData extends Record<string, any>>({
  mode = 'edit',
  title,
  hasAritoActionBar = true,
  subTitle,
  initialData,
  onSubmit,
  schema,
  headerFields,
  tabs,
  onClose,
  from,
  headerBar,
  bottomBar,
  actionButtons,
  className,
  classNameBottomBar,
  classNameHeaderBar
}: AritoFormProps<TFormData>) => {
  const [activeTab, setActiveTab] = useState<string>(Array.isArray(tabs) ? tabs[0]?.id || '' : '');
  const [activeHeaderTab, setActiveHeaderTab] = useState<string>(
    Array.isArray(headerFields) ? headerFields[0]?.id || '' : ''
  );

  const methods = useForm<TFormData>({
    defaultValues: initialData as DefaultValues<TFormData>,
    mode: 'onSubmit',
    ...(schema && { resolver: zodResolver(schema) })
  });

  const {
    handleSubmit,
    formState: { errors },
    control
  } = methods;

  const isViewMode = mode === 'view';
  const headerHasTabs = Array.isArray(headerFields);
  const tabsHasTabs = Array.isArray(tabs);

  const handleClose = () => {
    onClose?.();
  };

  const onFormSubmit = (data: TFormData) => {
    onSubmit?.(data);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
  };

  const handleHeaderTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveHeaderTab(newValue);
  };

  return (
    <FormProvider {...methods}>
      <AritoFormContext.Provider value={{ control, errors, isViewMode }}>
        <div className={cn('relative flex w-full flex-col overflow-auto bg-white', className)}>
          <div className='flex flex-1 flex-col bg-white'>
            <form onSubmit={handleSubmit(onFormSubmit)} className={'flex flex-1 flex-col'}>
              {hasAritoActionBar && (
                <AritoActionBar
                  inForm={true}
                  className='border-b border-gray-300'
                  titleComponent={
                    <div className='flex flex-col items-center justify-center lg:items-start lg:justify-start'>
                      <h1 className='text-xl font-bold'>{title || (mode === 'add' && 'Thêm')}</h1>
                      <h2 className='text-xs text-gray-500'>{subTitle}</h2>
                    </div>
                  }
                >
                  {actionButtons || (
                    <>
                      {!isViewMode && <AritoActionButton title='Lưu' icon={Save} variant='secondary' type='submit' />}
                      {<AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleClose} />}
                    </>
                  )}
                </AritoActionBar>
              )}

              {headerBar && (
                <div className={cn('w-full border-b border-gray-300', classNameHeaderBar)}>{headerBar}</div>
              )}

              {headerFields && (
                <div className='border-none'>
                  {headerHasTabs && (
                    <>
                      <Box sx={headerTabStyle}>
                        <Tabs
                          value={activeHeaderTab}
                          onChange={handleHeaderTabChange}
                          variant='scrollable'
                          scrollButtons='auto'
                          textColor='primary'
                          indicatorColor='primary'
                        >
                          {(headerFields as TabItem[]).map(tab => (
                            <Tab
                              key={tab.id}
                              value={tab.id}
                              label={tab.label}
                              sx={{
                                textTransform: 'none'
                              }}
                            />
                          ))}
                        </Tabs>
                      </Box>

                      {/* Header Tab Content */}
                      <div>
                        {(headerFields as TabItem[]).map(tab => (
                          <div key={tab.id} className={cn(activeHeaderTab === tab.id ? 'block' : 'hidden')}>
                            {tab.component}
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  {!headerHasTabs && <Box>{headerFields}</Box>}
                </div>
              )}

              {tabs && (
                <>
                  {tabsHasTabs && (
                    <>
                      <Box sx={mainTabStyle}>
                        <Tabs
                          value={activeTab}
                          onChange={handleTabChange}
                          variant='scrollable'
                          scrollButtons='auto'
                          textColor='primary'
                          indicatorColor='primary'
                        >
                          {tabs.map(tab => (
                            <Tab key={tab.id} value={tab.id} label={tab.label} className='normal-case' />
                          ))}
                        </Tabs>
                      </Box>

                      {/* Tab Content */}
                      <div className='w-full'>
                        {tabs.map(tab => (
                          <div
                            key={tab.id}
                            className={cn(
                              activeTab === tab.id ? 'block' : 'hidden',
                              'pb-10' /* Increased padding to ensure content isn't hidden by fixed bottomBar */
                            )}
                          >
                            {tab.component}
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  {!tabsHasTabs && <Box>{tabs}</Box>}
                </>
              )}

              {bottomBar && (
                <div
                  className={cn(
                    'fixed bottom-0 left-0 right-0 z-10 w-full border-t border-gray-200 bg-white',
                    classNameBottomBar
                  )}
                >
                  {bottomBar}
                </div>
              )}
            </form>
          </div>
        </div>
      </AritoFormContext.Provider>
    </FormProvider>
  );
};
