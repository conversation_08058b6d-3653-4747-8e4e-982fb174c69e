import { Lock, Refresh<PERSON>c<PERSON>, Pin, Plus, Edit, Trash } from 'lucide-react';
import { useState } from 'react';
import * as yup from 'yup';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface Props {
  onAuthorization: () => void;
  onRefresh: () => void;
  onPinColumn: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export default function ActionBar({
  className,
  onAuthorization,
  onRefresh,
  onPinColumn,
  onAdd,
  onEdit,
  onDelete,
  isEditDisabled = true
}: Props) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleOpenPopup = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  async function importFile(
    data: { [key: string]: any }[],
    id_column: string,
    doctype: string,
    option: string,
    maxError: number,
    schema: yup.ObjectSchema<any>,
    validation: (
      data: { [key: string]: any }[],
      schema: yup.ObjectSchema<any>
    ) => { type: string; message: string; position: string }[]
  ) {
    let errs = validation(data, schema);
    if (errs.length) {
      console.log(errs);
      return;
    }

    // Placeholder implementation - ERPNext functionality removed
    console.log('Import functionality disabled');
  }

  return (
    <AritoActionBar
      titleComponent={<h1 className='text-xl font-bold'>Khai báo nhóm người sử dụng và phân quyền truy cập</h1>}
    >
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} />
      <AritoActionButton title='Sửa' icon={Edit} onClick={onEdit} disabled={isEditDisabled} />
      <AritoActionButton title='Phân Quyền' icon={Lock} onClick={onAuthorization} />
      <AritoActionButton title='Làm tươi' icon={RefreshCcw} onClick={onRefresh} />
      <AritoActionButton title='Ghim cột' icon={Pin} onClick={onPinColumn} disabled={isEditDisabled} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} disabled={isEditDisabled} />
    </AritoActionBar>
  );
}
