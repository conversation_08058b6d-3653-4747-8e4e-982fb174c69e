import { z } from 'zod';
import { NhaCungCapInput } from '@/types/schemas/nha-cung-cap.type';

export const nhaCungCapSchema = z.object({
  // API required fields
  customer_code: z.string().optional(),
  customer_name: z.string().optional(),
  customer_type: z.string().optional(),
  is_customer: z.boolean().optional(),
  is_vendor: z.boolean().optional(),
  alternative_name: z.string().optional(),
  address: z.string().optional(),
  tax_code: z.string().optional(),
  contact_person: z.string().optional(),
  enterprise_name: z.string().optional(),
  phone: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().optional(),
  website: z.string().optional(),
  legal_representative: z.string().optional(),
  representative_position: z.string().optional(),
  representative: z.string().optional(),
  account: z.string().optional(),
  payment_term: z.string().optional(),
  payment_method: z.string().optional(),
  credit_limit: z.string().optional(),
  bank_account: z.string().optional(),
  bank_name: z.string().optional(),
  bank_branch: z.string().optional(),
  customer_group1: z.string().optional(),
  customer_group2: z.string().optional(),
  customer_group3: z.string().optional(),
  region: z.string().optional(),
  sales_rep: z.string().optional(),
  search_keywords: z.string().optional(),
  province: z.string().optional(),
  notes: z.string().optional(),
  status: z.string().optional(),
  birth_date: z.string().optional(),
  id_number: z.string().optional(),
  description: z.string().optional(),
  delivery_address: z.string().optional(),
  business_field: z.string().optional(),
  use_einvoice: z.string().optional(),
  einvoice_email: z.string().optional(),
  customer_number: z.string().optional(),
  active: z.boolean().optional(),
  hidden: z.boolean().optional(),
  additional_info: z.any().optional(),
  entity_model: z.string().optional()
});

export type NhaCungCapFormData = z.infer<typeof nhaCungCapSchema>;

export const initialNhaCungCapValues: Partial<NhaCungCapFormData> = {
  customer_code: '',
  customer_name: '',
  customer_type: '1',
  is_customer: false,
  is_vendor: true,
  alternative_name: '',
  address: '',
  tax_code: '',
  contact_person: '',
  enterprise_name: '',
  phone: '',
  fax: '',
  email: '',
  website: '',
  legal_representative: '',
  representative_position: '',
  representative: '',
  account: '',
  payment_term: '',
  payment_method: '',
  credit_limit: '',
  bank_account: '',
  bank_name: '',
  bank_branch: '',
  customer_group1: '',
  customer_group2: '',
  customer_group3: '',
  region: '',
  sales_rep: '',
  search_keywords: '',
  province: '',
  notes: '',
  status: 'Active',
  birth_date: '',
  id_number: '',
  description: '',
  delivery_address: '',
  business_field: '',
  use_einvoice: '',
  einvoice_email: '',
  customer_number: '',
  active: true,
  hidden: false
};
