import * as yup from 'yup';
import { z } from 'zod';

// Define the form schema for validation
export const bangKeNhapXuatKhoSchema = yup.object({}).required();

// Define the search form schema using zod
export const searchSchema = z.object({
  ngay_tu: z.string().optional(),
  ngay_den: z.string().optional(),
  ma_kho: z.string().optional(),
  ma_kho_dc: z.string().optional(),
  ma_vat_tu: z.string().optional(),
  ma_lo: z.string().optional(),
  ma_vi_tri: z.string().optional(),
  loai_vat_tu: z.string().optional(),
  chi_xem_vat_tu_co_theo_doi_ton_kho: z.boolean().optional(),
  nhom_vat_tu_1: z.string().optional(),
  nhom_vat_tu_2: z.string().optional(),
  nhom_vat_tu_3: z.string().optional(),
  nhom_theo: z.string().optional(),
  mau_bao_cao: z.string().optional(),
  quy_doi_dvt: z.string().optional(),
  mau_loc_bao_cao: z.string().optional(),
  mau_phan_tich_dl: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_tu: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
  ngay_den: new Date().toISOString().split('T')[0],
  ma_kho: '',
  ma_kho_dc: '',
  ma_vat_tu: '',
  ma_lo: '',
  ma_vi_tri: '',
  loai_vat_tu: '',
  chi_xem_vat_tu_co_theo_doi_ton_kho: false,
  nhom_vat_tu_1: '',
  nhom_vat_tu_2: '',
  nhom_vat_tu_3: '',
  nhom_theo: '0',
  mau_bao_cao: '0',
  quy_doi_dvt: '',
  mau_loc_bao_cao: '0',
  mau_phan_tich_dl: '0'
};
