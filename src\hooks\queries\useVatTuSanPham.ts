import { useState, useEffect, useCallback } from 'react';
import { VatTuSanPham, VatTuSanPhamInput } from '@/types/schemas/vat-tu-san-pham.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseVatTuSanPhamReturn {
  vatTuSanPhams: VatTuSanPham[];
  isLoading: boolean;
  addVatTuSanPham: (newVatTu: VatTuSanPhamInput) => Promise<VatTuSanPham>;
  updateVatTuSanPham: (uuid: string, updatedVatTu: VatTuSanPhamInput) => Promise<VatTuSanPham>;
  deleteVatTuSanPham: (uuid: string) => Promise<void>;
  refreshVatTuSanPhams: () => Promise<void>;
  getVatTuSanPhamById: (uuid: string) => Promise<VatTuSanPham | null>;
  searchVatTuSanPham: (searchTerm: string) => Promise<VatTuSanPham[]>;
  getVatTuSanPhamByCode: (code: string) => Promise<VatTuSanPham | null>;
  getVatTuSanPhamsByFilter: (filters: Record<string, any>) => Promise<VatTuSanPham[]>;
}

/**
 * Hook for managing VatTuSanPham (Product/Material) data
 *
 * This hook provides functions to fetch, create, update, and delete products/materials.
 * It also includes advanced search and filtering capabilities.
 */
export const useVatTuSanPham = (initialVatTus: VatTuSanPham[] = []): UseVatTuSanPhamReturn => {
  const [vatTuSanPhams, setVatTuSanPhams] = useState<VatTuSanPham[]>(initialVatTus);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  /**
   * Fetch all products/materials
   */
  const fetchVatTuSanPhams = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get(`/entities/${entity.slug}/erp/materials/`);
      setVatTuSanPhams(response.data.results || []);
    } catch (error) {
      console.error('Error fetching products/materials:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  /**
   * Get a product/material by its UUID
   */
  const getVatTuSanPhamById = async (uuid: string): Promise<VatTuSanPham | null> => {
    if (!entity?.slug) return null;

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/materials/${uuid}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product/material with ID ${uuid}:`, error);
      return null;
    }
  };

  /**
   * Get a product/material by its code
   */
  const getVatTuSanPhamByCode = async (code: string): Promise<VatTuSanPham | null> => {
    if (!entity?.slug || !code) return null;

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/materials/`, {
        params: { ma_vt: code }
      });

      const results = response.data.results || [];
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error(`Error fetching product/material with code ${code}:`, error);
      return null;
    }
  };

  /**
   * Search for products/materials by a search term
   */
  const searchVatTuSanPham = async (searchTerm: string): Promise<VatTuSanPham[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/materials/search/`, {
        params: { q: searchTerm }
      });
      return response.data.results || [];
    } catch (error) {
      console.error('Error searching products/materials:', error);
      return [];
    }
  };

  /**
   * Get products/materials by filter criteria
   */
  const getVatTuSanPhamsByFilter = async (filters: Record<string, any>): Promise<VatTuSanPham[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/materials/`, {
        params: filters
      });
      return response.data.results || [];
    } catch (error) {
      console.error('Error filtering products/materials:', error);
      return [];
    }
  };

  /**
   * Add a new product/material
   */
  const addVatTuSanPham = async (newVatTu: VatTuSanPhamInput): Promise<VatTuSanPham> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      // Prepare data before sending to API
      const payload = prepareDataForApi(newVatTu);

      const response = await api.post(`/entities/${entity.slug}/erp/materials/`, payload);

      const addedVatTu = response.data;
      setVatTuSanPhams(prev => [...prev, addedVatTu]);
      return addedVatTu;
    } catch (error) {
      console.error('Error adding product/material:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update an existing product/material
   */
  const updateVatTuSanPham = async (uuid: string, updatedVatTu: VatTuSanPhamInput): Promise<VatTuSanPham> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      // Prepare data before sending to API
      const payload = prepareDataForApi(updatedVatTu);

      const response = await api.patch(`/entities/${entity.slug}/erp/materials/${uuid}/`, payload);

      const updatedItem = response.data;
      setVatTuSanPhams(prev => prev.map(item => (item.uuid === uuid ? updatedItem : item)));
      return updatedItem;
    } catch (error) {
      console.error('Error updating product/material:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete a product/material
   */
  const deleteVatTuSanPham = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/materials/${uuid}/`);
      setVatTuSanPhams(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting product/material:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Helper function to prepare data before sending to API
   */
  function prepareDataForApi(data: any): any {
    const payload: any = { ...data };

    // Process foreign keys - convert from object to UUID or code
    const foreignKeyFields = [
      { field: 'don_vi_tinh', objField: 'don_vi_tinh_obj' },
      { field: 'nh_vt1', objField: 'nh_vt1_obj' },
      { field: 'nh_vt2', objField: 'nh_vt2_obj' },
      { field: 'nh_vt3', objField: 'nh_vt3_obj' },
      { field: 'ma_kho', objField: 'ma_kho_obj' },
      { field: 'ma_vi_tri', objField: 'ma_vi_tri_obj' },
      { field: 'ma_thue', objField: 'ma_thue_obj' },
      { field: 'ma_thue_nk', objField: 'ma_thue_nk_obj' },
      { field: 'nuoc_sx', objField: 'nuoc_sx_obj' },
      { field: 'mau_sac', objField: 'mau_sac_obj' },
      { field: 'kich_co', objField: 'kich_co_obj' },
      { field: 'ma_bp', objField: 'ma_bp_obj' }
    ];

    foreignKeyFields.forEach(({ field, objField }) => {
      if (payload[objField]) {
        payload[field] = payload[objField].uuid;
        delete payload[objField];
      }
    });

    // Process account fields
    const accountFields = ['tk_vt', 'tk_dt', 'tk_gv', 'tk_ck', 'tk_km', 'tk_tl', 'tk_spdd', 'tk_cpnvl'];

    accountFields.forEach(field => {
      const objField = `${field}_obj`;
      if (payload[objField]) {
        payload[field] = payload[objField].uuid;
        delete payload[objField];
      }
    });

    // Process secondary units of measure
    if (payload.dvt && Array.isArray(payload.dvt)) {
      payload.dvt = payload.dvt.map((item: any) => {
        const result: any = { ...item };
        if (result.ma_dvt_obj) {
          result.ma_dvt = result.ma_dvt_obj.uuid;
          delete result.ma_dvt_obj;
        }
        return result;
      });
    }

    return payload;
  }

  // Fetch data on component mount or when entity changes
  useEffect(() => {
    fetchVatTuSanPhams();
  }, [entity?.slug, fetchVatTuSanPhams]);

  return {
    vatTuSanPhams,
    isLoading,
    addVatTuSanPham,
    updateVatTuSanPham,
    deleteVatTuSanPham,
    refreshVatTuSanPhams: fetchVatTuSanPhams,
    getVatTuSanPhamById,
    searchVatTuSanPham,
    getVatTuSanPhamByCode,
    getVatTuSanPhamsByFilter
  };
};
