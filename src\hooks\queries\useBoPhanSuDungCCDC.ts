import { useState, useEffect } from 'react';
import {
  BoPhanSuDungCCDC,
  BoPhanSuDungCCDCInput,
  BoPhanSuDungCCDCResponse
} from '@/types/schemas/bo-phan-su-dung-ccdc.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseBoPhanSuDungCCDCReturn {
  boPhanSuDungCCDCs: BoPhanSuDungCCDC[];
  isLoading: boolean;
  addBoPhanSuDungCCDC: (newDepartment: BoPhanSuDungCCDCInput) => Promise<void>;
  updateBoPhanSuDungCCDC: (uuid: string, updatedDepartment: BoPhanSuDungCCDCInput) => Promise<void>;
  deleteBoPhanSuDungCCDC: (uuid: string) => Promise<void>;
  refreshBoPhanSuDungCCDCs: () => Promise<void>;
  getBoPhanSuDungCCDCByCode: (code: string) => Promise<BoPhanSuDungCCDC | null>;
  getActiveBoPhanSuDungCCDCs: () => Promise<BoPhanSuDungCCDC[]>;
}

/**
 * Hook for managing BoPhanSuDungCCDC (Departments for Tool and Equipment Usage) data
 *
 * This hook provides functions to fetch, create, update, and delete departments for tool and equipment usage.
 */
export const useBoPhanSuDungCCDC = (initialBoPhanSuDungCCDCs: BoPhanSuDungCCDC[] = []): UseBoPhanSuDungCCDCReturn => {
  const [boPhanSuDungCCDCs, setBoPhanSuDungCCDCs] = useState<BoPhanSuDungCCDC[]>(initialBoPhanSuDungCCDCs);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchBoPhanSuDungCCDCs = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<BoPhanSuDungCCDCResponse>(`/entities/${entity.slug}/erp/bo-phan-su-dung-ccdc/`);
      setBoPhanSuDungCCDCs(response.data.results);
    } catch (error) {
      console.error('Error fetching departments for tool and equipment usage:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getBoPhanSuDungCCDCByCode = async (code: string): Promise<BoPhanSuDungCCDC | null> => {
    if (!entity?.slug) return null;

    setIsLoading(true);
    try {
      const response = await api.get<BoPhanSuDungCCDCResponse>(
        `/entities/${entity.slug}/erp/bo-phan-su-dung-ccdc/?ma_bp=${code}`
      );

      if (response.data.results.length > 0) {
        return response.data.results[0];
      }
      return null;
    } catch (error) {
      console.error('Error fetching department for tool and equipment usage by code:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const getActiveBoPhanSuDungCCDCs = async (): Promise<BoPhanSuDungCCDC[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<BoPhanSuDungCCDCResponse>(
        `/entities/${entity.slug}/erp/bo-phan-su-dung-ccdc/?active=true`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching active departments for tool and equipment usage:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const addBoPhanSuDungCCDC = async (newDepartment: BoPhanSuDungCCDCInput): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.post(`/entities/${entity.slug}/erp/bo-phan-su-dung-ccdc/`, newDepartment);
      await fetchBoPhanSuDungCCDCs();
    } catch (error) {
      console.error('Error adding department for tool and equipment usage:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateBoPhanSuDungCCDC = async (uuid: string, updatedDepartment: BoPhanSuDungCCDCInput): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.patch(`/entities/${entity.slug}/erp/bo-phan-su-dung-ccdc/${uuid}/`, updatedDepartment);
      await fetchBoPhanSuDungCCDCs();
    } catch (error) {
      console.error('Error updating department for tool and equipment usage:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteBoPhanSuDungCCDC = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/bo-phan-su-dung-ccdc/${uuid}/`);
      setBoPhanSuDungCCDCs(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting department for tool and equipment usage:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBoPhanSuDungCCDCs();
  }, [entity?.slug]);

  return {
    boPhanSuDungCCDCs,
    isLoading,
    addBoPhanSuDungCCDC,
    updateBoPhanSuDungCCDC,
    deleteBoPhanSuDungCCDC,
    refreshBoPhanSuDungCCDCs: fetchBoPhanSuDungCCDCs,
    getBoPhanSuDungCCDCByCode,
    getActiveBoPhanSuDungCCDCs
  };
};
