import { FormField } from '@/components/custom/arito/form/form-field';

export const TaxTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='px-4'>
    <div className='flex flex-col justify-between lg:flex-row'>
      {/* Left column */}
      <div className='w-2/3 space-y-1'>
        {/* Mẫu báo cáo */}
        <FormField
          label='Mẫu báo cáo'
          className='items-center lg:grid lg:grid-cols-[140px,1fr]'
          name='reportTemplate'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 3, label: '3. Hoá đơn giá trị gia tăng' },
            { value: 4, label: '4. H<PERSON>ng hoá, dịch vụ mua vào không có hoá đơn' },
            { value: 5, label: '5. Hoá đơn bán hàng thông thường' }
          ]}
        />

        {/* <PERSON><PERSON> tính chất */}
        <FormField
          label='Mã tính chất'
          className='items-center lg:grid lg:grid-cols-[140px,1fr]'
          name='natureCode'
          type='select'
          disabled={formMode === 'view'}
          options={[
            {
              value: 1,
              label:
                '1. Hàng hóa, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hóa, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế'
            },
            {
              value: 2,
              label: '2. Hàng hóa, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT'
            },
            {
              value: 3,
              label: '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế'
            },
            {
              value: 4,
              label: '4. Hàng hóa, dịch vụ không đủ điều kiện khấu trừ'
            },
            {
              value: 5,
              label: '5. Hàng hóa, dịch vụ không phải tổng hợp trên tờ khai 01/GTGT'
            }
          ]}
        />

        {/* Mã thuế */}
        <FormField
          label='Mã thuế'
          className='items-center lg:grid lg:grid-cols-[140px,1fr]'
          name='taxCode'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />

        {/* TK thuế */}
        <FormField
          label='TK thuế'
          className='items-center lg:grid lg:grid-cols-[140px,1fr]'
          name='taxAccount'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />

        {/* Cục thuế */}
        <FormField
          label='Cục thuế'
          className='items-center lg:grid lg:grid-cols-[140px,1fr]'
          name='taxDepartment'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />

        {/* Nhóm sản phẩm */}
        <FormField
          label='Nhóm sản phẩm'
          className='items-center lg:grid lg:grid-cols-[140px,1fr]'
          name='productGroup'
          type='text'
          disabled={formMode === 'view'}
        />

        {/* Ghi chú */}
        <FormField
          label='Ghi chú'
          className='items-center lg:grid lg:grid-cols-[140px,1fr]'
          name='note'
          type='text'
          disabled={formMode === 'view'}
        />

        {/* Kê thuế đầu ra */}
        <FormField
          label='Kê thuế đầu ra'
          className='items-center lg:grid lg:grid-cols-[300px,1fr]'
          name='outputTax'
          type='checkbox'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
