import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { ToolEquipmentChangeReasonFormValues, toolEquipmentChangeReasonSchema } from '../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { BasicInfoTab } from './BasicInfoTab';
import ConfirmDialog from './ConfirmDialog';

interface CopyDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: ToolEquipmentChangeReasonFormValues) => void;
}

const CopyDialog = ({ open, onClose, onSubmit }: CopyDialogProps) => {
  const [confirm, setConfirm] = useState<boolean>(false);

  // Define default initial values
  const initialValues: ToolEquipmentChangeReasonFormValues = {
    loai_tg_cc: '1',
    ma_tg_cc: '',
    ten_tg_cc: '',
    ten_tg_cc2: '',
    status: '1',
    action: 'CUSTOM',
    param: 'MANUAL'
  };

  const handleSubmit = (data: ToolEquipmentChangeReasonFormValues) => {
    onSubmit(data);
    onClose();
  };

  // Handle closing the dialog
  const handleClose = () => {
    setConfirm(true);
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={() => setConfirm(true)}
        title={'Sao chép'}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
        actions={
          <>
            <Button type='submit' variant='contained' color='primary' className='bg-teal-600 hover:bg-teal-700'>
              Đồng ý
            </Button>
            <Button onClick={() => setConfirm(true)} variant='outlined' color='primary'>
              Hủy
            </Button>
          </>
        }
      >
        <AritoForm
          mode={'add'}
          hasAritoActionBar={false}
          schema={toolEquipmentChangeReasonSchema}
          onSubmit={handleSubmit}
          initialData={initialValues}
          className='w-full'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
              <BasicInfoTab formMode={'add'} />
            </div>
          }
        />
      </AritoDialog>

      {confirm && (
        <ConfirmDialog
          open={confirm}
          onClose={() => setConfirm(false)}
          onConfirm={() => {
            setConfirm(false);
            onClose();
          }}
          title='Xác nhận hủy'
          message='Bạn có chắc chắn muốn hủy thao tác này? Dữ liệu đã nhập sẽ không được lưu.'
        />
      )}
    </>
  );
};

export default CopyDialog;
