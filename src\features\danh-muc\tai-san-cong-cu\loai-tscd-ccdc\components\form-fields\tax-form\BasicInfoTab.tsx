'use client';

import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AccountModel, Phi } from '@/types/schemas';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
  tkTs: AccountModel | null;
  tkKh: AccountModel | null;
  tkCp: AccountModel | null;
  maPhi: Phi | null;
  setTkTs: (account: AccountModel) => void;
  setTkKh: (account: AccountModel) => void;
  setTkCp: (account: AccountModel) => void;
  setMaPhi: (phi: Phi) => void;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({
  formMode,
  tkTs,
  tkKh,
  tkCp,
  maPhi,
  setTkTs,
  setTkKh,
  setTkCp,
  setMaPhi
}) => {
  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='space-y-2 p-4'>
        <div className='space-y-1'>
          {/* 1. Tài sản/Công cụ */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tài sản/Công cụ</Label>
            <div className='w-64'>
              <FormField
                name='loai_tscc'
                type='select'
                options={[
                  { value: '1', label: '1. Tài sản' },
                  { value: '2', label: '2. Công cụ' }
                ]}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
          {/* 2. Mã loại */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã loại</Label>
            <FormField type='text' name='ma_lts' className='w-64' disabled={formMode === 'view'} />
          </div>
          {/* 3. Tên loại */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tên loại</Label>
            <FormField type='text' name='ten_lts' className='w-96' disabled={formMode === 'view'} />
          </div>
          {/* 4. Tên khác */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tên khác</Label>
            <FormField type='text' name='ten_lts2' className='w-96' disabled={formMode === 'view'} />
          </div>
          {/* 5. Tài khoản */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tài khoản</Label>
            <SearchField<AccountModel>
              value={tkTs?.code || undefined}
              columnDisplay='code'
              displayRelatedField='name'
              searchEndpoint='/accounts/'
              disabled={formMode === 'view'}
              searchColumns={[
                { field: 'code', headerName: 'Mã tài khoản', width: 150 },
                { field: 'name', headerName: 'Tên tài khoản', width: 300 }
              ]}
              dialogTitle='Danh mục tài khoản'
              className='w-64'
              onRowSelection={setTkTs}
            />
          </div>
          {/* 6. Tk khấu hao */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tk khấu hao</Label>
            <SearchField<AccountModel>
              value={tkKh?.code || undefined}
              columnDisplay='code'
              displayRelatedField='name'
              searchEndpoint='/accounts/'
              disabled={formMode === 'view'}
              searchColumns={[
                { field: 'code', headerName: 'Mã tài khoản', width: 150 },
                { field: 'name', headerName: 'Tên tài khoản', width: 300 }
              ]}
              dialogTitle='Danh mục tài khoản'
              className='w-64'
              onRowSelection={setTkKh}
            />
          </div>
          {/* 7. Tk chi phí */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tk chi phí</Label>
            <SearchField<AccountModel>
              value={tkCp?.code || undefined}
              columnDisplay='code'
              displayRelatedField='name'
              searchEndpoint='/accounts/'
              disabled={formMode === 'view'}
              searchColumns={[
                { field: 'code', headerName: 'Mã tài khoản', width: 150 },
                { field: 'name', headerName: 'Tên tài khoản', width: 300 }
              ]}
              dialogTitle='Danh mục tài khoản'
              className='w-64'
              onRowSelection={setTkCp}
            />
          </div>
          {/* 8. Mã phí */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã phí</Label>
            <SearchField<Phi>
              value={maPhi?.ma_phi || ''}
              columnDisplay='ma_phi'
              displayRelatedField='ten_phi'
              searchEndpoint='/fees/'
              disabled={formMode === 'view'}
              searchColumns={[
                { field: 'ma_phi', headerName: 'Mã phí', width: 150 },
                { field: 'ten_phi', headerName: 'Tên phí', width: 300 }
              ]}
              dialogTitle='Danh mục phí'
              className='w-64'
              onRowSelection={setMaPhi}
            />
          </div>
          {/* 9. Trạng thái */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Trạng thái</Label>
            <div className='w-64'>
              <FormField
                name='status'
                type='select'
                options={[
                  { value: '1', label: '1. Còn sử dụng' },
                  { value: '0', label: '0. Không sử dụng' }
                ]}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
