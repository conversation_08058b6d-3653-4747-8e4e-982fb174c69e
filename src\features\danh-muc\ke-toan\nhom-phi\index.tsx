'use client';
import { ChevronRight } from 'lucide-react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useSidebarAndFilter } from './hooks/useSidebarAndFilter';
import { exportMainColumns, groupTypes } from './cols-definition';
import FeeGroupDialog from './components/feegroup-dialog/index';
import { useRowSelection } from './hooks/useRowSelection';
import { useDialogState } from './hooks/useDialogState';
import DeleteDialog from './components/DeleteDialog';
import { useGroup } from '@/hooks/queries/useGroup';
import { ActionBar } from './components/ActionBar';
import { Sidebar } from './components/SideBar';
import { GroupType } from '@/types/schemas';

export default function FeeGroupPage() {
  const { groups, isLoading, refreshGroups, addGroup, updateGroup, deleteGroup, fetchGroupsByType } = useGroup(
    GroupType.FEE1
  );
  const { selectedObj, handleRowClick, clearSelection } = useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  } = useDialogState(clearSelection);

  const { showSidebar, activeGroup, toggleSidebar, handleFilterChange } = useSidebarAndFilter({
    fetchGroupsByType
  });

  const tables = [
    {
      name: 'Tất cả',
      rows: groups,
      columns: exportMainColumns
    }
  ];
  return (
    <div className='flex h-screen w-screen flex-col overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <FeeGroupDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedObj && showEditDialog
              ? {
                  ma_nhom: selectedObj.ma_nhom,
                  ten_phan_nhom: selectedObj.ten_phan_nhom,
                  ten2: selectedObj.ten2,
                  trang_thai: parseInt(selectedObj.trang_thai)
                }
              : selectedObj && showAddDialog && isCopyMode
                ? {
                    ma_nhom: selectedObj.ma_nhom,
                    ten_phan_nhom: selectedObj.ten_phan_nhom,
                    ten2: selectedObj.ten2,
                    trang_thai: parseInt(selectedObj.trang_thai)
                  }
                : undefined
          }
          onClose={showEditDialog ? closeEditDialog : closeAddDialog}
          selectedObj={showEditDialog ? selectedObj : null}
          addFeeGroup={addGroup}
          updateFeeGroup={updateGroup}
          activeGroupType={activeGroup}
        />
      )}

      {showDeleteDialog && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          selectedObj={selectedObj}
          deleteFeeGroup={deleteGroup}
          clearSelection={clearSelection}
        />
      )}

      {showWatchDialog && selectedObj && (
        <FeeGroupDialog
          open={showWatchDialog}
          mode='view'
          initialData={{
            ma_nhom: selectedObj.ma_nhom,
            ten_phan_nhom: selectedObj.ten_phan_nhom,
            ten2: selectedObj.ten2,
            trang_thai: parseInt(selectedObj.trang_thai)
          }}
          onClose={closeWatchDialog}
          selectedObj={selectedObj}
          addFeeGroup={addGroup}
          updateFeeGroup={updateGroup}
          onAddButtonClick={handleAddButtonClick}
          onEditButtonClick={handleEditButtonClick}
          onDeleteButtonClick={handleDeleteButtonClick}
          onCopyButtonClick={handleCopyButtonClick}
          activeGroupType={activeGroup}
        />
      )}

      <div className='flex h-full overflow-hidden'>
        <Sidebar
          activeGroup={activeGroup}
          onFilterChange={handleFilterChange}
          groupTypes={groupTypes}
          isOpen={showSidebar}
          toggleSidebar={toggleSidebar}
        />

        <div className='flex flex-1 flex-col overflow-hidden'>
          <div className='flex shrink-0 items-center border-b bg-white p-2'>
            <div className='flex w-full'>
              {!showSidebar && (
                <div className='mr-2 flex items-center'>
                  <button
                    onClick={toggleSidebar}
                    className='flex-shrink-0 rounded-full bg-gray-200 p-1 text-gray-500 hover:text-gray-700'
                  >
                    <ChevronRight className='h-5 w-5' />
                  </button>
                </div>
              )}
              <ActionBar
                onAddClick={openAddDialog}
                onEditClick={() => selectedObj && openEditDialog()}
                onDeleteClick={() => selectedObj && openDeleteDialog()}
                onCopyClick={() => selectedObj && handleCopyButtonClick()}
                onRefreshClick={refreshGroups}
                onShowSidebar={toggleSidebar}
                onViewClick={() => selectedObj && openWatchDialog()}
              />
            </div>
          </div>

          <div className='w-full flex-1 overflow-auto'>
            {isLoading && (
              <div className='flex h-64 items-center justify-center'>
                <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
              </div>
            )}

            {!isLoading && (
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.uuid || undefined}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
