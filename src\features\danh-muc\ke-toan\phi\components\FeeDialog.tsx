import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { FeeFormattedData, searchSchema, SearchFormValues } from '../schemas';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog, AritoForm } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';
import { Phi } from '@/types/schemas';
import FeeForm from './FeeForm';

interface FeeDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: SearchFormValues;
  onClose: () => void;
  selectedObj: Phi | null;
  addFee: (data: FeeFormattedData) => Promise<void>;
  updateFee: (uuid: string, data: FeeFormattedData) => Promise<void>;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

function FeeDialog({
  open,
  mode,
  initialData,
  onClose,
  selectedObj,
  addFee,
  updateFee,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: FeeDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleSubmit = async (data: SearchFormValues) => {
    try {
      const formattedData: FeeFormattedData = {
        ma_phi: data.ma_phi,
        ten_phi: data.ten_phi,
        ten_khac: data.ten_khac || null,
        nhom_phi_1: data.nhom_phi_1 || null,
        nhom_phi_2: data.nhom_phi_2 || null,
        nhom_phi_3: data.nhom_phi_3 || null,
        bo_phan: data.bo_phan || null,
        trang_thai: typeof data.trang_thai === 'number' ? String(data.trang_thai) : data.trang_thai
      };

      if (mode === 'edit' && selectedObj) {
        await updateFee(selectedObj.uuid, formattedData);
      } else if (mode === 'add') {
        await addFee(formattedData);
      }

      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const title = mode === 'add' ? 'Thêm phí' : mode === 'edit' ? 'Sửa phí' : 'Xem phí';

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={mode === 'add' ? 1 : mode === 'edit' ? 2 : 3} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={searchSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<FeeForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}

              {mode !== 'view' && (
                <div className='flex justify-end gap-2 p-4'>
                  <Button variant='outlined' onClick={onClose}>
                    Hủy
                  </Button>
                  <Button variant='contained' color='primary' type='submit'>
                    Lưu
                  </Button>
                </div>
              )}
            </>
          }
        />
      </AritoDialog>

      <ConfirmDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={() => {
          setShowConfirmDialog(false);
          onClose();
        }}
        title='Xác nhận'
        message='Bạn có chắc chắn muốn lưu thông tin này?'
      />
    </>
  );
}

export default FeeDialog;
