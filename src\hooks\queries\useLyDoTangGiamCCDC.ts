import { useState, useEffect } from 'react';
import {
  LyDoTangGiamCCDC,
  LyDoTangGiamCCDCInput,
  LyDoTangGiamCCDCResponse
} from '@/types/schemas/ly-do-tang-giam-ccdc.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseLyDoTangGiamCCDCReturn {
  toolEquipmentChangeReasons: LyDoTangGiamCCDC[];
  isLoading: boolean;
  addToolEquipmentChangeReason: (newReason: LyDoTangGiamCCDCInput) => Promise<void>;
  updateToolEquipmentChangeReason: (uuid: string, updatedReason: LyDoTangGiamCCDCInput) => Promise<void>;
  deleteToolEquipmentChangeReason: (uuid: string) => Promise<void>;
  refreshToolEquipmentChangeReasons: () => Promise<void>;
}

export const useLyDoTangGiamCCDC = (initialReasons: LyDoTangGiamCCDC[] = []): UseLyDoTangGiamCCDCReturn => {
  const [toolEquipmentChangeReasons, setToolEquipmentChangeReasons] = useState<LyDoTangGiamCCDC[]>(initialReasons);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchToolEquipmentChangeReasons = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<LyDoTangGiamCCDCResponse>(
        `/entities/${entity.slug}/erp/tool-equipment-change-reasons/`
      );

      setToolEquipmentChangeReasons(response.data.results);
    } catch (error) {
      console.error('Error fetching tool equipment change reasons:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addToolEquipmentChangeReason = async (newReason: LyDoTangGiamCCDCInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.post(`/entities/${entity.slug}/erp/tool-equipment-change-reasons/`, {
        action: newReason.action,
        param: newReason.param || null,
        loai_tg_cc: newReason.loai_tg_cc,
        ma_tg_cc: newReason.ma_tg_cc,
        ten_tg_cc: newReason.ten_tg_cc,
        ten_tg_cc2: newReason.ten_tg_cc2,
        status: newReason.status
      });

      const addedReason: LyDoTangGiamCCDC = response.data;
      setToolEquipmentChangeReasons(prev => [...prev, addedReason]);
    } catch (error) {
      console.error('Error adding tool equipment change reason:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateToolEquipmentChangeReason = async (uuid: string, updatedReason: LyDoTangGiamCCDCInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.patch(`/entities/${entity.slug}/erp/tool-equipment-change-reasons/${uuid}/`, {
        action: updatedReason.action,
        param: updatedReason.param || null,
        loai_tg_cc: updatedReason.loai_tg_cc,
        ma_tg_cc: updatedReason.ma_tg_cc,
        ten_tg_cc: updatedReason.ten_tg_cc,
        ten_tg_cc2: updatedReason.ten_tg_cc2,
        status: updatedReason.status
      });

      // Update the reason in the list
      const updatedReasonData: LyDoTangGiamCCDC = response.data;

      setToolEquipmentChangeReasons(prev =>
        prev.map(reason => (reason.uuid === updatedReasonData.uuid ? updatedReasonData : reason))
      );
    } catch (error) {
      console.error('Error updating tool equipment change reason:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteToolEquipmentChangeReason = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/tool-equipment-change-reasons/${uuid}/`);

      // Remove the reason from the list
      setToolEquipmentChangeReasons(prev => prev.filter(reason => reason.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting tool equipment change reason:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchToolEquipmentChangeReasons();
  }, [entity?.slug]);

  return {
    toolEquipmentChangeReasons,
    isLoading,
    addToolEquipmentChangeReason,
    updateToolEquipmentChangeReason,
    deleteToolEquipmentChangeReason,
    refreshToolEquipmentChangeReasons: fetchToolEquipmentChangeReasons
  };
};
