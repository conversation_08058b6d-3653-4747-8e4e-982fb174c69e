import { GridColDef } from '@mui/x-data-grid';
import AritoCheckboxCellRenderer from '@/components/custom/arito/cell-renderers/arito-checkbox-cell-renderer';

export const exportMainColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'trang_thai', headerName: 'Trạng thái', flex: 1 },
  { field: 'so_ctu', headerName: 'Số c/từ', flex: 1 },
  { field: 'ngay_ctu', headerName: 'Ngày c/từ', flex: 1 },
  { field: 'ma_ncc', headerName: 'Mã nhà cung cấp', flex: 1 },
  { field: 'ten_ncc', headerName: 'Tên nhà cung cấp', flex: 2 },
  { field: 'dien_giai', headerName: '<PERSON><PERSON><PERSON> g<PERSON>', flex: 2 },
  { field: 'tong_tien', headerName: 'Tổng tiền', flex: 1 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', flex: 1 },
  { field: 'loai_ctu', headerName: 'Loại chứng từ', flex: 1 }
];

export const exportBottomColumns: GridColDef[] = [
  { field: 'ma_sp', headerName: 'Mã sản phẩm', flex: 1 },
  { field: 'ten_sp', headerName: 'Tên sản phẩm', flex: 2 },
  { field: 'dvt', headerName: 'Đvt', flex: 1 },
  { field: 'so_luong', headerName: 'Số lượng', flex: 1 },
  { field: 'thue_suat', headerName: 'Thuế suất', flex: 1 },
  { field: 'thue_suat_phan_tram', headerName: 'Thuế suất(%)', flex: 1 }
];

export const exportDetailItemColumns: GridColDef[] = [
  { field: 'ma_sp', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_sp', headerName: 'Tên sản phẩm', width: 150 },
  { field: 'dvt', headerName: 'Đvt', width: 150 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 150 },
  { field: 'so_luong', headerName: 'Số lượng', width: 150 },
  { field: 'gia_vnd', headerName: 'Giá VND', width: 150 },
  { field: 'tien_hang_vnd', headerName: 'Tiền hàng VND', width: 150 },
  { field: 'chi_phi_vnd', headerName: 'Chi phí VND', width: 150 },
  { field: 'thue_suat', headerName: 'Thuế suất', width: 150 },
  { field: 'thue_gtgt_vnd', headerName: 'Thuế GTGT VND', width: 150 },
  { field: 'tk_no', headerName: 'Tk nợ', width: 150 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 150 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 150 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 150 },
  { field: 'cp_khong_hop_le', headerName: 'Chi phí không hợp lệ', width: 150 }
];

export const SoChungTuSearchColBasicInfo: GridColDef[] = [
  { field: 'ma_quyen', headerName: 'Mã quyển', flex: 1 },
  { field: 'ten_quyen', headerName: 'Tên quyển', flex: 2 },
  { field: 'Số khai báo', headerName: 'Số khai báo', flex: 1 }
];

export const NhanVienColDef: GridColDef[] = [
  { field: 'ma_nv', headerName: 'Mã nhân viên', flex: 1 },
  { field: 'ten_nv', headerName: 'Tên nhân viên', flex: 2 }
];
