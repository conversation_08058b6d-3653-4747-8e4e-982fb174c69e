import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { Phi } from '@/types/schemas';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: Phi | null;
  deleteFee: (uuid: string) => Promise<void>;
  clearSelection: () => void;
}

function DeleteDialog({ onClose, open, selectedObj, deleteFee, clearSelection }: DeleteDialogProps) {
  const handleDelete = async () => {
    if (selectedObj) {
      try {
        await deleteFee(selectedObj.uuid);
        clearSelection();
        onClose();
      } catch (error) {
        console.error('Error deleting fee:', error);
      }
    }
  };
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Xóa dữ liệu'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            onClick={handleDelete}
            type='submit'
            form='search-form'
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <p className='min-w-[40vw] p-4 text-base font-medium'>Bạn có chắc chắn xóa không?</p>
    </AritoDialog>
  );
}

export default DeleteDialog;
