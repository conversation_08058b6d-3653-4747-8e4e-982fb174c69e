import { GridEventListener } from '@mui/x-data-grid';
import { useState } from 'react';
import { Phi } from '@/types/schemas';

interface UseRowSelectionReturn {
  selectedObj: Phi | null;
  selectedRowIndex: string | null;
  handleRowClick: GridEventListener<'rowClick'>;
  clearSelection: () => void;
}

export const useRowSelection = (): UseRowSelectionReturn => {
  const [selectedObj, setSelectedObj] = useState<Phi | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const handleRowClick = (params: any) => {
    setSelectedRowIndex(params.id.toString());
    setSelectedObj(params.row);
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection
  };
};
