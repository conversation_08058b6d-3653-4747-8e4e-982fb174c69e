import { warehouseReceiptItemColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface WarehouseReceiptDetailItemsTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const WarehouseReceiptItemsTab = ({ value, onChange, formMode }: WarehouseReceiptDetailItemsTabProps) => {
  return (
    <div className='h-[270px] w-screen'>
      <AritoInputTable value={value} columns={warehouseReceiptItemColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
