'use client';
import Split from 'react-split';
import { filter } from 'lodash';
import {
  exportCreditAdviceDetailColumns,
  getChangingValueCreditAdviceColumns
} from '@/features/tien-gui/hach-toan/giay-bao-co/cols-definition';

import { AritoColoredDot, AritoInputTable, AritoDataTables } from '@/components/custom/arito';
import { useFormState, useGiayBaoCoData } from './hooks';
import { calculateTotals } from './utils/Caculate';
import { filterValues } from './types/filterTabs';
import { ActionBar, AddForm } from './components';

export default function GiayBaoCoPage() {
  // Use custom hooks for state management
  const {
    showForm,
    formMode,
    selectedObj,
    currentObj,
    inputDetails,
    setSelectedObj,
    setInputDetails,
    handleOpenViewForm,
    handleOpenAddForm,
    handleOpenEditForm,
    handleCloseForm
  } = useFormState();

  const { rows, createHandleRowClick, handleFormSubmit } = useGiayBaoCoData();

  // Create the handleRowClick function for row selection only (not for bottom table)
  const handleRowClick = (params: any) => {
    setSelectedObj(params.row);
    // Don't set inputDetails since bottom table should be empty
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getChangingValueCreditAdviceColumns(handleOpenViewForm, () => {})
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: getChangingValueCreditAdviceColumns(handleOpenViewForm, () => {}),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: rows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: getChangingValueCreditAdviceColumns(handleOpenViewForm, () => {}),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col'>
      {showForm ? (
        <AddForm
          formMode={formMode}
          currentObj={currentObj}
          inputDetails={inputDetails}
          setInputDetails={setInputDetails}
          handleFormSubmit={handleFormSubmit}
          handleCloseForm={handleCloseForm}
          calculateTotals={calculateTotals}
        />
      ) : (
        <>
          <ActionBar
            onPrintClick={() => console.log('Print clicked')}
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={() => console.log('Delete clicked')}
            onCopyClick={() => console.log('Copy clicked')}
            onSearchClick={() => console.log('Search clicked')}
            onRefreshClick={() => console.log('Refresh clicked')}
            onFixedColumnsClick={() => console.log('Fixed columns clicked')}
            onPrintMultipleClick={() => console.log('Print multiple clicked')}
            onExportDataClick={() => console.log('Export data clicked')}
            onDownloadExcelTemplateClick={() => console.log('Download Excel template clicked')}
            onImportFromExcelClick={() => console.log('Import from Excel clicked')}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-auto'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.id || undefined}
              />
            </div>
            <div className='overflow-x-auto'>
              <AritoInputTable
                value={[]}
                columns={exportCreditAdviceDetailColumns}
                mode='view'
                className='w-full'
                tableActionButtons={['export', 'pin']}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
