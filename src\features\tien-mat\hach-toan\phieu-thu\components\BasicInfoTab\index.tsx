import { SearchFieldStatesProps } from '../../hooks/useSearchFieldStates';
import { AritoHeaderTabs, TabItem } from '@/components/custom/arito';
import { InfoTab } from './InfoTab';

type FormMode = 'add' | 'edit' | 'view';

interface BasicInfoTabProps {
  formMode: FormMode;
  searchFieldStates: SearchFieldStatesProps;
}

export const BasicInfoTab = ({ formMode, searchFieldStates }: BasicInfoTabProps) => {
  const tabs: TabItem[] = [
    {
      id: 'info',
      label: 'Thông tin',
      component: <InfoTab formMode={formMode} searchFieldStates={searchFieldStates} />
    }
  ];

  return <AritoHeaderTabs tabs={tabs} />;
};
