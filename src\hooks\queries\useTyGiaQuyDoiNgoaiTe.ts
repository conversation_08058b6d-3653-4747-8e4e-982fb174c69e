import { useState, useEffect, useCallback } from 'react';
import {
  TyGiaQuyDoiNgoaiTe,
  TyGiaQuyDoiNgoaiTeInput,
  TyGiaQuyDoiNgoaiTeResponse
} from '@/types/schemas/ty-gia-quy-doi-ngoai-te.type';
import { ExchangeRateFormattedData } from '@/features/danh-muc/doi-tuong/ty-gia-quy-doi-ngoai-te/schemas';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseTyGiaQuyDoiNgoaiTeReturn {
  exchangeRates: TyGiaQuyDoiNgoaiTe[];
  isLoading: boolean;
  addExchangeRate: (newExchangeRate: ExchangeRateFormattedData) => Promise<void>;
  updateExchangeRate: (uuid: string, updatedExchangeRate: ExchangeRateFormattedData) => Promise<void>;
  deleteExchangeRate: (uuid: string) => Promise<void>;
  refreshExchangeRates: () => Promise<void>;
}

export const useTyGiaQuyDoiNgoaiTe = (initialExchangeRates: TyGiaQuyDoiNgoaiTe[] = []): UseTyGiaQuyDoiNgoaiTeReturn => {
  const [exchangeRates, setExchangeRates] = useState<TyGiaQuyDoiNgoaiTe[]>(initialExchangeRates);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchExchangeRates = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<TyGiaQuyDoiNgoaiTeResponse>(`/entities/${entity.slug}/erp/exchange-rates/`);

      // API might return either data directly or in a results property
      const exchangeRatesData = response.data.results || response.data;
      setExchangeRates(exchangeRatesData);
    } catch (error) {
      console.error('Error fetching exchange rates:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  const addExchangeRate = async (newExchangeRate: ExchangeRateFormattedData) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const payload = {
        ngay_hl:
          newExchangeRate.ngay_hl instanceof Date
            ? newExchangeRate.ngay_hl.toISOString().split('T')[0]
            : newExchangeRate.ngay_hl,
        ma_nt: newExchangeRate.ma_nt,
        ty_gia: newExchangeRate.ty_gia
      };

      const response = await api.post(`/entities/${entity.slug}/erp/exchange-rates/`, payload);

      const addedExchangeRate: TyGiaQuyDoiNgoaiTe = response.data;
      setExchangeRates(prev => [...prev, addedExchangeRate]);
    } catch (error) {
      console.error('Error adding exchange rate:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateExchangeRate = async (uuid: string, updatedExchangeRate: ExchangeRateFormattedData) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const payload = {
        ty_gia: updatedExchangeRate.ty_gia
      };

      const response = await api.patch(`/entities/${entity.slug}/erp/exchange-rates/${uuid}/`, payload);
      const updatedExchangeRateData: TyGiaQuyDoiNgoaiTe = response.data;
      setExchangeRates(prev =>
        prev.map(exchangeRate =>
          exchangeRate.uuid === updatedExchangeRateData.uuid ? updatedExchangeRateData : exchangeRate
        )
      );
    } catch (error) {
      console.error('Error updating exchange rate:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteExchangeRate = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/exchange-rates/${uuid}/`);

      // Remove the exchange rate from the list
      setExchangeRates(prev => prev.filter(exchangeRate => exchangeRate.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting exchange rate:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchExchangeRates();
  }, [fetchExchangeRates]);

  return {
    exchangeRates,
    isLoading,
    addExchangeRate,
    updateExchangeRate,
    deleteExchangeRate,
    refreshExchangeRates: fetchExchangeRates
  };
};
