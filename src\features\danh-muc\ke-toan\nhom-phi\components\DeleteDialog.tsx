import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { Group } from '@/types/schemas';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: Group | null;
  deleteFeeGroup: (uuid: string) => Promise<boolean | undefined>;
  clearSelection: () => void;
}

function DeleteDialog({ onClose, open, selectedObj, deleteFeeGroup, clearSelection }: DeleteDialogProps) {
  const handleDelete = async () => {
    if (selectedObj) {
      try {
        await deleteFeeGroup(selectedObj.uuid);
        clearSelection();
        onClose();
      } catch (error) {
        console.error('Error deleting group:', error);
      }
    }
  };

  return (
    <AritoDialog open={open} onClose={onClose} title='Xác nhận xóa' maxWidth='sm' titleIcon={<AritoIcon icon={12} />}>
      <div className='p-4'>
        <p className='mb-4'><PERSON>ạn có chắc chắn muốn xóa nhóm phí này?</p>
        <div className='flex justify-end gap-2'>
          <Button variant='outlined' onClick={onClose}>
            Hủy
          </Button>
          <Button variant='contained' color='error' onClick={handleDelete}>
            Xóa
          </Button>
        </div>
      </div>
    </AritoDialog>
  );
}

export default DeleteDialog;
