import { useState } from 'react';
import { TyGiaQuyDoiNgoaiTe } from '@/types/schemas/ty-gia-quy-doi-ngoai-te.type';

export const useRowSelection = () => {
  const [selectedObj, setSelectedObj] = useState<TyGiaQuyDoiNgoaiTe | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const handleRowClick = (params: any) => {
    const row = params.row;
    setSelectedObj(row);
    setSelectedRowIndex(row.id.toString());
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection
  };
};
