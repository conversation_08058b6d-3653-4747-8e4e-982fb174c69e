import { GridCellParams } from '@mui/x-data-grid';
import { useState } from 'react';

export interface SelectedCellInfo {
  id: string;
  field: string;
}

export function useInputTableRows<
  T extends {
    uuid: string;
  }
>(initialRows: T[] = []) {
  const [rows, setRows] = useState<T[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<T | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);

  const handleRowClick = (params: { id: string; row: any }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    // Update row selection
    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as any);

    // Update cell selection
    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    updatedRows[rowIndex] = {
      ...updatedRows[rowIndex],
      [field]: newValue
    };

    setRows(updatedRows);

    // If the changed row is the currently selected row, update selectedRow
    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleCellValueChange
  };
}
