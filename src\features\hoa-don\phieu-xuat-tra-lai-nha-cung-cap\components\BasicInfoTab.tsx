import { FormField } from '@/components/custom/arito/form/form-field';

export const BasicInformationTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid gap-x-6 gap-y-4 lg:grid-cols-5'>
      {/* Left column - 3/5 */}
      <div className='col-span-5 space-y-1 lg:col-span-3'>
        <FormField
          label='Mã nhà cung cấp'
          className='grid grid-cols-[150px,1fr] items-center'
          name='ma_nha_cung_cap'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />

        <FormField
          label='Tên nhà cung cấp'
          className='grid grid-cols-[150px,1fr] items-center'
          name='ten_nha_cung_cap'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Địa chỉ'
          className='grid grid-cols-[150px,1fr] items-center'
          name='dia_chi'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Mã nhập xuất'
          className='grid grid-cols-[150px,1fr] items-center'
          name='ma_nhap_xuat'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Tài khoản nợ'
          className='grid grid-cols-[150px,1fr] items-center'
          name='tai_khoan_no'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />

        <FormField
          label='Diễn giải'
          className='grid grid-cols-[150px,1fr] items-center'
          name='dien_giai'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>

      {/* Middle column - 1/5 */}
      <div className='col-span-5 space-y-1 lg:col-span-1'>
        <FormField
          label='Mã số thuế'
          className='grid grid-cols-[100px,1fr] items-center'
          name='ma_so_thue'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Người nhận'
          className='grid grid-cols-[100px,1fr] items-center'
          name='nguoi_nhan'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Email'
          className='grid grid-cols-[100px,1fr] items-center'
          name='email'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Hạn thanh toán'
          className='grid grid-cols-[100px,1fr] items-center'
          name='han_thanh_toan'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />
      </div>

      {/* Right column - 1/5 */}
      <div className='col-span-1 space-y-1 lg:col-span-1'>
        <FormField
          label='Số chứng từ'
          className='grid grid-cols-[100px,1fr] items-center'
          name='so_chung_tu'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Ngày chứng từ'
          className='grid grid-cols-[100px,1fr] items-center'
          name='ngay_chung_tu'
          type='date'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Ngày hoá đơn'
          className='grid grid-cols-[100px,1fr] items-center'
          name='ngay_hoa_don'
          type='date'
          disabled={formMode === 'view'}
        />

        <div className='grid grid-cols-2 gap-x-4'>
          <FormField
            label='Ngoại tệ'
            className='grid grid-cols-[100px,1fr] items-center'
            name='ngoai_te'
            type='text'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <FormField
            label='Tỷ giá'
            className='grid grid-cols-[60px,1fr] items-center'
            name='ty_gia'
            type='number'
            disabled={formMode === 'view'}
          />
        </div>

        <FormField
          label='Trạng thái'
          className='grid grid-cols-[100px,1fr] items-center'
          name='trang_thai'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 0, label: 'Chưa ghi sổ' },
            { value: 1, label: 'Chờ duyệt' },
            { value: 2, label: 'Đã ghi sổ' },
            { value: 3, label: 'Huỷ' }
          ]}
        />

        <div className='flex w-full items-center gap-x-2 pl-[100px]'>
          <FormField
            className='grid grid-cols-[100px,1fr] items-center'
            type='checkbox'
            label='Dữ liệu được nhận'
            name='du_lieu_duoc_nhan'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  </div>
);
