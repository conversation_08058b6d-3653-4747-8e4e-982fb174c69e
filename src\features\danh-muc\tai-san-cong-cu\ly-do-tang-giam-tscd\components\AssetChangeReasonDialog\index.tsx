import React, { useState } from 'react';
import { Button } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';

import { assetChangeReasonSchema, AssetChangeReasonFormValues } from '../../schemas';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import AssetChangeReasonForm from './AssetChangeReasonForm';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';

interface AssetChangeReasonDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: AssetChangeReasonFormValues;
  onClose: () => void;
  selectedObj?: any;
  addAssetChangeReason: (data: AssetChangeReasonFormValues) => Promise<void>;
  updateAssetChangeReason: (uuid: string, data: AssetChangeReasonFormValues) => Promise<void>;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

export function AssetChangeReasonDialog({
  open,
  mode,
  initialData,
  onClose,
  selectedObj,
  addAssetChangeReason,
  updateAssetChangeReason,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: AssetChangeReasonDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const title = mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Danh mục lý do tăng giảm TSCĐ';

  const handleSubmit = async (data: AssetChangeReasonFormValues) => {
    try {
      setError(null);

      const formData = {
        ...data,
        ten_tg_ts2: data.ten_tg_ts2 || ''
      };

      if (mode === 'add') {
        await addAssetChangeReason(formData);
      } else if (mode === 'edit' && selectedObj) {
        await updateAssetChangeReason(selectedObj.uuid, formData);
      }
      onClose();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      setError(error.message || 'Có lỗi xảy ra khi lưu dữ liệu');
    }
  };

  const handleCloseConfirm = () => {
    setShowConfirmDialog(false);
  };

  const handleCancelClick = () => {
    if (mode !== 'view') {
      setShowConfirmDialog(true);
    } else {
      onClose();
    }
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={assetChangeReasonSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<AssetChangeReasonForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}

              {mode !== 'view' && (
                <div className='flex w-full justify-end gap-2'>
                  <Button variant='contained' color='primary' type='submit' className='bg-teal-600 hover:bg-teal-700'>
                    {mode === 'add' ? 'Đồng ý' : 'Lưu'}
                  </Button>
                  <Button variant='outlined' color='primary' onClick={handleCancelClick}>
                    Hủy
                  </Button>
                </div>
              )}
            </>
          }
        />
        {error && <div className='mt-2 rounded bg-red-50 p-4 text-red-600'>{error}</div>}
      </AritoDialog>

      {showConfirmDialog && (
        <ConfirmDialog
          open={showConfirmDialog}
          onClose={handleCloseConfirm}
          onConfirm={onClose}
          title='Xác nhận hủy'
          message='Bạn có chắc chắn muốn hủy thao tác này? Dữ liệu đã nhập sẽ không được lưu.'
        />
      )}
    </>
  );
}
