import { z } from 'zod';
import { LyDoTangGiamTSCDInput } from '@/types/schemas/ly-do-tang-giam-tscd.type';

export const assetChangeReasonSchema = z.object({
  loai_tg_ts: z.string({ required_error: 'Loại tăng giảm là bắt buộc' }),
  ma_tg_ts: z.string({ required_error: 'Mã tăng giảm là bắt buộc' }),
  ten_tg_ts: z.string({ required_error: 'Tên tăng giảm là bắt buộc' }),
  ten_tg_ts2: z.string().optional().nullable(),
  status: z.string({ required_error: 'Trạng thái là bắt buộc' })
});

export type AssetChangeReasonFormValues = LyDoTangGiamTSCDInput;
