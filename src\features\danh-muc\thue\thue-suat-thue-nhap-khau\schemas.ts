import * as yup from 'yup';

// Define the form schema for validation
export const taxRateSchema = yup
  .object({
    taxCode: yup.string().required('Bạn chưa nhập mã thuế'),
    taxName: yup.string().required('Bạn chưa nhập tên thuế'),
    taxRate: yup.number().required('Bạn chưa nhập thuế suất').min(0, 'Thu<PERSON> suất không được âm'),
    outputTaxAccount: yup.string().required('Bạn chưa nhập TK thuế'),
    status: yup.string().required('Bạn chưa nhập trạng thái')
  })
  .required();
