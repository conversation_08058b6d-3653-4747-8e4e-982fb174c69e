import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { FormMode } from '@/types/form';

interface AssetChangeReasonFormProps {
  mode: FormMode;
}

export default function AssetChangeReasonForm({ mode }: AssetChangeReasonFormProps) {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
        {/* Cột trái */}
        <div className='col-span-4 space-y-2'>
          <FormField
            label='Loại tăng giảm'
            className='grid grid-cols-[160px,1fr] items-center'
            name='loai_tg_ts'
            type='select'
            options={[
              { value: '1', label: 'Tăng tài sản' },
              { value: '2', label: 'Giảm tài sản' }
            ]}
            disabled={mode === 'view'}
          />
          <FormField
            label='Mã tăng giảm'
            className='grid grid-cols-[160px,1fr] items-center'
            name='ma_tg_ts'
            type='text'
            disabled={mode === 'view'}
          />
          <FormField
            label='Tên tăng giảm'
            className='grid grid-cols-[160px,1fr] items-center'
            name='ten_tg_ts'
            type='text'
            disabled={mode === 'view'}
          />
          <FormField
            label='Tên khác'
            className='grid grid-cols-[160px,1fr] items-center'
            name='ten_tg_ts2'
            type='text'
            disabled={mode === 'view'}
          />

          <FormField
            label='Trạng thái'
            className='grid grid-cols-[160px,1fr] items-center'
            name='status'
            type='select'
            options={[
              { value: '0', label: '0. Không sử dụng' },
              { value: '1', label: '1. Còn sử dụng' }
            ]}
            disabled={mode === 'view'}
          />

          {/* Hidden fields for required values */}
          <div className='hidden'>
            <FormField name='action' type='text' defaultValue='CUSTOM' />
            <FormField name='param' type='text' defaultValue='MANUAL' />
          </div>
        </div>
      </div>
    </div>
  );
}
