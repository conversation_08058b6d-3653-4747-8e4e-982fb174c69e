import React from 'react';
import DepartmentForm from '@/features/danh-muc/doi-tuong/bo-phan-ke-toan/components/department-dialog/DepartmentForm';
import { departmentSearchColumns, feeGroupSearchColumns } from '../cols-definition';
import FeeGroupForm from '../../nhom-phi/components/feegroup-dialog/FeeGroupForm';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface FeeFormProps {
  mode: FormMode;
}

function FeeForm({ mode }: FeeFormProps) {
  const isDisabled = mode === 'view';

  return (
    <div>
      <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
        <div className='p-4 md:p-6'>
          <div className='flex flex-col gap-y-4 md:gap-y-6'>
            <div className='space-y-4 md:space-y-6'>
              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã phí</Label>
                <FormField type='text' name='ma_phi' disabled={isDisabled || mode === 'edit'} />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên phí</Label>
                <FormField type='text' name='ten_phi' disabled={isDisabled} />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên khác</Label>
                <FormField type='text' name='ten_khac' disabled={isDisabled} />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Nhóm phí 1</Label>
                <FormField
                  type='text'
                  name='nhom_phi_1'
                  disabled={isDisabled}
                  withSearch
                  searchEndpoint='/groups/?loai_nhom=PHI1'
                  searchColumns={feeGroupSearchColumns}
                  dialogTitle='Tìm kiếm nhóm phí 1'
                  actionButtons={['add', 'edit']}
                  headerFields={<FeeGroupForm mode={mode} />}
                  displayRelatedField='ten_phan_nhom'
                  searchResultValueKey='uuid'
                  searchResultLabelKey='ten_phan_nhom'
                />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Nhóm phí 2</Label>
                <FormField
                  type='text'
                  name='nhom_phi_2'
                  disabled={isDisabled}
                  withSearch
                  searchEndpoint='/groups/?loai_nhom=PHI2'
                  searchColumns={feeGroupSearchColumns}
                  dialogTitle='Tìm kiếm nhóm phí 2'
                  actionButtons={['add', 'edit']}
                  headerFields={<FeeGroupForm mode={mode} />}
                  displayRelatedField='ten_phan_nhom'
                  searchResultValueKey='uuid'
                  searchResultLabelKey='ten_phan_nhom'
                />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Nhóm phí 3</Label>
                <FormField
                  type='text'
                  name='nhom_phi_3'
                  disabled={isDisabled}
                  withSearch
                  searchEndpoint='/groups/?loai_nhom=PHI3'
                  searchColumns={feeGroupSearchColumns}
                  dialogTitle='Tìm kiếm nhóm phí 3'
                  actionButtons={['add', 'edit']}
                  headerFields={<FeeGroupForm mode={mode} />}
                  displayRelatedField='ten_phan_nhom'
                  searchResultValueKey='uuid'
                  searchResultLabelKey='ten_phan_nhom'
                />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Bộ phận</Label>
                <FormField
                  type='text'
                  name='bo_phan'
                  disabled={isDisabled}
                  withSearch
                  searchEndpoint='/departments'
                  searchColumns={departmentSearchColumns}
                  dialogTitle='Tìm kiếm bộ phận'
                  actionButtons={['add', 'edit']}
                  headerFields={<DepartmentForm mode={mode} />}
                  displayRelatedField='ten_bp'
                  searchResultValueKey='uuid'
                  searchResultLabelKey='ten_bp'
                />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label htmlFor='trang_thai' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                  Trạng thái
                </Label>
                <FormField
                  id='trang_thai'
                  type='select'
                  name='trang_thai'
                  defaultValue={1}
                  disabled={isDisabled}
                  options={[
                    { label: '1. Còn sử dụng', value: 1 },
                    { label: '2. Không sử dụng', value: 0 }
                  ]}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FeeForm;
