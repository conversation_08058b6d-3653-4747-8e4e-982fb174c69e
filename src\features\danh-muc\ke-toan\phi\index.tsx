'use client';

import React from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, DeleteDialog, FeeDialog } from './components';
import { useDialogState, useRowSelection } from './hooks';
import { useFee } from '@/hooks/queries/useFee';
import { FeeColumns } from './cols-definition';

export default function Phi() {
  const { fees, loading, addFee, updateFee, deleteFee } = useFee();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  } = useDialogState(clearSelection);

  const tables = [
    {
      name: 'Tất cả',
      rows: fees,
      columns: FeeColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <FeeDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedObj && showEditDialog
              ? {
                  ma_phi: selectedObj.ma_phi,
                  ten_phi: selectedObj.ten_phi,
                  ten_khac: selectedObj.ten_khac,
                  nhom_phi_1: selectedObj.nhom_phi_1?.uuid || null,
                  nhom_phi_2: selectedObj.nhom_phi_2?.uuid || null,
                  nhom_phi_3: selectedObj.nhom_phi_3?.uuid || null,
                  bo_phan: selectedObj.bo_phan,
                  trang_thai: selectedObj.trang_thai
                }
              : selectedObj && showAddDialog && isCopyMode
                ? {
                    ma_phi: '',
                    ten_phi: selectedObj.ten_phi,
                    ten_khac: selectedObj.ten_khac,
                    nhom_phi_1: selectedObj.nhom_phi_1?.uuid || null,
                    nhom_phi_2: selectedObj.nhom_phi_2?.uuid || null,
                    nhom_phi_3: selectedObj.nhom_phi_3?.uuid || null,
                    bo_phan: selectedObj.bo_phan,
                    trang_thai: selectedObj.trang_thai
                  }
                : undefined
          }
          onClose={showEditDialog ? closeEditDialog : closeAddDialog}
          selectedObj={showEditDialog ? selectedObj : null}
          addFee={addFee}
          updateFee={updateFee}
        />
      )}

      {showDeleteDialog && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          selectedObj={selectedObj}
          deleteFee={deleteFee}
          clearSelection={clearSelection}
        />
      )}

      {showWatchDialog && selectedObj && (
        <FeeDialog
          open={showWatchDialog}
          mode='view'
          initialData={{
            ma_phi: selectedObj.ma_phi,
            ten_phi: selectedObj.ten_phi,
            ten_khac: selectedObj.ten_khac,
            nhom_phi_1: selectedObj.nhom_phi_1?.uuid || null,
            nhom_phi_2: selectedObj.nhom_phi_2?.uuid || null,
            nhom_phi_3: selectedObj.nhom_phi_3?.uuid || null,
            bo_phan: selectedObj.bo_phan,
            trang_thai: selectedObj.trang_thai
          }}
          onClose={closeWatchDialog}
          selectedObj={selectedObj}
          addFee={addFee}
          updateFee={updateFee}
          onAddButtonClick={handleAddButtonClick}
          onEditButtonClick={handleEditButtonClick}
          onDeleteButtonClick={handleDeleteButtonClick}
          onCopyButtonClick={handleCopyButtonClick}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddClick={openAddDialog}
          onEditClick={() => selectedObj && openEditDialog()}
          onDeleteClick={() => selectedObj && openDeleteDialog()}
          onViewClick={() => selectedObj && openWatchDialog()}
          isEditDisabled={!selectedObj}
          isViewDisabled={!selectedObj}
        />

        {loading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
          </div>
        )}

        {!loading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>
    </div>
  );
}
