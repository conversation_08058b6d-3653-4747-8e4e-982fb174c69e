interface Props {
  totalQuantity: number;
  totalExpense: number;
  totalTax: number;
  totalAmount: number;
  totalPayment: number;
  formMode: 'add' | 'edit' | 'view';
}

export function BottomBar({ totalQuantity, totalExpense, totalTax, totalAmount, totalPayment, formMode }: Props) {
  return (
    <div className='bottom-0 w-full bg-white px-5 py-4 pb-2 shadow-md lg:fixed lg:pb-1'>
      <div className='flex justify-between'>
        {/* Left Corner */}
        <div className='space-y-2'>
          <div className='flex gap-8'>
            <div className='grid w-[300px] grid-cols-[160px,1fr] items-center'>
              <div className='text-sm font-bold'>Tổng số lượng</div>
              <div className='border-b-[1px] border-gray-200 text-right text-sm font-bold'>{totalQuantity}</div>
            </div>
            <div className='grid w-[300px] grid-cols-[160px,1fr] items-center'>
              <div className='text-sm font-bold'>Tổng chi phí</div>
              <div className='border-b-[1px] border-gray-200 text-right text-sm font-bold'>{totalExpense}</div>
            </div>
          </div>
          <div className='grid w-[300px] grid-cols-[160px,1fr] items-center'>
            <div className='text-sm font-bold'>Tổng thuế</div>
            <div className='border-b-[1px] border-gray-200 text-right text-sm font-bold'>{totalTax}</div>
          </div>
        </div>

        {/* Right Corner */}
        <div className='space-y-2'>
          <div className='grid w-[300px] grid-cols-[160px,1fr] items-center'>
            <div className='text-sm font-bold'>Tổng tiền</div>
            <div className='border-b-[1px] border-gray-200 text-right text-sm font-bold'>{totalAmount}</div>
          </div>
          <div className='grid w-[300px] grid-cols-[160px,1fr] items-center'>
            <div className='text-sm font-bold'>Tổng thanh toán</div>
            <div className='border-b-[1px] border-gray-200 text-right text-sm font-bold'>{totalPayment}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
