'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  getTransferOrderColumns,
  transferOrderDetailColumns
} from '@/features/ton-kho/phieu-xuat-dieu-chuyen/cols-definition';
import { InternalShipmentFormTab } from '@/features/ton-kho/phieu-xuat-dieu-chuyen/components/InternalShipmentFormTab';
import { TransferOrderActionBar } from '@/features/ton-kho/phieu-xuat-dieu-chuyen/components/TransferOrderActionBar';
import { TransferOrderItemTab } from '@/features/ton-kho/phieu-xuat-dieu-chuyen/components/TransferOrderItemTab';
import { BasicInfoTab } from '@/features/ton-kho/phieu-xuat-dieu-chuyen/components/BasicInfoTab';
import { BottomBar } from '@/features/ton-kho/phieu-xuat-dieu-chuyen/components/BottomBar';
import { transferOrderSchema } from '@/features/ton-kho/phieu-xuat-dieu-chuyen/schemas';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';

// Mock data for initial state
const initialTransferOrderRows: any[] = [
  {
    id: 1,
    status: 'Mới',
    receiptNumber: 'PXDC001',
    date: '2023-07-15',
    objectCode: 'NCC001',
    objectName: 'Công ty ABC',
    departmentCode: 'IT',
    description: 'Phiếu xuất điều chuyển tháng 7/2023',
    totalAmount: ********,
    currency: 'VND',
    totalQuantity: 10,
    documentCode: 'CT001',
    createdBy: 'admin',
    createdAt: '2023-07-15',
    updatedBy: 'admin',
    updatedAt: '2023-07-15',
    transferOrderDetails: [
      {
        id: 'DET001',
        productCode: 'SP001',
        productName: 'Laptop Dell XPS 13',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 2,
        averagePrice: ********,
        priceInVND: ********,
        amountInVND: ********,
        debitAccount: '156',
        exportReason: 'Điều chuyển',
        creditAccount: '331',
        department: 'IT',
        product: 'SP001',
        price: ********,
        amount: ********,
        quantityExported: 2,
        exportNumber: 'PXDC001',
        exportLine: '1'
      },
      {
        id: 'DET002',
        productCode: 'SP002',
        productName: 'Màn hình Dell 27"',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 3,
        averagePrice: 5000000,
        priceInVND: 5000000,
        amountInVND: ********,
        debitAccount: '156',
        exportReason: 'Điều chuyển',
        creditAccount: '331',
        department: 'IT',
        product: 'SP002',
        price: 5000000,
        amount: ********,
        quantityExported: 3,
        exportNumber: 'PXDC001',
        exportLine: '2'
      }
    ]
  }
];

// Helper functions to calculate totals from transfer order data
const calculateTotals = (selectedObj: any) => {
  if (!selectedObj) {
    return {
      totalQuantity: 0,
      totalAmount: 0
    };
  }

  // Initialize result object
  const result = {
    totalQuantity: 0,
    totalAmount: 0
  };

  // Calculate totals from transfer order details
  if (selectedObj.transferOrderDetails && selectedObj.transferOrderDetails.length > 0) {
    selectedObj.transferOrderDetails.forEach((detail: any) => {
      // Total quantity
      result.totalQuantity += detail.quantity || 0;

      // Total amount
      result.totalAmount += detail.amountInVND || 0;
    });
  }

  return result;
};

export default function Page() {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [transferOrderRows, setTransferOrderRows] = useState<any[]>(initialTransferOrderRows);
  //Add and Edit
  const [inputTransferOrderDetail, setInputTransferOrderDetail] = useState<any[]>([]);

  // Helper function to calculate totals from current form state
  const calculateFormTotals = () => {
    // Initialize result object
    const result = {
      totalQuantity: 0,
      totalAmount: 0
    };

    // Calculate totals from current transfer order details in the form
    if (inputTransferOrderDetail && inputTransferOrderDetail.length > 0) {
      inputTransferOrderDetail.forEach((detail: any) => {
        // Total quantity
        result.totalQuantity += Number(detail.quantity) || 0;

        // Total amount
        result.totalAmount += Number(detail.amountInVND) || 0;
      });
    }

    return result;
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.transferOrderDetails) {
      setInputTransferOrderDetail([...obj.transferOrderDetails]);
    } else {
      setInputTransferOrderDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.transferOrderDetails) {
      setInputTransferOrderDetail([...obj.transferOrderDetails]);
    } else {
      setInputTransferOrderDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputTransferOrderDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    console.log(inputTransferOrderDetail);
    console.log(data);
    try {
      if (inputTransferOrderDetail.length === 0) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi nhập liệu',
          html: `<p class="text-[15px]">Bạn chưa nhập chi tiết phiếu xuất điều chuyển</p>`
        });
        return;
      }

      // Validate transfer order detail
      for (const row of inputTransferOrderDetail) {
        await transferOrderSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...transferOrderRows.map(row => row.id || 0)) + 1;
        const newTransferOrder = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          transferOrderDetails: inputTransferOrderDetail
        };

        setTransferOrderRows([...transferOrderRows, newTransferOrder]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = transferOrderRows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              transferOrderDetails: inputTransferOrderDetail
            };
          }
          return row;
        });

        setTransferOrderRows(updatedRows);
      }

      setShowForm(false);
      setInputTransferOrderDetail([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const handleRowClick = (params: GridRowParams) => {
    const transferOrder = params.row as any;
    setSelectedObj(transferOrder);
    setInputTransferOrderDetail(transferOrder.transferOrderDetails);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: transferOrderRows,
      columns: getTransferOrderColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Lập chứng từ',
      rows: transferOrderRows.filter(row => row.status === 'Lập chứng từ'),
      columns: getTransferOrderColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Xuất kho',
      rows: transferOrderRows.filter(row => row.status === 'Chờ duyệt'),
      columns: getTransferOrderColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='blue' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  const from = (
    <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Phiếu yêu cầu xuất kho'
      >
        Phiếu yêu cầu xuất kho
      </button>
    </div>
  );

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'lineItems',
                label: 'Chi tiết',
                component: (
                  <TransferOrderItemTab
                    value={inputTransferOrderDetail}
                    onChange={setInputTransferOrderDetail}
                    formMode={formMode}
                  />
                )
              },
              {
                id: 'internalShipment',
                label: 'PX kho kiêm v/c nội bộ',
                component: <InternalShipmentFormTab formMode={formMode} />
              },
              {
                id: 'filekem',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Phiếu xuất điều chuyển'}
            from={from}
            bottomBar={<BottomBar totalQuantity={0} totalAmount={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <TransferOrderActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.transferOrderDetails || []}
                columns={transferOrderDetailColumns}
                mode={formMode}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
