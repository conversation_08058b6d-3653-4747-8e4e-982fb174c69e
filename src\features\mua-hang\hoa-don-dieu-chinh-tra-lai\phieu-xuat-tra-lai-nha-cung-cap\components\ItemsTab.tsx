import { AritoInputTable } from '@/components/custom/arito/input-table';
import { FormField } from '@/components/custom/arito/form/form-field';
interface ItemsTabProps {
  columns: any[];
  formMode: 'edit' | 'view' | 'add';
}

export const ItemsTab = ({ columns, formMode }: ItemsTabProps) => {
  return (
    // <div className='h-[calc(100vh)] w-screen'>
    //   <FormField name='items' type='table' columns={columns} disabled={formMode === 'view'} />
    // </div>
    <div className='h-[500px] w-full'>
      <AritoInputTable
        value={[]}
        columns={columns}
        onChange={() => {}}
        mode={formMode}
        tableActionButtons={['add', 'delete', 'copy', 'paste', 'moveUp', 'moveDown', 'export', 'pin']}
      />
    </div>
  );
};
