import { But<PERSON> } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onCloseConfirmDialog: () => void;
  onConfirm?: () => void;
}

function ConfirmDialog({ onClose, onCloseConfirmDialog, onConfirm, open }: ConfirmDialogProps) {
  return (
    <AritoDialog
      open={open}
      onClose={onCloseConfirmDialog}
      title='Xác nhận'
      maxWidth='sm'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            onClick={onConfirm || onClose}
            type='submit'
            form='search-form'
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            <PERSON><PERSON>ng <PERSON>
          </Button>
          <Button onClick={onCloseConfirmDialog} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Không
          </Button>
        </>
      }
    >
      <p className='p-4 text-base font-medium'>
        {onConfirm
          ? 'Bạn có chắc chắn muốn lưu thay đổi?'
          : 'Dữ liệu có thể bị mất nếu bạn thoát khỏi form này. Bạn có chắc chắn muốn thoát?'}
      </p>
    </AritoDialog>
  );
}

export default ConfirmDialog;
