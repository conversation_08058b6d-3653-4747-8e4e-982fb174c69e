/**
 * TypeScript interfaces for HoaDonBanHangDichVu (Service Sales Invoice)
 *
 * This interface represents the structure of the HoaDonDichVu model from the backend.
 * It defines service sales invoices issued by the organization to customers.
 */

import { HinhThucThanhToan } from './hinh-thuc-thanh-toan.type';
import { <PERSON>ha<PERSON><PERSON>ang } from './khach-hang.type';
import { AccountModel } from './account.type';
import { NhanVien } from './nhan-vien.type';
import { NgoaiTe } from './ngoai-te.type';
import { ApiResponse } from '../api.type';
import { DichVu } from './dich-vu.type';
import { Tax } from './tax.type';

// Main HoaDonBanHangDichVu interface
export interface HoaDonBanHangDichVu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Customer code
   */
  ma_kh: string;
  ma_kh_data?: KhachHang;

  /**
   * Customer tax code
   */
  ma_so_thue: string;

  /**
   * Creation method (true: automatic, false: manual)
   */
  pt_tao_yn: boolean;

  /**
   * Payment method code
   */
  ma_httt: string;
  ma_httt_data?: HinhThucThanhToan;

  /**
   * Customer name on tax invoice
   */
  ten_kh_thue: string;

  /**
   * Customer address
   */
  dia_chi: string;

  /**
   * Customer representative name
   */
  ong_ba: string;

  /**
   * Sales staff code
   */
  ma_nvbh: string;
  ma_nvbh_data?: NhanVien;

  /**
   * Customer contact email
   */
  e_mail: string;

  /**
   * Related accounting account
   */
  tk: string;
  tk_data?: AccountModel;

  /**
   * Status code
   */
  ma_tt: string;

  /**
   * Detailed description of the invoice
   */
  dien_giai: string;

  /**
   * Supervisor code
   */
  ma_ngv: string;
  ma_ngv_data?: NhanVien;

  /**
   * Unit ID related to the invoice
   */
  unit_id: number;

  /**
   * Internal document number
   */
  i_so_ct: string;

  /**
   * Transaction code
   */
  ma_nk: string;
  ma_nk_data?: any;

  /**
   * Document number
   */
  so_ct: string;

  /**
   * Document date
   */
  ngay_ct: string;

  /**
   * Last document date
   */
  ngay_lct: string;

  /**
   * Secondary document number
   */
  so_ct2: string;

  /**
   * Foreign currency code
   */
  ma_nt: string;
  ma_nt_data?: NgoaiTe;

  /**
   * Exchange rate
   */
  ty_gia: number;

  /**
   * Status
   */
  status: string;

  /**
   * Transfer flag
   */
  transfer_yn: boolean;

  /**
   * Customer code 9
   */
  ma_kh9: string;

  /**
   * Cancellation reason
   */
  ly_do_huy: string;

  /**
   * Reason
   */
  ly_do: string;

  /**
   * Tax item name
   */
  ten_vt_thue: string;

  /**
   * Notes
   */
  ghi_chu: string;

  /**
   * E-invoice status code
   */
  ma_tthddt: string;

  /**
   * Payment method code
   */
  ma_pttt: string;

  /**
   * E-invoice document number
   */
  so_ct_hddt: string;

  /**
   * E-invoice document date
   */
  ngay_ct_hddt: string;

  /**
   * Secondary e-invoice document number
   */
  so_ct2_hddt: string;

  /**
   * E-invoice template code
   */
  ma_mau_ct_hddt: string;

  /**
   * Total amount in foreign currency 2
   */
  t_tien_nt2: number;

  /**
   * Total amount 2
   */
  t_tien2: number;

  /**
   * Total tax in foreign currency
   */
  t_thue_nt: number;

  /**
   * Total tax
   */
  t_thue: number;

  /**
   * Total discount in foreign currency
   */
  t_ck_nt: number;

  /**
   * Total discount
   */
  t_ck: number;

  /**
   * Total payment in foreign currency
   */
  t_tt_nt: number;

  /**
   * Total payment
   */
  t_tt: number;

  /**
   * Invoice detail items
   */
  chi_tiet?: ChiTietHoaDonBanHangDichVu[];

  /**
   * Payment information
   */
  thong_tin_thanh_toan?: ThongTinThanhToanHoaDonDichVu[];

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

// Chi tiet hoa don (Invoice Detail) interface
export interface ChiTietHoaDonBanHangDichVu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Related service invoice UUID
   */
  hoa_don: string;

  /**
   * Line number in the invoice
   */
  line: number;

  /**
   * Service code
   */
  ma_dv: string;
  ma_dv_data?: DichVu;

  /**
   * Revenue account
   */
  tk_dt: string;
  tk_dt_data?: AccountModel;

  /**
   * Unit of measurement
   */
  dvt: string;
  dvt_data?: any;

  /**
   * Quantity of service
   */
  so_luong: number;

  /**
   * Price in foreign currency
   */
  gia_nt2: number;

  /**
   * Amount in foreign currency
   */
  tien_nt2: number;

  /**
   * Detailed description of the service
   */
  dien_giai: string;

  /**
   * Discount rate
   */
  tl_ck: number;

  /**
   * Discount in foreign currency
   */
  ck_nt: number;

  /**
   * Discount account
   */
  tk_ck: string;

  /**
   * Discount account name
   */
  ten_tk_ck: string;

  /**
   * Tax code
   */
  ma_thue: string;
  ma_thue_data?: Tax;

  /**
   * Tax rate
   */
  thue_suat: number;

  /**
   * Tax credit account
   */
  tk_thue_co: string;
  tk_thue_co_data?: AccountModel;

  /**
   * Tax credit account name
   */
  ten_tk_thue_co: string;

  /**
   * Tax in foreign currency
   */
  thue_nt: number;

  /**
   * Price
   */
  gia2: number;

  /**
   * Amount
   */
  tien2: number;

  /**
   * Discount
   */
  ck: number;

  /**
   * Tax
   */
  thue: number;

  /**
   * Department code
   */
  ma_bp: string;
  ma_bp_data?: any;

  /**
   * Case code
   */
  ma_vv: string;
  ma_vv_data?: any;

  /**
   * Contract code
   */
  ma_hd: string;
  ma_hd_data?: any;

  /**
   * Aggregation object code
   */
  ma_dtt: string;
  ma_dtt_data?: any;

  /**
   * Area code
   */
  ma_ku: string;
  ma_ku_data?: any;

  /**
   * Fee code
   */
  ma_phi: string;
  ma_phi_data?: any;

  /**
   * Product code
   */
  ma_sp: string;

  /**
   * Production order code
   */
  ma_lsx: string;

  /**
   * Cost code
   */
  ma_cp0: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

// Thong tin thanh toan (Payment Information) interface
export interface ThongTinThanhToanHoaDonDichVu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Related service invoice UUID
   */
  hoa_don_dich_vu: string;

  /**
   * Line number in the payment information
   */
  line: number;

  /**
   * Payment method code
   */
  ma_httt: string;

  /**
   * Payment method name
   */
  ten_httt: string;

  /**
   * Bank account code
   */
  tknh: string;

  /**
   * Bank account name
   */
  ten_tknh: string;

  /**
   * Account code
   */
  tk: string;

  /**
   * Account name
   */
  ten_tk: string;

  /**
   * Document code
   */
  ma_ct: string;

  /**
   * Document name
   */
  ten_ct: string;

  /**
   * Document date
   */
  ngay_ct: string;

  /**
   * Warehouse code
   */
  ma_nk: string;

  /**
   * Warehouse name
   */
  ten_nk: string;

  /**
   * Total payment in foreign currency
   */
  t_tt_nt: number;

  /**
   * Payment document ID
   */
  id_ct_tt: number;

  /**
   * Status
   */
  status: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for HoaDonBanHangDichVu API response
 */
export type HoaDonBanHangDichVuResponse = ApiResponse<HoaDonBanHangDichVu>;

/**
 * Type for creating or updating a HoaDonBanHangDichVu
 */
export interface HoaDonBanHangDichVuInput {
  ma_kh: string;
  ma_so_thue: string;
  pt_tao_yn: boolean;
  ma_httt: string;
  ten_kh_thue: string;
  dia_chi: string;
  ong_ba: string;
  ma_nvbh: string;
  e_mail: string;
  tk: string;
  ma_tt: string;
  dien_giai: string;
  ma_ngv: string;
  unit_id: number;
  i_so_ct: string;
  ma_nk: string;
  so_ct: string;
  ngay_ct: string;
  ngay_lct: string;
  so_ct2: string;
  ma_nt: string;
  ty_gia: number;
  status: string;
  transfer_yn: boolean;
  ma_kh9: string;
  ly_do_huy: string;
  ly_do: string;
  ten_vt_thue: string;
  ghi_chu: string;
  ma_tthddt: string;
  ma_pttt: string;
  so_ct_hddt: string;
  ngay_ct_hddt: string;
  so_ct2_hddt: string;
  ma_mau_ct_hddt: string;
  t_tien_nt2: number;
  t_tien2: number;
  t_thue_nt: number;
  t_thue: number;
  t_ck_nt: number;
  t_ck: number;
  t_tt_nt: number;
  t_tt: number;
  chi_tiet?: ChiTietHoaDonBanHangDichVuInput[];
}

/**
 * Type for creating or updating ChiTietHoaDonBanHangDichVu
 */
export interface ChiTietHoaDonBanHangDichVuInput {
  hoa_don: string;
  line: number;
  ma_dv: string;
  tk_dt: string;
  dvt: string;
  so_luong: number;
  gia_nt2: number;
  tien_nt2: number;
  dien_giai: string;
  tl_ck: number;
  ck_nt: number;
  tk_ck: string;
  ten_tk_ck: string;
  ma_thue: string;
  thue_suat: number;
  tk_thue_co: string;
  ten_tk_thue_co: string;
  thue_nt: number;
  gia2: number;
  tien2: number;
  ck: number;
  thue: number;
  ma_bp: string;
  ma_vv: string;
  ma_hd: string;
  ma_dtt: string;
  ma_ku: string;
  ma_phi: string;
  ma_sp: string;
  ma_lsx: string;
  ma_cp0: string;
}

/**
 * Search form values interface for filtering invoices
 */
export interface HoaDonBanHangDichVuSearchFormValues {
  ngay_ct_tu?: string;
  ngay_ct_den?: string;
  so_ct_tu?: string;
  so_ct_den?: string;
  ma_kh?: string;
  ten_kh_thue?: string;
  ma_nvbh?: string;
  ma_httt?: string;
  status?: string;
  ma_tthddt?: string;
  unit_id?: string;
  ma_nt?: string;
  [key: string]: any;
}

/**
 * Hook return type for managing HoaDonBanHangDichVu data
 */
export interface UseHoaDonBanHangDichVuReturn {
  data: HoaDonBanHangDichVu[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: HoaDonBanHangDichVuSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
  createInvoice: (data: HoaDonBanHangDichVuInput) => Promise<HoaDonBanHangDichVu>;
  updateInvoice: (uuid: string, data: Partial<HoaDonBanHangDichVuInput>) => Promise<HoaDonBanHangDichVu>;
  deleteInvoice: (uuid: string) => Promise<void>;
}
