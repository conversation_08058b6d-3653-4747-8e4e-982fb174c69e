import { useMemo, useState } from 'react';
import { getDataTableColumns } from '../cols-definition';
import { HoaDonBanHangDichVu } from '@/types/schemas';
import { DetailType, TaxType } from '../schema';

export const useTableData = (hoaDonBanDichVus: HoaDonBanHangDichVu[]) => {
  const [detailRows, setDetailRows] = useState<DetailType[]>([]);
  const [taxRows, setTaxRows] = useState<TaxType[]>([]);

  const { totalAmount, totalDiscount, totalTax } = useMemo(() => {
    const amount = detailRows.reduce((sum, row) => sum + (row.amount || 0), 0);
    const discount = detailRows.reduce((sum, row) => sum + (row.discount || 0), 0);
    const tax = detailRows.reduce((sum, row) => sum + (row.tax || 0), 0);
    return { totalAmount: amount, totalDiscount: discount, totalTax: tax };
  }, [detailRows]);

  const tables = [
    {
      name: 'Tất cả',
      rows: hoaDonBanDichVus,
      columns: getDataTableColumns()
    },
    {
      name: 'Chưa ghi sổ',
      rows: hoaDonBanDichVus.filter(row => row.status === 'chua-ghi-so'),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Đã ghi sổ',
      rows: hoaDonBanDichVus.filter(row => row.status === 'da-ghi-so'),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Chờ duyệt',
      rows: hoaDonBanDichVus.filter(row => row.status === 'cho-duyet'),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Khác',
      rows: hoaDonBanDichVus.filter(
        row => row.status !== 'chua-ghi-so' && row.status !== 'da-ghi-so' && row.status !== 'cho-duyet'
      ),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return {
    hoaDonBanDichVus,
    detailRows,
    taxRows,
    totalAmount,
    totalDiscount,
    totalTax,
    tables,
    setDetailRows,
    setTaxRows
  };
};
