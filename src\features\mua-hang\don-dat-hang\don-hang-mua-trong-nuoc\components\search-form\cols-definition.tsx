import { GridColDef } from '@mui/x-data-grid';

export const <PERSON><PERSON><PERSON>VienColDef: GridColDef[] = [
  { field: 'ma_nhan_vien', headerName: 'Mã nhân viên', flex: 1 },
  { field: 'ten_nhan_vien', headerName: 'Tên nhân viên', flex: 2 }
];

export const DonViColDef: GridColDef[] = [
  { field: 'checkbox', headerName: '', width: 50 },
  { field: 'ma_don_vi', headerName: 'Mã đơn vị', flex: 1 },
  { field: 'ten_don_vi', headerName: 'Tên đơn vị', flex: 2 },
  { field: 'id', headerName: 'ID', flex: 2 }
];
