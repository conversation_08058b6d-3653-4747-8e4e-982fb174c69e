/**
 * TypeScript interface for DonViChiNhanh (Branch/Unit) model
 *
 * This interface represents the structure of the EntityUnitModel from the backend.
 * It defines branches or units within an organization.
 */

import { ApiResponse } from '../api.type';
import { Group } from './group.type';

/**
 * Interface for DonViChiNhanh (Branch/Unit) model
 */
export interface DonViChiNhanh {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Name of the branch/unit
   */
  name: string;

  /**
   * Slug identifier for the branch/unit
   */
  slug: string;

  /**
   * Reference to the entity model
   */
  entity: string;

  /**
   * Entity slug (read-only)
   */
  entity_slug: string;

  /**
   * Entity name (read-only)
   */
  entity_name: string;

  /**
   * Document prefix (max 3 characters)
   */
  document_prefix: string;

  /**
   * Whether the branch/unit is active
   */
  active: boolean;

  /**
   * Whether the branch/unit is hidden from UI
   */
  hidden: boolean;

  /**
   * Unit ID (not a primary key)
   */
  unit_id?: number | null;

  /**
   * Unit code (Mã đơn vị)
   */
  ma_unit: string;

  /**
   * Unit name in Vietnamese (Tên đơn vị tiếng Việt)
   */
  ten_unit: string;

  /**
   * Alternative unit name (Tên đơn vị thay thế)
   */
  ten_unit2?: string;

  /**
   * Additional unit name (Tên đơn vị phụ)
   */
  ten_unit3?: string;

  /**
   * Tax code (Mã số thuế)
   */
  ma_so_thue?: string;

  /**
   * Control date (Ngày kiểm soát)
   */
  ngay_ks?: string | null;

  /**
   * Unit group 1 reference (Mã đơn vị 1)
   */
  nh_dv1?: string | null;

  /**
   * Unit group 1 data
   */
  nh_dv1_data?: Group | null;

  /**
   * Entity line 1.1 (Thông tin pháp nhân)
   */
  entity_line11?: string;

  /**
   * Entity line 1.2 (Thông tin pháp nhân)
   */
  entity_line12?: string;

  /**
   * Entity line 2.1 (Công ty)
   */
  entity_line21?: string;

  /**
   * Entity line 2.2 (Công ty)
   */
  entity_line22?: string;

  /**
   * Entity line 3.1 (Địa chỉ)
   */
  entity_line31?: string;

  /**
   * Entity line 3.2 (Địa chỉ)
   */
  entity_line32?: string;

  /**
   * Entity line 4.1
   */
  entity_line41?: string;

  /**
   * Entity line 4.2
   */
  entity_line42?: string;

  /**
   * Entity line 5.1
   */
  entity_line51?: string;

  /**
   * Entity line 5.2
   */
  entity_line52?: string;

  /**
   * Inventory valuation method (Giá tồn)
   */
  gia_ton?: string;

  /**
   * Department flag (Bộ phận)
   */
  bp_yn?: boolean;

  /**
   * Production flag (Lệnh sản xuất)
   */
  lsx_yn?: boolean;

  /**
   * Bank account name (Thông tin ngân hàng)
   */
  bankAccountName?: string;

  /**
   * Bank name (Thông tin ngân hàng)
   */
  bankName?: string;

  /**
   * Chief accountant name (Kế toán trưởng)
   */
  chiefAccountantName?: string;

  /**
   * Director name (Giám đốc)
   */
  directorName?: string;

  /**
   * Cashier name (Thu ngân)
   */
  cashierName?: string;

  /**
   * Storekeeper name (Thủ kho)
   */
  storeKeeperName?: string;

  /**
   * Signature fullname (Chữ ký)
   */
  signatureFullname?: string;

  /**
   * Signature fullname with seal (Chữ ký có dấu)
   */
  signatureFullnameSeal?: string;

  /**
   * Signature image (Hình ảnh)
   */
  ximage?: string;

  /**
   * Address (Địa chỉ)
   */
  address?: string;

  /**
   * District (Quận/Huyện)
   */
  district?: string;

  /**
   * Province (Tỉnh/Thành phố)
   */
  province?: string;

  /**
   * Phone number (Điện thoại)
   */
  phone?: string;

  /**
   * Fax number
   */
  fax?: string;

  /**
   * Email address
   */
  email?: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for DonViChiNhanh API response
 */
export type DonViChiNhanhResponse = ApiResponse<DonViChiNhanh>;

/**
 * Type for creating or updating a DonViChiNhanh
 */
export interface DonViChiNhanhInput {
  /**
   * Name of the branch/unit
   */
  name: string;

  /**
   * Document prefix (max 3 characters)
   */
  document_prefix?: string;

  /**
   * Whether the branch/unit is active
   */
  active?: boolean;

  /**
   * Whether the branch/unit is hidden from UI
   */
  hidden?: boolean;

  /**
   * Unit ID (not a primary key)
   */
  unit_id?: number | null;

  /**
   * Unit code (Mã đơn vị)
   */
  ma_unit: string;

  /**
   * Unit name in Vietnamese (Tên đơn vị tiếng Việt)
   */
  ten_unit: string;

  /**
   * Alternative unit name (Tên đơn vị thay thế)
   */
  ten_unit2?: string;

  /**
   * Additional unit name (Tên đơn vị phụ)
   */
  ten_unit3?: string;

  /**
   * Tax code (Mã số thuế)
   */
  ma_so_thue?: string;

  /**
   * Control date (Ngày kiểm soát)
   */
  ngay_ks?: string | null;

  /**
   * Unit group 1 reference (Mã đơn vị 1)
   */
  nh_dv1?: string | null;

  /**
   * Entity line 1.1 (Thông tin pháp nhân)
   */
  entity_line11?: string;

  /**
   * Entity line 1.2 (Thông tin pháp nhân)
   */
  entity_line12?: string;

  /**
   * Entity line 2.1 (Công ty)
   */
  entity_line21?: string;

  /**
   * Entity line 2.2 (Công ty)
   */
  entity_line22?: string;

  /**
   * Entity line 3.1 (Địa chỉ)
   */
  entity_line31?: string;

  /**
   * Entity line 3.2 (Địa chỉ)
   */
  entity_line32?: string;

  /**
   * Entity line 4.1
   */
  entity_line41?: string;

  /**
   * Entity line 4.2
   */
  entity_line42?: string;

  /**
   * Entity line 5.1
   */
  entity_line51?: string;

  /**
   * Entity line 5.2
   */
  entity_line52?: string;

  /**
   * Inventory valuation method (Giá tồn)
   */
  gia_ton?: string;

  /**
   * Department flag (Bộ phận)
   */
  bp_yn?: boolean;

  /**
   * Production flag (Lệnh sản xuất)
   */
  lsx_yn?: boolean;

  /**
   * Bank account name (Thông tin ngân hàng)
   */
  bankAccountName?: string;

  /**
   * Bank name (Thông tin ngân hàng)
   */
  bankName?: string;

  /**
   * Chief accountant name (Kế toán trưởng)
   */
  chiefAccountantName?: string;

  /**
   * Director name (Giám đốc)
   */
  directorName?: string;

  /**
   * Cashier name (Thu ngân)
   */
  cashierName?: string;

  /**
   * Storekeeper name (Thủ kho)
   */
  storeKeeperName?: string;

  /**
   * Signature fullname (Chữ ký)
   */
  signatureFullname?: string;

  /**
   * Signature fullname with seal (Chữ ký có dấu)
   */
  signatureFullnameSeal?: string;

  /**
   * Signature image (Hình ảnh)
   */
  ximage?: string;

  /**
   * Address (Địa chỉ)
   */
  address?: string;

  /**
   * District (Quận/Huyện)
   */
  district?: string;

  /**
   * Province (Tỉnh/Thành phố)
   */
  province?: string;

  /**
   * Phone number (Điện thoại)
   */
  phone?: string;

  /**
   * Fax number
   */
  fax?: string;

  /**
   * Email address
   */
  email?: string;
}
