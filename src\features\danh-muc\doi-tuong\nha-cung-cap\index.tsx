'use client';

import { useState, useEffect } from 'react';
import { useRowSelection, useDialogState, useNhaCungCapIntegration } from './hooks';
import { DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { exportMainColumns } from './cols-definition';
import { ActionBar, FormDialog } from './components';

export default function SupplierPage() {
  const [showDelete, setShowDelete] = useState(false);
  const [isCopyMode, setIsCopyMode] = useState(false);

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const { showForm, formMode, currentObj, toggleSidebar, closeForm, openAddForm, openEditForm, openViewForm } =
    useDialogState();
  const {
    nhaCungCaps,
    isLoading,
    addNhaCungCap,
    updateNhaCungCap,
    deleteNhaCungCap,
    copyNhaCungCap,
    refreshNhaCungCaps
  } = useNhaCungCapIntegration();

  // Load data when component mounts
  useEffect(() => {
    refreshNhaCungCaps();
  }, [refreshNhaCungCaps]);

  const handleFormSubmitNew = (data: any) => {
    // Map form data to API expected fields
    const enhancedData = {
      // API fields from form (form now uses API field names)
      customer_code: data.customer_code || '',
      customer_name: data.customer_name || '',
      customer_type: data.customer_type || '1',
      is_customer: data.is_customer !== undefined ? data.is_customer : false,
      is_vendor: data.is_vendor !== undefined ? data.is_vendor : true,
      alternative_name: data.alternative_name || '',
      address: data.address || '',
      tax_code: data.tax_code || '',
      contact_person: data.contact_person || '',
      enterprise_name: data.enterprise_name || '',
      phone: data.phone || '',
      fax: data.fax || '',
      email: data.email || '',
      website: data.website || '',
      legal_representative: data.legal_representative || '',
      representative_position: data.representative_position || '',
      representative: data.representative || '',
      credit_limit: data.credit_limit || '',
      bank_account: data.bank_account || '',
      bank_name: data.bank_name || '',
      bank_branch: data.bank_branch || '',
      search_keywords: data.search_keywords || '',
      province: data.province || '',
      notes: data.notes || '',
      status: data.status || 'Active',
      birth_date: data.birth_date || '',
      id_number: data.id_number || '',
      description: data.description || '',
      delivery_address: data.delivery_address || '',
      business_field: data.business_field || '',
      use_einvoice: data.use_einvoice || '',
      einvoice_email: data.einvoice_email || '',
      customer_number: data.customer_number || '',
      active: data.active !== undefined ? data.active : true,
      hidden: data.hidden !== undefined ? data.hidden : false,
      // Include foreign key fields if they have values
      ...(data.sales_rep ? { sales_rep: data.sales_rep } : {}),
      ...(data.account ? { account: data.account } : {}),
      ...(data.payment_term ? { payment_term: data.payment_term } : {}),
      ...(data.payment_method ? { payment_method: data.payment_method } : {}),
      ...(data.customer_group1 ? { customer_group1: data.customer_group1 } : {}),
      ...(data.customer_group2 ? { customer_group2: data.customer_group2 } : {}),
      ...(data.customer_group3 ? { customer_group3: data.customer_group3 } : {}),
      ...(data.region ? { region: data.region } : {}),
      ...(data.entity_model ? { entity_model: data.entity_model } : {})
    };

    if (formMode === 'add' && !isCopyMode) {
      addNhaCungCap(enhancedData)
        .then(() => {
          closeForm();
          clearSelection();
        })
        .catch((error: any) => {});
    } else if (formMode === 'add' && isCopyMode && selectedObj) {
      // Handle copy operation
      copyNhaCungCap(enhancedData)
        .then(() => {
          closeForm();
          clearSelection();
          setIsCopyMode(false);
        })
        .catch((error: any) => {});
    } else if (formMode === 'edit' && selectedObj) {
      const editData = {
        ...enhancedData,
        uuid: selectedObj.uuid
      };

      updateNhaCungCap(selectedObj.uuid, editData)
        .then(() => {
          closeForm();
          clearSelection();
        })
        .catch((error: any) => {});
    }
  };

  const handleDeleteClick = () => {
    setShowDelete(true);
  };

  const handleCloseDelete = () => {
    setShowDelete(false);
  };

  const handleCopyClick = () => {
    if (selectedObj) {
      setIsCopyMode(true);
      openAddForm();
    }
  };

  // Transform data for display
  const transformedRows = nhaCungCaps.map((item: any) => {
    return {
      ...item,
      id: (item as any).uuid,
      checkbox: false
    };
  });

  const tables = [
    {
      name: 'Tất cả',
      rows: transformedRows, // Hiển thị tất cả records
      columns: exportMainColumns
    },
    {
      name: 'Không hoạt động',
      rows: transformedRows.filter((row: any) => row.status === 'Inactive'), // Chỉ hiển thị records có active = false
      columns: exportMainColumns
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          formMode={formMode}
          initialData={isCopyMode && selectedObj ? selectedObj : currentObj}
          selectedObj={isCopyMode ? selectedObj : undefined}
          onSubmit={handleFormSubmitNew}
          onClose={() => {
            closeForm();
            setIsCopyMode(false);
          }}
          onAdd={openAddForm}
          onEdit={() => selectedObj && openEditForm(selectedObj)}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      <DeleteDialog
        open={showDelete}
        onClose={handleCloseDelete}
        selectedObj={selectedObj}
        deleteObj={deleteNhaCungCap}
        clearSelection={clearSelection}
      />

      {!showForm && (
        <>
          <ActionBar
            onAddClick={openAddForm}
            onEditClick={() => selectedObj && openEditForm(selectedObj)}
            onDeleteClick={handleDeleteClick}
            onCopyClick={handleCopyClick}
            onRefreshClick={refreshNhaCungCaps}
            onPrintClick={() => {}}
            onShowSidebar={toggleSidebar}
          />
          <div className='w-full overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && (
              <AritoDataTables
                tables={tables}
                defaultTabIndex={0}
                onRowClick={handleRowClick}
                selectedRowId={selectedRowIndex || undefined}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
}
