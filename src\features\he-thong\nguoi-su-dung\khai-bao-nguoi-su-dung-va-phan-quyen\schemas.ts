import * as Yup from 'yup';

export const userSchema = Yup.object().shape({
  name: Yup.string().required('ID là bắt buộc'),
  full_name: Yup.string().required('Tên đ<PERSON><PERSON> đủ là bắt buộc'),
  email: Yup.string().required('Thư là bắt buộc'),
  phone: Yup.string(),
  is_admin: Yup.boolean(),
  reports_to: Yup.string(),
  department: Yup.string(),
  disabled: Yup.string()
});

export const userInitialValues = {
  name: '',
  full_name: '',
  email: '',
  phone: '',
  is_admin: false,
  reports_to: '',
  department: '',
  disabled: '0'
};
