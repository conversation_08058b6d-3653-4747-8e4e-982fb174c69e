import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const TaxCodeGeneralTab = ({ formMode }: Props) => (
  <div className='p-4'>
    <div className='grid gap-x-6 gap-y-4 lg:grid-cols-1'>
      <FormField
        label='Tài khoản ngầm định'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='tai_khoan_ngam_dinh'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        label='Mã th.toán công nợ'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='ma_thanh_toan_cong_no'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        label='Ph/th th.toán (HĐĐ)'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='phuong_thuc_thanh_toan'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        label='Giới hạn tiền nợ'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='gioi_han_tien_no'
        type='number'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Nhóm 1'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='nhom_1'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        label='Nhóm 2'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='nhom_2'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        label='Nhóm 3'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='nhom_3'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <FormField
        label='Khu vực'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='khu_vuc'
        type='text'
        disabled={formMode === 'view'}
        searchEndpoint='/'
      />
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
        <FormField
          label='Điện thoại'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='dien_thoai'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Fax'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='fax'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
        <FormField
          label='Thư (Email)'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='email'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Trang chủ (Website)'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='email'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        label='Ghi chú'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='ghi_chu'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Trạng thái'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='trang_thai'
        type='select'
        disabled={formMode === 'view'}
        options={[
          { value: 1, label: 'Còn sử dụng' },
          { value: 0, label: 'Không sử dụng' }
        ]}
      />
    </div>
  </div>
);
