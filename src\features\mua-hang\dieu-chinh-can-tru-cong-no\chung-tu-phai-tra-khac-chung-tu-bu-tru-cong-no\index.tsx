'use client';
import { useState } from 'react';
import Split from 'react-split';
import { AritoDataTables, AritoColoredDot, DeleteDialog } from '@/components/custom/arito';
import { exportBottomColumns, exportMainColumns } from './cols-definition';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { ActionBar, FormDialog, SearchForm } from './components';
import { useFormState, useFormSubmit } from './hooks';
import { useRows } from '@/hooks';

const ChungTuPhaiTraChungTuBuTruCongNo = () => {
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();

  const [showSearchForm, setShowSearchForm] = useState<boolean>(false);

  const { submitForm, error } = useFormSubmit();

  const handleFormSubmit = async (data: any) => {
    try {
      const success = await submitForm(data, formMode);
      if (success) {
        handleCloseForm();
      } else {
        console.error('Form submission failed:', error);
      }
    } catch (err: any) {
      console.error('Error submitting form:', err);
    }
  };

  const onSearchClick = () => {
    setShowSearchForm(true);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: [],
      columns: exportMainColumns(handleViewClick, handleEditClick)
    },
    {
      name: 'Chưa ghi sổ',
      rows: [],
      columns: exportMainColumns(handleViewClick, handleEditClick),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: [],
      columns: exportMainColumns(handleViewClick, handleEditClick),
      icon: <AritoColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Đã ghi sổ',
      rows: [],
      columns: exportMainColumns(handleViewClick, handleEditClick),
      icon: <AritoColoredDot color='#3D85C6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onEditClick={() => selectedObj && handleEditClick()}
            onDeleteClick={() => selectedObj && handleDeleteClick()}
            onCopyClick={() => selectedObj && handleCopyClick()}
            onSearchClick={onSearchClick}
            onRefreshClick={() => {}}
            onPrintClick={() => {}}
            className='border-b border-gray-200'
          />

          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedRowIndex || undefined}
              />
            </div>
            <div className='w-full overflow-hidden'>
              <InputTable rows={[]} columns={exportBottomColumns} mode='view' />
            </div>
          </Split>
        </>
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : undefined
          }
          onClose={handleCloseForm}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showSearchForm && (
        <SearchForm
          open={showSearchForm}
          onClose={setShowSearchForm}
          initialData={[]}
          handleSubmit={() => {}}
          formMode='add'
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={() => {
            return Promise.resolve();
          }}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
};

export default ChungTuPhaiTraChungTuBuTruCongNo;
