import { useState } from 'react';

interface UseDialogStateReturn {
  showForm: boolean;
  formMode: 'add' | 'edit' | 'view';
  currentObj: any | null;
  showSidebar: boolean;
  showConfirm: boolean;

  toggleSidebar: () => void;
  closeForm: () => void;
  closeConfirm: () => void;
  openAddForm: () => void;
  openViewForm: (obj: any) => void;
  openEditForm: (obj: any) => void;
  openCopyForm: (obj: any) => void;
  openConfirm: () => void;
  handleFormSubmit: (data: any) => void;
  handleConfirm: (data: any) => void;
}

const useDialogState = (): UseDialogStateReturn => {
  const [showForm, setShowForm] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [showSidebar, setShowSidebar] = useState(false);

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  const closeForm = () => {
    setShowForm(false);
  };

  const closeConfirm = () => {
    setShowConfirm(false);
  };

  const openEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const openViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const openAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const openCopyForm = (obj: any) => {
    setFormMode('add');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const openConfirm = () => {
    setShowConfirm(true);
  };

  const handleFormSubmit = (data: any) => {
    console.log(data);
  };

  const handleConfirm = (data: any) => {
    console.log(data);
  };

  return {
    showForm,
    formMode,
    currentObj,
    showSidebar,
    showConfirm,

    toggleSidebar,
    closeForm,
    closeConfirm,
    openAddForm,
    openEditForm,
    openViewForm,
    openCopyForm,
    openConfirm,
    handleFormSubmit,
    handleConfirm
  };
};

export default useDialogState;
