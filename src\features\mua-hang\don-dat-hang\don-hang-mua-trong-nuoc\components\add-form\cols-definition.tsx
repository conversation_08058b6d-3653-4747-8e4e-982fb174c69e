import { GridColDef } from '@mui/x-data-grid';

export const NhanVienColDef: GridColDef[] = [
  { field: 'ma_nv', headerName: 'Mã nhân viên', flex: 1 },
  { field: 'ten_nv', headerName: 'Tên nhân viên', flex: 2 }
];

export const MaNCCSearchColBasicInfo: GridColDef[] = [
  { field: 'code', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'supplier_name', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'tax_code', headerName: 'Công nợ phải trả', flex: 1 },
  { field: 'address', headerName: 'Mã số thuế', flex: 1 },
  { field: 'employee', headerName: 'email', flex: 1 },
  { field: 'phone', headerName: 'Điện thoại', flex: 1 }
];

export const SoChungTuSearchColBasicInfo: GridColDef[] = [
  { field: 'ma_quyen', headerName: 'Mã quyển', flex: 1 },
  { field: 'ten_quyen', headerName: 'Tên quyển', flex: 2 },
  { field: 'Số khai báo', headerName: 'Số khai báo', flex: 1 }
];

export const DetailItemColumns: GridColDef[] = [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', flex: 1 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', flex: 2 },
  { field: 'dvt', headerName: 'Đvt', flex: 1 },
  { field: 'so_luong', headerName: 'Số lượng', flex: 1 },
  { field: 'gia_vnd', headerName: 'Giá VND', flex: 1 },
  { field: 'tien_vnd', headerName: 'Tiền VND', flex: 1 },
  { field: 'ngay_giao', headerName: 'Ngày giao', flex: 1 },
  { field: 'thue_suat', headerName: 'Thuế suất', flex: 1 },
  { field: 'thue_suat_phan_tram', headerName: 'Thuế suất(%)', flex: 1 },
  { field: 'thue_vnd', headerName: 'Thuế VND', flex: 1 },
  { field: 'bo_phan', headerName: 'Bộ phận', flex: 1 },
  { field: 'vu_viec', headerName: 'Vụ việc', flex: 1 },
  { field: 'hop_dong', headerName: 'Hợp đồng', flex: 1 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', flex: 1 },
  { field: 'khe_uoc', headerName: 'Khế ước', flex: 1 },
  { field: 'phi', headerName: 'Phí', flex: 1 },
  { field: 'san_pham', headerName: 'Sản phẩm', flex: 1 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', flex: 1 },
  { field: 'cp_khong_hop_le', headerName: 'C/p không h/lệ', flex: 1 },
  { field: 'so_luong_hoa_don', headerName: 'Số lượng hóa đơn', flex: 1 },
  { field: 'so_luong_phieu_nhap', headerName: 'Số lượng phiếu nhập', flex: 1 },
  { field: 'so_don_hang', headerName: 'Số đơn hàng', flex: 1 },
  { field: 'dong', headerName: 'Dòng', flex: 1 },
  { field: 'so_phieu_so_sanh_gia', headerName: 'Số phiếu so sánh giá', flex: 1 },
  { field: 'dong_so_sanh', headerName: 'Dòng', flex: 1 }
];

export const NoiNhanColDef: GridColDef[] = [
  { field: 'dia_chi', headerName: 'Địa chỉ', flex: 1 },
  { field: 'dien_giai', headerName: 'Diễn giải', flex: 1 }
];

export const PhuongThucThanhToanColDef: GridColDef[] = [
  { field: 'ma_phuong_thuc', headerName: 'Mã phương thức', flex: 1 },
  { field: 'ten_phuong_thuc', headerName: 'Tên phương thức', flex: 1 }
];
