'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  getAdvancePaymentVoucherColumns,
  getAdvancePaymentVoucherSubColumns
} from '@/features/mua-hang/thanh-toan-tam-ung/phieu-thanh-toan-tam-ung/cols-definition';
import { BasicInfoTab } from '@/features/mua-hang/thanh-toan-tam-ung/phieu-thanh-toan-tam-ung/components/BasicInfoTab';
import { DetailsTab } from '@/features/mua-hang/thanh-toan-tam-ung/phieu-thanh-toan-tam-ung/components/DetailsTab';
import { ActionBar } from '@/features/mua-hang/thanh-toan-tam-ung/phieu-thanh-toan-tam-ung/components/ActionBar';
import { BottomBar } from '@/features/mua-hang/thanh-toan-tam-ung/phieu-thanh-toan-tam-ung/components/BottomBar';
import { TaxesTab } from '@/features/mua-hang/thanh-toan-tam-ung/phieu-thanh-toan-tam-ung/components/TaxesTab';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoDataTables from '@/components/custom/arito/data-tables';
import ColoredDot from '@/components/custom/arito/colored-dot';
import { AritoForm } from '@/components/custom/arito/form';
import FromPopup from '../popup/FormPopup';

export default function PhieuThanhToanTamUngPage({ initialRows }: { initialRows: any[] }) {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [showSearchForm, setShowSearchForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [invoiceRows, setInvoiceRows] = useState<any[]>(initialRows);
  //Add and Edit
  const [inputInvoiceDetail, setInputInvoiceDetail] = useState<any[]>([]);
  const [inputExpense, setInputExpense] = useState<any[]>([]);
  const [inputExpenseDetail, setInputExpenseDetail] = useState<any[]>([]);
  const [inputTax, setInputTax] = useState<any[]>([]);

  // Helper function to calculate totals from current form state
  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.invoiceDetails) {
      setInputInvoiceDetail([...obj.invoiceDetails]);
    } else {
      setInputInvoiceDetail([]);
    }
    if (obj.expenseDetails) {
      setInputExpenseDetail([...obj.expenseDetails]);
    } else if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    if (obj.expenses) {
      setInputExpense([...obj.expenses]);
    } else {
      setInputExpense([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.invoiceDetails) {
      setInputInvoiceDetail([...obj.invoiceDetails]);
    } else {
      setInputInvoiceDetail([]);
    }
    if (obj.expenseDetails) {
      setInputExpenseDetail([...obj.expenseDetails]);
    } else if (obj.invoiceExpense) {
      setInputExpenseDetail([...obj.invoiceExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    if (obj.expenses) {
      setInputExpense([...obj.expenses]);
    } else {
      setInputExpense([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputInvoiceDetail([]);
    setInputExpenseDetail([]);
    setInputExpense([]);
    setInputTax([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    setShowForm(false);
  };

  const handleOpenSearchForm = () => {
    setShowSearchForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const order = params.row as any;
    setSelectedObj(order);
    setInputExpense(order.expenses);
    setInputExpenseDetail(order.expenseDetails);
    setInputTax(order.taxes);
    setInputInvoiceDetail(order.invoiceDetails);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: invoiceRows,
      columns: getAdvancePaymentVoucherColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Chưa ghi sổ',
      rows: invoiceRows.filter(row => row.status === 'Chưa ghi sổ'),
      columns: getAdvancePaymentVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <ColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: invoiceRows.filter(row => row.status === 'Chờ duyệt'),
      columns: getAdvancePaymentVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <ColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Đã ghi sổ',
      rows: invoiceRows.filter(row => row.status === 'Đã ghi sổ'),
      columns: getAdvancePaymentVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <ColoredDot color='#3B82F6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'details',
                label: 'Chi tiết',
                component: (
                  <DetailsTab value={inputInvoiceDetail} onChange={setInputInvoiceDetail} formMode={formMode} />
                )
              },
              {
                id: 'taxes',
                label: 'Thuế',
                component: <TaxesTab value={inputTax} onChange={setInputTax} formMode={formMode} />
              },
              {
                id: 'filekem',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Phiếu thanh toán tạm ứng'}
            bottomBar={<BottomBar totalAmount={0} totalPayment={0} totalTax={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onSearchClick={handleOpenSearchForm}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.invoiceDetails || []}
                columns={getAdvancePaymentVoucherSubColumns}
                mode={formMode}
              />
            </div>
          </Split>
        </>
      )}

      {showSearchForm && (
        <FromPopup showFromPopUpForm={showSearchForm} setShowFromPopUpForm={setShowSearchForm} formMode={'add'} />
      )}
    </div>
  );
}
