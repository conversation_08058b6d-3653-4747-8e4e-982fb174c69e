import { useState, useEffect, useCallback } from 'react';
import {
  HoaDonBanHangDichVu,
  HoaDonBanHangDichVuInput,
  HoaDonBanHangDichVuResponse,
  ChiTietHoaDonBanHangDichVu,
  ChiTietHoaDonBanHangDichVuInput,
  ThongTinThanhToanHoaDonDichVu
} from '@/types/schemas/hoa-don-ban-dich-vu.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseHoaDonBanDichVuReturn {
  hoaDonBanDichVus: HoaDonBanHangDichVu[];
  isLoading: boolean;
  addHoaDonBanDichVu: (newHoaDonBanDichVu: HoaDonBanHangDichVuInput) => Promise<void>;
  updateHoaDonBanDichVu: (uuid: string, updatedHoaDonBanDichVu: HoaDonBanHangDichVuInput) => Promise<void>;
  deleteHoaDonBanDichVu: (uuid: string) => Promise<void>;
  refreshHoaDonBanDichVus: () => Promise<void>;
  getHoaDonBanDichVuById: (uuid: string) => Promise<HoaDonBanHangDichVu | null>;
  getHoaDonBanDichVuByCode: (invoiceCode: string) => Promise<HoaDonBanHangDichVu | null>;
  getHoaDonBanDichVuByCustomer: (customerId: string) => Promise<HoaDonBanHangDichVu[]>;
  getHoaDonBanDichVuByDateRange: (startDate: string, endDate: string) => Promise<HoaDonBanHangDichVu[]>;
  getHoaDonBanDichVuByStatus: (status: string) => Promise<HoaDonBanHangDichVu[]>;
  // Chi tiet methods
  addChiTietHoaDon: (hoaDonUuid: string, chiTiet: ChiTietHoaDonBanHangDichVuInput) => Promise<void>;
  updateChiTietHoaDon: (
    hoaDonUuid: string,
    chiTietUuid: string,
    chiTiet: ChiTietHoaDonBanHangDichVuInput
  ) => Promise<void>;
  deleteChiTietHoaDon: (hoaDonUuid: string, chiTietUuid: string) => Promise<void>;
  getChiTietHoaDon: (hoaDonUuid: string) => Promise<ChiTietHoaDonBanHangDichVu[]>;
  // Thanh toan methods
  addThongTinThanhToan: (hoaDonUuid: string, thanhToan: ThongTinThanhToanHoaDonDichVu) => Promise<void>;
  updateThongTinThanhToan: (
    hoaDonUuid: string,
    thanhToanUuid: string,
    thanhToan: ThongTinThanhToanHoaDonDichVu
  ) => Promise<void>;
  deleteThongTinThanhToan: (hoaDonUuid: string, thanhToanUuid: string) => Promise<void>;
  getThongTinThanhToan: (hoaDonUuid: string) => Promise<ThongTinThanhToanHoaDonDichVu[]>;
}

/**
 * Hook for managing HoaDonBanDichVu (Service Sales Invoice) data
 *
 * This hook provides functions to fetch, create, update, and delete service sales invoices.
 */
export const useHoaDonBanDichVu = (initialHoaDonBanDichVus: HoaDonBanHangDichVu[] = []): UseHoaDonBanDichVuReturn => {
  const [hoaDonBanDichVus, setHoaDonBanDichVus] = useState<HoaDonBanHangDichVu[]>(initialHoaDonBanDichVus);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchHoaDonBanDichVus = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<HoaDonBanHangDichVuResponse>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/`
      );
      setHoaDonBanDichVus(response.data.results);
    } catch (error) {
      console.error('Error fetching service sales invoices:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  const getHoaDonBanDichVuById = async (uuid: string): Promise<HoaDonBanHangDichVu | null> => {
    if (!entity?.slug) return null;

    try {
      const response = await api.get<HoaDonBanHangDichVu>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${uuid}/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching service sales invoice with UUID ${uuid}:`, error);
      return null;
    }
  };

  const getHoaDonBanDichVuByCode = async (invoiceCode: string): Promise<HoaDonBanHangDichVu | null> => {
    if (!entity?.slug) return null;

    setIsLoading(true);
    try {
      const response = await api.get<HoaDonBanHangDichVuResponse>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/?so_ct=${invoiceCode}`
      );

      if (response.data.results.length > 0) {
        return response.data.results[0];
      }
      return null;
    } catch (error) {
      console.error('Error fetching service sales invoice by code:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const getHoaDonBanDichVuByCustomer = async (customerId: string): Promise<HoaDonBanHangDichVu[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<HoaDonBanHangDichVuResponse>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/?ma_kh=${customerId}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching service sales invoices by customer:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const getHoaDonBanDichVuByDateRange = async (startDate: string, endDate: string): Promise<HoaDonBanHangDichVu[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<HoaDonBanHangDichVuResponse>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/?ngay_ct__gte=${startDate}&ngay_ct__lte=${endDate}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching service sales invoices by date range:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const getHoaDonBanDichVuByStatus = async (status: string): Promise<HoaDonBanHangDichVu[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<HoaDonBanHangDichVuResponse>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/?status=${status}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching service sales invoices by status:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const addHoaDonBanDichVu = async (newHoaDonBanDichVu: HoaDonBanHangDichVuInput): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.post<HoaDonBanHangDichVu>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/`,
        newHoaDonBanDichVu
      );
      const addedHoaDonBanDichVu: HoaDonBanHangDichVu = response.data;
      setHoaDonBanDichVus(prev => [...prev, addedHoaDonBanDichVu]);
    } catch (error) {
      console.error('Error adding service sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateHoaDonBanDichVu = async (
    uuid: string,
    updatedHoaDonBanDichVu: HoaDonBanHangDichVuInput
  ): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.put<HoaDonBanHangDichVu>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${uuid}/`,
        updatedHoaDonBanDichVu
      );
      const updatedData: HoaDonBanHangDichVu = response.data;
      setHoaDonBanDichVus(prev => prev.map(item => (item.uuid === uuid ? updatedData : item)));
    } catch (error) {
      console.error('Error updating service sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteHoaDonBanDichVu = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${uuid}/`);
      setHoaDonBanDichVus(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting service sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Chi tiet hoa don methods
  const getChiTietHoaDon = async (hoaDonUuid: string): Promise<ChiTietHoaDonBanHangDichVu[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<{ results: ChiTietHoaDonBanHangDichVu[] }>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/chi-tiet/`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching invoice details:', error);
      return [];
    }
  };

  const addChiTietHoaDon = async (hoaDonUuid: string, chiTiet: ChiTietHoaDonBanHangDichVuInput): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.post(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/chi-tiet/`,
        chiTiet
      );
      await fetchHoaDonBanDichVus(); // Refresh the list
    } catch (error) {
      console.error('Error adding invoice detail:', error);
      throw error;
    }
  };

  const updateChiTietHoaDon = async (
    hoaDonUuid: string,
    chiTietUuid: string,
    chiTiet: ChiTietHoaDonBanHangDichVuInput
  ): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.put(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/chi-tiet/${chiTietUuid}/`,
        chiTiet
      );
      await fetchHoaDonBanDichVus(); // Refresh the list
    } catch (error) {
      console.error('Error updating invoice detail:', error);
      throw error;
    }
  };

  const deleteChiTietHoaDon = async (hoaDonUuid: string, chiTietUuid: string): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.delete(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/chi-tiet/${chiTietUuid}/`
      );
      await fetchHoaDonBanDichVus(); // Refresh the list
    } catch (error) {
      console.error('Error deleting invoice detail:', error);
      throw error;
    }
  };

  // Thong tin thanh toan methods
  const getThongTinThanhToan = async (hoaDonUuid: string): Promise<ThongTinThanhToanHoaDonDichVu[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<{ results: ThongTinThanhToanHoaDonDichVu[] }>(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/thanh-toan/`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching payment information:', error);
      return [];
    }
  };

  const addThongTinThanhToan = async (hoaDonUuid: string, thanhToan: ThongTinThanhToanHoaDonDichVu): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.post(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/thanh-toan/`,
        thanhToan
      );
      await fetchHoaDonBanDichVus(); // Refresh the list
    } catch (error) {
      console.error('Error adding payment information:', error);
      throw error;
    }
  };

  const updateThongTinThanhToan = async (
    hoaDonUuid: string,
    thanhToanUuid: string,
    thanhToan: ThongTinThanhToanHoaDonDichVu
  ): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.put(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/thanh-toan/${thanhToanUuid}/`,
        thanhToan
      );
      await fetchHoaDonBanDichVus(); // Refresh the list
    } catch (error) {
      console.error('Error updating payment information:', error);
      throw error;
    }
  };

  const deleteThongTinThanhToan = async (hoaDonUuid: string, thanhToanUuid: string): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.delete(
        `/entities/${entity.slug}/erp/ban-hang/hoa-don-ban-ra/hoa-don-ban-dich-vu/${hoaDonUuid}/thanh-toan/${thanhToanUuid}/`
      );
      await fetchHoaDonBanDichVus(); // Refresh the list
    } catch (error) {
      console.error('Error deleting payment information:', error);
      throw error;
    }
  };

  const refreshHoaDonBanDichVus = async (): Promise<void> => {
    await fetchHoaDonBanDichVus();
  };

  useEffect(() => {
    if (entity?.slug) {
      fetchHoaDonBanDichVus();
    }
  }, [entity?.slug, fetchHoaDonBanDichVus]);

  return {
    hoaDonBanDichVus,
    isLoading,
    addHoaDonBanDichVu,
    updateHoaDonBanDichVu,
    deleteHoaDonBanDichVu,
    refreshHoaDonBanDichVus,
    getHoaDonBanDichVuById,
    getHoaDonBanDichVuByCode,
    getHoaDonBanDichVuByCustomer,
    getHoaDonBanDichVuByDateRange,
    getHoaDonBanDichVuByStatus,
    // Chi tiet methods
    addChiTietHoaDon,
    updateChiTietHoaDon,
    deleteChiTietHoaDon,
    getChiTietHoaDon,
    // Thanh toan methods
    addThongTinThanhToan,
    updateThongTinThanhToan,
    deleteThongTinThanhToan,
    getThongTinThanhToan
  };
};
