import * as Yup from 'yup';

export interface ValidationError {
  type: string;
  message: string;
  position: string;
}

interface ValidateParams {
  data: { [key: string]: any };
  schema: Yup.ObjectSchema<any>;
  rowNumber: number; // e.g., 2, 3, 4...
}

const getColumnLetter = (index: number): string => {
  let letter = '';
  while (index >= 0) {
    letter = String.fromCharCode((index % 26) + 65) + letter;
    index = Math.floor(index / 26) - 1;
  }
  return letter;
};

export const validateRow = ({ data, schema, rowNumber }: ValidateParams): ValidationError[] => {
  try {
    schema.validateSync(data, { abortEarly: false });
    return [];
  } catch (error: any) {
    const keys = Object.keys(data); // Get field order
    const errors: ValidationError[] = error.inner.map((err: any) => {
      const field = err.path; // e.g., "price"
      const index = keys.indexOf(field); // Find its position
      const column = index >= 0 ? getColumnLetter(index) : '?'; // Convert index to column letter
      const position = `${column}${rowNumber}`; // E.g., B2

      return {
        type: 'error',
        message: err.message,
        position: position
      };
    });

    return errors;
  }
};
