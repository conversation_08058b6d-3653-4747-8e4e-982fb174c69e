import { useState } from 'react';
import { TKNoSearchColBasicInfo } from '@/components/cac-loai-form/popup-form-type-2/cols-definition';
import BasicInfoTabType1 from '@/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1';
import BasicInfoTabType2 from '@/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2';
import TaxCodePopUpForm from '@/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm';
import { CurrencyField } from '@/components/custom/arito/form/form-field/components';
import { NhanVienColDef, SoChungTuSearchColBasicInfo } from './cols-definition';
import { Type1Tabs } from '@/components/cac-loai-form/popup-form-type-1/Tabs';
import { Type2Tabs } from '@/components/cac-loai-form/popup-form-type-2/Tabs';
import { FormField } from '@/components/custom/arito/form/form-field';
import { MaNCCSearchColBasicInfo } from './cols-definition';
import { AritoIcon } from '@/components/custom/arito';

interface Props {
  formMode: 'add' | 'edit' | 'view';
  showTaxCodePopupForm: boolean;
  setShowTaxCodePopupForm: (value: boolean) => void;
}
export const BasicInfoTab = ({ formMode, setShowTaxCodePopupForm, showTaxCodePopupForm }: Props) => {
  return (
    <div className='flex p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
        {/* Cột trái */}
        <div className='col-span-4 space-y-2'>
          <div className='flex items-center gap-x-6'>
            <FormField
              className='flex w-full items-center gap-x-2'
              type='text'
              labelClassName='min-w-[160px]'
              label='Mã nhà cung cấp'
              name='supplierCode'
              disabled={formMode === 'view'}
              withSearch={true}
              searchEndpoint='accounting/supplier'
              searchColumns={MaNCCSearchColBasicInfo}
              actionButtons={['add', 'edit']}
              headerFields={<BasicInfoTabType1 formMode={formMode} />}
              tabs={Type1Tabs({ formMode })}
            />
            <div className='flex items-center gap-x-2'>
              <FormField
                className='flex items-center gap-x-1'
                type='text'
                labelClassName='min-w-[160px]'
                label='Mã số thuế'
                name='taxCode'
                disabled={formMode === 'view'}
              />
              <div className='cursor-pointer'>
                <AritoIcon icon={15} />
              </div>
              <div className='cursor-pointer' onClick={() => setShowTaxCodePopupForm(true)}>
                <AritoIcon icon={58} />
              </div>
            </div>
          </div>
          <div className='flex items-center gap-x-4'>
            <FormField
              className='flex items-center gap-x-12'
              type='text'
              labelClassName='min-w-[120px]'
              label='Tên nhà cung cấp'
              name='supplierName'
              disabled={formMode === 'view'}
              placeholder='Tên nhà cung cấp'
            />
            <FormField
              label='Dư công nợ'
              className='flex items-center'
              type='number'
              labelClassName='min-w-[100px]'
              name='debt'
              value={0}
              disabled={formMode === 'view'}
            />
          </div>
          <div className='flex items-center gap-x-4'>
            <FormField
              className='flex items-center gap-x-12'
              label='Địa chỉ'
              labelClassName='min-w-[120px]'
              name='address'
              type='text'
              disabled={formMode === 'view'}
              placeholder='Địa chỉ'
            />
            <FormField
              label='Người giao hàng'
              className='flex items-center'
              name='deliveryPerson'
              labelClassName='min-w-[100px]'
              type='text'
              disabled={formMode === 'view'}
              placeholder='Người giao hàng'
            />
          </div>
          <div className='flex items-center gap-x-16'>
            <FormField
              className='flex items-center gap-x-12'
              label='Mã nhân viên'
              name='ma_nhan_vien'
              type='text'
              labelClassName='min-w-[120px]'
              disabled={formMode === 'view'}
              withSearch
              searchEndpoint='accounting/account'
              searchColumns={NhanVienColDef}
            />
            <FormField
              label='Email'
              className='flex w-[280px] items-center'
              name='email'
              labelClassName='min-w-[100px]'
              type='text'
              disabled={formMode === 'view'}
              placeholder='Email'
            />
          </div>
          <div className='flex items-center gap-x-16'>
            <FormField
              className='flex items-center gap-x-12'
              label='Tài khoản có'
              name='tai_khoan_co'
              type='text'
              labelClassName='min-w-[120px]'
              disabled={formMode === 'view'}
              withSearch
              searchEndpoint='accounting/account'
              searchColumns={TKNoSearchColBasicInfo}
              actionButtons={['add', 'edit']}
              headerFields={<BasicInfoTabType2 formMode={formMode} />}
              tabs={Type2Tabs({ formMode })}
            />
            <FormField
              label='Hạn thanh toán'
              className='flex w-[280px] items-center'
              name='status'
              labelClassName='min-w-[100px]'
              type='select'
              disabled={formMode === 'view'}
              options={[
                { value: 'create', label: 'Đã ghi sổ' },
                { value: 'pending', label: 'Chờ duyệt' },
                { value: 'approved', label: 'Chưa ghi sổ' }
              ]}
              placeholder='Hạn thanh toán'
            />
          </div>
          <FormField
            className='flex items-center gap-x-4'
            label='Diễn giải'
            name='description'
            labelClassName='min-w-[150px]'
            type='text'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Cột phải */}
        <div className='grid grid-cols-1 space-y-1 lg:col-span-1'>
          <FormField
            className='flex max-w-[300px] items-center justify-between'
            label='Số chứng từ'
            withSearch={true}
            labelClassName='min-w-[120px]'
            type='text'
            name='bookNumber'
            disabled={formMode === 'view'}
            searchEndpoint='accounting/transaction'
            searchColumns={SoChungTuSearchColBasicInfo}
          />
          <FormField
            label='Ngày chứng từ'
            className='flex max-w-[300px] items-center justify-between'
            type='date'
            labelClassName='min-w-[120px]'
            name='dhmCode'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Ngày hiệu lực'
            className='flex max-w-[300px] items-center justify-between'
            type='date'
            labelClassName='min-w-[120px]'
            name='effectiveDate'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[300px] items-center justify-between'
            label='Số hợp đồng'
            labelClassName='min-w-[120px]'
            type='text'
            name='contractNumber'
            disabled={formMode === 'view'}
          />
          <CurrencyField name='currency' disabled={formMode === 'view'} labelClassName='w-[150px]' />
          <FormField
            label='Trạng thái'
            className='flex max-w-[300px] items-center justify-between'
            name='status'
            type='select'
            labelClassName='min-w-[120px]'
            disabled={formMode === 'view'}
            options={[
              { value: 'create', label: 'Lập chứng từ' },
              { value: 'pending', label: 'Chờ duyệt' },
              { value: 'approved', label: 'Đã duyệt' }
            ]}
          />
          <FormField
            label='Dữ liệu được nhận'
            className='grid grid-cols-[200px,1fr] items-center gap-y-2'
            name='status'
            type='checkbox'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
      {showTaxCodePopupForm && (
        <TaxCodePopUpForm
          showTaxCodePopupForm={showTaxCodePopupForm}
          setShowTaxCodePopupForm={setShowTaxCodePopupForm}
          formMode={formMode}
          currentObj={{}}
        />
      )}
    </div>
  );
};
