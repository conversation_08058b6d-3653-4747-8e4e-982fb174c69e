import { useState, useEffect } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { FormValues, FormSchema, initialFormValues } from '../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import BasicInfoTab from './form-fields/BasicInfoTab';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { ConfirmDialog } from '.';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: FormValues) => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  formMode: 'add' | 'edit' | 'view';
}

const FormDialog = ({ open, onClose, onSubmit, onAdd, onEdit, onDelete, onCopy, formMode }: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);

  const handleSubmit = (data: FormValues) => {
    onSubmit(data);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const viewActions = [
    { onClick: onAdd, icon: 571, text: 'Thêm' },
    { onClick: onEdit, icon: 9, text: 'Sửa' },
    { onClick: onDelete, icon: 8, text: 'Xoá' },
    { onClick: onCopy, icon: 11, text: 'Sao chép' },
    { onClick: handleClose, icon: 885, text: 'Đóng' }
  ];

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={`${formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Danh mục phương tiện vận chuyển'}`}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={FormSchema}
          onSubmit={handleSubmit}
          initialData={initialFormValues}
          className='w-[800px]'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
              <BasicInfoTab formMode={formMode} />
            </div>
          }
          bottomBar={
            <>
              {formMode === 'view' && (
                <BottomBar
                  mode={formMode}
                  onSubmit={() => {}}
                  onClose={handleClose}
                  onAdd={onAdd}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onCopy={onCopy}
                />
              )}
              {formMode !== 'view' && <BottomBar mode={formMode} onSubmit={() => {}} onClose={handleClose} />}
            </>
          }
          classNameBottomBar='relative'
        />
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
