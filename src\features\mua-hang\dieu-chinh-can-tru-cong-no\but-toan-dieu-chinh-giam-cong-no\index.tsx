'use client';
import { GridRowParams } from '@mui/x-data-grid';
import React, { useState } from 'react';
import Split from 'react-split';
import { filter } from 'lodash';
import { ActionBar, BottomBar, BasicInfoTab, DetailTab, ExchangeRateTab, OtherTab } from './components';
import { exportBottomColumns, exportMainColumns } from './cols-definition';
import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoForm } from '@/components/custom/arito';
import { SearchForm } from './components/search-form';
import { PrintForm } from './components/print-form';
import { filterableCols } from './types/filterCols';
import { calculateTotals } from './utils/Caculate';
import { filterValues } from './types/filterTab';

const ButToanDieuChinhGiamCongNoPage = () => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [inputDetails, setInputDetails] = useState<any[]>([]);
  const [rows, setRows] = useState<any[]>([]);
  const [showSearchForm, setShowSearchForm] = useState<boolean>(false);
  const [showPrintForm, setShowPrintForm] = useState<boolean>(false);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputDetails([]);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const obj = params.row as any;
    setSelectedObj(obj);
    setInputDetails(obj.details || []);
  };

  const handleFormSubmit = async (data: any) => {};

  const onSearchClick = () => {
    setShowSearchForm(true);
  };

  const onPrintClick = () => {
    setShowPrintForm(true);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: rows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: exportMainColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm ? (
        <div className='flex h-full flex-col'>
          <div className='flex-1 overflow-y-auto pb-[120px]'>
            <AritoForm
              mode={formMode}
              initialData={currentObj || undefined}
              onSubmit={handleFormSubmit}
              onClose={handleCloseForm}
              subTitle='Bút toán điều chỉnh giảm công nợ'
              headerFields={<BasicInfoTab formMode={formMode} />}
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'Ty-gia',
                  label: 'Tỷ giá',
                  component: <ExchangeRateTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'khac',
                  label: 'Khác',
                  component: <OtherTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                }
              ]}
            />
          </div>
          <div className='fixed bottom-0 left-0 right-0 bg-white'>
            <BottomBar totalMoney={calculateTotals(inputDetails)} totalPayment={0} formMode={formMode} />
          </div>
        </div>
      ) : (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={() => {}}
            onCopyClick={() => {}}
            onSearchClick={onSearchClick}
            onRefreshClick={() => {}}
            onPrintClick={onPrintClick}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.id || undefined}
                filterableColumns={filterableCols}
              />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.details || []}
                columns={exportBottomColumns}
                mode='view'
                tableActionButtons={['export', 'pin']}
              />
            </div>
          </Split>
        </>
      )}
      {showSearchForm && (
        <SearchForm
          open={showSearchForm}
          onClose={setShowSearchForm}
          initialData={[]}
          handleSubmit={() => {}}
          formMode='add'
        />
      )}

      {showPrintForm && (
        <PrintForm
          open={showPrintForm}
          onClose={() => setShowPrintForm(false)}
          initialData={currentObj || {}}
          handleSubmit={() => {}}
          formMode='add'
        />
      )}
    </div>
  );
};

export default ButToanDieuChinhGiamCongNoPage;
