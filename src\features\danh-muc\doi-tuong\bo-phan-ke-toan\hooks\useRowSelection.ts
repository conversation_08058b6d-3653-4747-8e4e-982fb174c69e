import { useState } from 'react';
import { BoPhan } from '@/types/schemas/bo-phan.type';

export const useRowSelection = () => {
  const [selectedObj, setSelectedObj] = useState<BoPhan | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const handleRowClick = (params: any) => {
    const row = params.row;
    setSelectedObj(row);
    setSelectedRowIndex(row.id.toString());
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection
  };
};
