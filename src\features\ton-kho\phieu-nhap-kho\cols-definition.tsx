import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getWarehouseReceiptColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 100 },
  {
    field: 'receiptNumber',
    headerName: 'Số phiếu nhập',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'date', headerName: 'Ngày c/từ', width: 100 },
  { field: 'objectCode', headerName: 'Mã đối tượng', width: 120 },
  { field: 'objectName', headerName: 'Tên đối tượng', width: 200 },
  { field: 'description', headerName: '<PERSON>ễn giả<PERSON>', width: 200 },
  { field: 'totalAmount', headerName: 'Tổng tiền', type: 'number', width: 120 },
  { field: 'currency', headerName: 'Ngoại tệ', width: 100 },
  {
    field: 'totalQuantity',
    headerName: 'Tổng số lượng',
    type: 'number',
    width: 120
  },
  { field: 'documentCode', headerName: 'Mã ct', width: 100 }
];

export const warehouseReceiptDetailColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unitOfMeasure', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  {
    field: 'averagePrice',
    headerName: 'Giá trung bình',
    type: 'number',
    width: 120
  },
  { field: 'priceInVND', headerName: 'Giá %s', type: 'number', width: 100 },
  { field: 'amountInVND', headerName: 'Tiền %s', type: 'number', width: 100 },
  { field: 'debitAccount', headerName: 'Tk nợ', width: 100 },
  { field: 'importReason', headerName: 'Lý do nhập', width: 120 },
  { field: 'creditAccount', headerName: 'Tk có', width: 100 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'price', headerName: 'Giá', width: 120 },
  { field: 'amount', headerName: 'Tiền', width: 120 },
  { field: 'quantityReceived', headerName: 'Sl đã nhập', width: 120 },
  { field: 'receiptNumber', headerName: 'Số PN t/tế', width: 120 },
  { field: 'receiptLine', headerName: 'Dòng pn', width: 120 }
];

export const warehouseReceiptItemColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unitOfMeasure', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  {
    field: 'averagePrice',
    headerName: 'Giá trung bình',
    type: 'number',
    width: 120
  },
  { field: 'priceInVND', headerName: 'Giá %s', type: 'number', width: 100 },
  { field: 'amountInVND', headerName: 'Tiền %s', type: 'number', width: 100 },
  { field: 'debitAccount', headerName: 'Tk nợ', width: 100 },
  { field: 'importReason', headerName: 'Lý do nhập', width: 120 },
  { field: 'creditAccount', headerName: 'Tk có', width: 100 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 }
];
