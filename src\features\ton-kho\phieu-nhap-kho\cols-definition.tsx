import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import React from 'react';
import {
  vatTuSearchColumns,
  warehouseSearchColumns,
  accountSearchColumns,
  boPhanSearchColumns,
  vuViecSearchColumns,
  phiSearchColumns,
  hopDongSearchColumns,
  paymentInstallmentSearchColumns,
  kheUocSearchColumns,
  invalidExpenseSearchColumns,
  lenhSanXuatSearchColumns,
  lyDoNhapXuatSearchColumns
} from '@/constants/search-columns';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';
import { QUERY_KEYS } from '@/constants/query-keys';

export const getWarehouseReceiptColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 100 },
  {
    field: 'receiptNumber',
    headerName: 'Số phiếu nhập',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'date', headerName: 'Ngày c/từ', width: 100 },
  { field: 'objectCode', headerName: 'Mã đối tượng', width: 120 },
  { field: 'objectName', headerName: 'Tên đối tượng', width: 200 },
  { field: 'description', headerName: 'Diễn giải', width: 200 },
  { field: 'totalAmount', headerName: 'Tổng tiền', type: 'number', width: 120 },
  { field: 'currency', headerName: 'Ngoại tệ', width: 100 },
  {
    field: 'totalQuantity',
    headerName: 'Tổng số lượng',
    type: 'number',
    width: 120
  },
  { field: 'documentCode', headerName: 'Mã ct', width: 100 }
];

export const warehouseReceiptDetailColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unitOfMeasure', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  {
    field: 'averagePrice',
    headerName: 'Giá trung bình',
    type: 'number',
    width: 120
  },
  { field: 'priceInVND', headerName: 'Giá %s', type: 'number', width: 100 },
  { field: 'amountInVND', headerName: 'Tiền %s', type: 'number', width: 100 },
  { field: 'debitAccount', headerName: 'Tk nợ', width: 100 },
  { field: 'importReason', headerName: 'Lý do nhập', width: 120 },
  { field: 'creditAccount', headerName: 'Tk có', width: 100 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'price', headerName: 'Giá', width: 120 },
  { field: 'amount', headerName: 'Tiền', width: 120 },
  { field: 'quantityReceived', headerName: 'Sl đã nhập', width: 120 },
  { field: 'receiptNumber', headerName: 'Số PN t/tế', width: 120 },
  { field: 'receiptLine', headerName: 'Dòng pn', width: 120 }
];

export const getWarehouseReceiptItemColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  // Mã sản phẩm - SearchField với auto-fill tên sản phẩm và đvt
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
        searchColumns={vatTuSearchColumns}
        dialogTitle='Danh mục vật tư'
        columnDisplay='ma_vt'
        displayRelatedField='ten_vt'
        value={params.row.vat_tu_data?.ma_vt || params.row.ma_vt || ''}
        relatedFieldValue={params.row.vat_tu_data?.ten_vt || params.row.ten_vt || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'vat_tu_data', row);
          // Auto-populate related fields
          onCellValueChange(params.row.uuid, 'ten_vt', row.ten_vt);
          onCellValueChange(params.row.uuid, 'dvt', row.dvt_data?.dvt || '');
        }}
      />
    )
  },
  // Tên sản phẩm - Read-only, auto-populated from ma_vt
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='ten_vt'
        type='text'
        value={params.row.vat_tu_data?.ten_vt || params.row.ten_vt || ''}
        disabled={true}
      />
    )
  },
  // Đvt - Read-only, auto-populated from ma_vt
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 80,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='dvt'
        type='text'
        value={params.row.vat_tu_data?.dvt_data?.dvt || params.row.dvt || ''}
        disabled={true}
      />
    )
  },
  // Mã kho - SearchField
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHO_HANG}`}
        searchColumns={warehouseSearchColumns}
        dialogTitle='Danh mục kho'
        columnDisplay='ma_kho'
        value={params.row.kho_data?.ma_kho || params.row.ma_kho || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'kho_data', row);
          onCellValueChange(params.row.uuid, 'ma_kho', row.ma_kho);
        }}
      />
    )
  },
  // Số lượng - Input field
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='so_luong'
        type='number'
        value={params.row.so_luong || 0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_luong', newValue)}
      />
    )
  },
  // Giá trung bình - Checkbox
  {
    field: 'pn_tb',
    headerName: 'Giá trung bình',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='pn_tb'
        type='checkbox'
        value={params.row.pn_tb || false}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'pn_tb', newValue)}
      />
    )
  },
  // Giá VND - Input field (disabled when pn_tb is true)
  {
    field: 'gia_nt',
    headerName: 'Giá VND',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='gia_nt'
        type='number'
        value={params.row.gia_nt || 0}
        disabled={params.row.pn_tb === true}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'gia_nt', newValue)}
      />
    )
  },
  // Tiền VND - Input field (disabled when pn_tb is true)
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='tien_nt'
        type='number'
        value={params.row.tien_nt || 0}
        disabled={params.row.pn_tb === true}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
      />
    )
  },
  // Tk nợ - Read-only, auto-populated
  {
    field: 'tk_vt',
    headerName: 'Tk nợ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='tk_vt' type='text' value={params.row.tk_vt || ''} disabled={true} />
    )
  },
  // Lý do nhập - SearchField
  {
    field: 'ma_nx',
    headerName: 'Lý do nhập',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LY_DO_NHAP_XUAT}`}
        searchColumns={lyDoNhapXuatSearchColumns}
        dialogTitle='Lý do nhập xuất'
        columnDisplay='ma_nx'
        value={params.row.ly_do_data?.ma_nx || params.row.ma_nx || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ly_do_data', row);
          onCellValueChange(params.row.uuid, 'ma_nx', row.ma_nx);
        }}
      />
    )
  },
  // Tk có - SearchField
  {
    field: 'tk_du',
    headerName: 'Tk có',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
        searchColumns={accountSearchColumns}
        dialogTitle='Danh mục tài khoản'
        columnDisplay='code'
        value={params.row.tk_du_data?.code || params.row.tk_du || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'tk_du_data', row);
          onCellValueChange(params.row.uuid, 'tk_du', row.code);
        }}
      />
    )
  },
  // Bộ phận - SearchField
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
        searchColumns={boPhanSearchColumns}
        dialogTitle='Danh mục bộ phận'
        columnDisplay='ma_bp'
        value={params.row.bo_phan_data?.ma_bp || params.row.ma_bp || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'bo_phan_data', row);
          onCellValueChange(params.row.uuid, 'ma_bp', row.ma_bp);
        }}
      />
    )
  },
  // Vụ việc - SearchField
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}`}
        searchColumns={vuViecSearchColumns}
        dialogTitle='Danh mục vụ việc'
        columnDisplay='ma_vv'
        value={params.row.vu_viec_data?.ma_vv || params.row.ma_vv || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'vu_viec_data', row);
          onCellValueChange(params.row.uuid, 'ma_vv', row.ma_vv);
        }}
      />
    )
  },
  // Hợp đồng - SearchField
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}`}
        searchColumns={hopDongSearchColumns}
        dialogTitle='Danh mục hợp đồng'
        columnDisplay='ma_hd'
        value={params.row.hop_dong_data?.ma_hd || params.row.ma_hd || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'hop_dong_data', row);
          onCellValueChange(params.row.uuid, 'ma_hd', row.ma_hd);
        }}
      />
    )
  },
  // Đợt thanh toán - SearchField
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 140,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}`}
        searchColumns={paymentInstallmentSearchColumns}
        dialogTitle='Danh mục đợt thanh toán'
        columnDisplay='ma_dtt'
        value={params.row.dot_thanh_toan_data?.ma_dtt || params.row.ma_dtt || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'dot_thanh_toan_data', row);
          onCellValueChange(params.row.uuid, 'ma_dtt', row.ma_dtt);
        }}
      />
    )
  },
  // Khế ước - SearchField
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}`}
        searchColumns={kheUocSearchColumns}
        dialogTitle='Danh mục khế ước'
        columnDisplay='ma_ku'
        value={params.row.khe_uoc_data?.ma_ku || params.row.ma_ku || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'khe_uoc_data', row);
          onCellValueChange(params.row.uuid, 'ma_ku', row.ma_ku);
        }}
      />
    )
  },
  // Phí - SearchField
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}`}
        searchColumns={phiSearchColumns}
        dialogTitle='Danh mục phí'
        columnDisplay='ma_phi'
        value={params.row.phi_data?.ma_phi || params.row.ma_phi || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'phi_data', row);
          onCellValueChange(params.row.uuid, 'ma_phi', row.ma_phi);
        }}
      />
    )
  },
  // Lệnh sản xuất - SearchField
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 140,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}`}
        searchColumns={lenhSanXuatSearchColumns}
        dialogTitle='Danh mục lệnh sản xuất'
        columnDisplay='so_lsx'
        value={params.row.lenh_san_xuat_data?.so_lsx || params.row.ma_lsx || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'lenh_san_xuat_data', row);
          onCellValueChange(params.row.uuid, 'ma_lsx', row.so_lsx);
        }}
      />
    )
  },
  // C/p không h/lệ - SearchField
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 140,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}`}
        searchColumns={invalidExpenseSearchColumns}
        dialogTitle='Danh mục chi phí không hợp lệ'
        columnDisplay='ma_chi_phi'
        value={params.row.chi_phi_data?.ma_chi_phi || params.row.ma_cp0 || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'chi_phi_data', row);
          onCellValueChange(params.row.uuid, 'ma_cp0', row.ma_chi_phi);
        }}
      />
    )
  },
  // Số lệnh SX - Read-only
  {
    field: 'so_ct_sx1',
    headerName: 'Số lệnh SX',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='so_ct_sx1' type='text' value={params.row.so_ct_sx1 || ''} disabled={true} />
    )
  },
  // Dòng lệnh SX - Read-only
  {
    field: 'line_sx1',
    headerName: 'Dòng lệnh SX',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='line_sx1' type='text' value={params.row.line_sx1 || ''} disabled={true} />
    )
  }
];
