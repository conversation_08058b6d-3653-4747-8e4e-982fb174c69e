import { useCallback } from 'react';
import { useNhaCungCap } from '@/hooks/queries/useNhaCungCap';
import { NhaCungCapFormData } from '../schema';

export const useNhaCungCapIntegration = () => {
  const { nhaCungCaps, isLoading, addNhaCungCap, updateNhaCungCap, deleteNhaCungCap, refreshNhaCungCaps } =
    useNhaCungCap();

  const copyNhaCungCap = useCallback(
    async (data: NhaCungCapFormData) => {
      // Create a copy with a new code
      const copyData = {
        ...data,
        customer_code: `${data.customer_code}_copy_${Date.now()}`,
        customer_name: `${data.customer_name} (Copy)`,
        description: data.description || 'Copy description'
      };
      return addNhaCungCap(copyData);
    },
    [addNhaCungCap]
  );

  return {
    nhaCungCaps,
    isLoading,
    addNhaCungCap,
    updateNhaCungCap,
    deleteNhaCungCap,
    copyNhaCungCap,
    refreshNhaCungCaps
  };
};
