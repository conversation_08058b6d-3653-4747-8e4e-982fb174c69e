import { Printer, Plus, Pencil, Trash, Copy, Search, RefreshCw } from 'lucide-react';
import { AritoActionButton, AritoMenuButton, AritoIcon } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface ActionBarProps {
  onPrintClick?: () => void;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onPrintMultipleClick?: () => void;
  onExportDataClick?: () => void;
  onDownloadExcelTemplateClick?: () => void;
  onImportFromExcelClick?: () => void;
  isEditDisabled?: boolean;
}

export const ActionBar = ({
  onPrintClick,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onPrintMultipleClick,
  onExportDataClick,
  onDownloadExcelTemplateClick,
  onImportFromExcelClick,
  isEditDisabled
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Giấy báo có</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick || (() => {})} variant='primary' />
    <AritoActionButton
      title='Sửa'
      icon={Pencil}
      onClick={onEditClick || (() => {})}
      variant='secondary'
      disabled={isEditDisabled}
    />
    <AritoActionButton title='Xoá' icon={Trash} onClick={onDeleteClick || (() => {})} variant='destructive' />
    <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick || (() => {})} variant='secondary' />
    <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick || (() => {})} variant='secondary' />

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Tìm kiếm',
          icon: <AritoIcon icon={12} />,
          onClick: onSearchClick || (() => {}),
          group: 0
        },
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick || (() => {}),
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: onFixedColumnsClick || (() => {}),
          group: 0
        },
        {
          title: 'In nhiều',
          icon: <AritoIcon icon={883} />,
          onClick: onPrintMultipleClick || (() => {}),
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: onExportDataClick || (() => {}),
          group: 1
        },
        {
          title: 'Tải mẫu Excel',
          icon: <AritoIcon icon={28} />,
          onClick: onDownloadExcelTemplateClick || (() => {}),
          group: 2
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: <AritoIcon icon={29} />,
          onClick: onImportFromExcelClick || (() => {}),
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);
