import React from 'react';
import {
  AccountTab,
  AttributeTab,
  BasicInformationTab,
  GeneralTab,
  ItemPriceTab,
  OtherTab,
  UnitConversionTab
} from './tabs';
import { vatTuSanPhamSchema, initialVatTuSanPhamValues } from '../../schema';
import useUnitConversions from './hooks/useUnitConversion';
import { priceListColumns } from '../../cols-definition';
import { AritoForm } from '@/components/custom/arito';
import useItemPriceData from './hooks/useItemPrice';

interface FormDialogProps {
  formMode: 'add' | 'edit' | 'view';
  initialData?: any;
  onSubmit: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

export const FormDialog: React.FC<FormDialogProps> = ({
  formMode,
  initialData,
  onSubmit,
  onClose,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}) => {
  const {
    rows: unitConversionRows,
    selectedRowUuid: unitConversionSelectedRowUuid,
    handleRowClick: handleUnitConversionRowClick,
    handleAddRow: handleAddUnitConversionRow,
    handleDeleteRow: handleDeleteUnitConversionRow,
    handleCopyRow: handleCopyUnitConversionRow,
    handlePasteRow: handlePasteUnitConversionRow,
    handleMoveRow: handleMoveUnitConversionRow,
    handleCellValueChange: handleUnitConversionCellValueChange
  } = useUnitConversions();

  const {
    rows: itemPriceRows,
    selectedRowUuid: itemPriceSelectedRowUuid,
    handleRowClick: handleItemPriceRowClick,
    handleAddRow: handleAddItemPriceRow,
    handleDeleteRow: handleDeleteItemPriceRow,
    handleCopyRow: handleCopyItemPriceRow,
    handlePasteRow: handlePasteItemPriceRow,
    handleMoveRow: handleMoveItemPriceRow,
    handleCellValueChange: handleItemPriceCellValueChange
  } = useItemPriceData();

  const handleSubmit = async (data: any) => {
    onSubmit(data);
  };

  return (
    <div className='h-full flex-1 lg:overflow-hidden'>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialVatTuSanPhamValues}
        onSubmit={handleSubmit}
        headerFields={<BasicInformationTab formMode={formMode} />}
        tabs={[
          {
            id: 'thong_tin_chung',
            label: 'Thông tin chung',
            component: <GeneralTab formMode={formMode} />
          },
          {
            id: 'tai_khoan',
            label: 'Tài khoản',
            component: <AccountTab formMode={formMode} />
          },
          {
            id: 'qddvt',
            label: 'Quy đổi đvt',
            component: (
              <UnitConversionTab
                mode={formMode}
                rows={unitConversionRows}
                selectedRowUuid={unitConversionSelectedRowUuid}
                onRowClick={handleUnitConversionRowClick}
                onAddRow={handleAddUnitConversionRow}
                onDeleteRow={handleDeleteUnitConversionRow}
                onCopyRow={handleCopyUnitConversionRow}
                onPasteRow={handlePasteUnitConversionRow}
                onMoveRow={handleMoveUnitConversionRow}
                onCellValueChange={handleUnitConversionCellValueChange}
              />
            )
          },
          {
            id: 'gia_ban',
            label: 'Giá bán',
            component: (
              <ItemPriceTab
                mode={formMode}
                rows={itemPriceRows}
                selectedRowUuid={itemPriceSelectedRowUuid}
                onRowClick={handleItemPriceRowClick}
                onAddRow={handleAddItemPriceRow}
                onDeleteRow={handleDeleteItemPriceRow}
                onCopyRow={handleCopyItemPriceRow}
                onPasteRow={handlePasteItemPriceRow}
                onMoveRow={handleMoveItemPriceRow}
                onCellValueChange={handleItemPriceCellValueChange}
              />
            )
          },
          {
            id: 'thuoc_tinh',
            label: 'Thuộc tính',
            component: <AttributeTab formMode={formMode} />
          },
          {
            id: 'khac',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          }
        ]}
        onClose={onClose}
        subTitle={'Vật tư sản phẩm'}
      />
    </div>
  );
};

export default FormDialog;
