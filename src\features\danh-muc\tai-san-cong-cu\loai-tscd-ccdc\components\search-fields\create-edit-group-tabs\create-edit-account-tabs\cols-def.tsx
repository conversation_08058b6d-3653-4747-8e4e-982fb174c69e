import { GridColDef } from '@mui/x-data-grid';
import AritoCheckboxCellRenderer from '@/components/custom/arito/cell-renderers/arito-checkbox-cell-renderer';
import { ExtendedGridColDef } from '@/components/custom/arito';

export const getObjectColumns = (): GridColDef[] => [
  {
    field: 'objectName',
    headerName: 'Tên đối tượng',
    width: 250
  },
  {
    field: 'requireInput',
    headerName: 'Bắt buộc nhập',
    width: 120,
    renderCell: AritoCheckboxCellRenderer
  }
];
