import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

function HeaderTab() {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-2 md:space-y-2'>
          {/* Mã bộ phận */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã bộ phận</Label>
            <FormField type='text' name='departmentCode' className='w-[400px]' />
          </div>

          {/* Tên bộ phận */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên bộ phận</Label>
            <FormField type='text' name='departmentName' className='w-[400px]' />
          </div>

          {/* Tên khác */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên khác</Label>
            <FormField type='text' name='otherName' className='w-[400px]' />
          </div>

          {/* Ghi chú */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ghi chú</Label>
            <FormField type='text' name='otherName' className='w-[400px]' />
          </div>

          {/* Trạng thái */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Trạng thái</Label>
            <FormField
              type='select'
              name='status'
              className='w-[400px]'
              options={[
                { label: '1. Còn sử dụng', value: 'active' },
                { label: '0. Không sử dụng', value: 'inactive' }
              ]}
              defaultValue='active'
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default HeaderTab;
