## Danh Sách <PERSON>c Bảng Trong Database Hiện Tại

Dưới đây là danh sách các bảng thực tế trong database:

### Bảng Hệ Thống Django

- auth_group
- auth_group_permissions
- auth_permission
- auth_user
- auth_user_groups
- auth_user_user_permissions
- authtoken_token
- django_admin_log
- django_content_type
- django_session
- oauth2_provider_accesstoken
- oauth2_provider_application
- oauth2_provider_grant
- oauth2_provider_idtoken
- oauth2_provider_refreshtoken

### Bảng Django Ledger

- django_ledger_accountconstraintmodel
- django_ledger_accountmodel
- django_ledger_bankaccountmodel
- django_ledger_billmodel
- django_ledger_chartofaccountmodel
- django_ledger_chitietdieukienthanhtoanmodel
- django_ledger_chungtu
- django_ledger_closingentrymodel
- django_ledger_closingentrytransactionmodel
- django_ledger_customermodel
- django_ledger_dieukienthanhtoanmodel
- django_ledger_entitymanagementmodel
- django_ledger_entitymodel
- django_ledger_entitystatemodel
- django_ledger_entityunitmodel
- django_ledger_estimatemodel
- django_ledger_giamuamodel
- django_ledger_importjobmodel
- django_ledger_invoicemodel
- django_ledger_itemmodel
- django_ledger_itemtransactionmodel
- django_ledger_journalentrymodel
- django_ledger_khohangmodel
- django_ledger_ledgermodel
- django_ledger_loaivattumodel
- django_ledger_lomodel
- django_ledger_mausohdmodel
- django_ledger_ngoaitemodel
- django_ledger_nhanvienmodel
- django_ledger_nhomhangmodel
- django_ledger_nhomkhachhang
- django_ledger_nhomloaihdmodel
- django_ledger_nhomphimodel
- django_ledger_phimodel
- django_ledger_purchasemodel
- django_ledger_purchaseordermodel
- django_ledger_rangbuocnhapmodel
- django_ledger_stagedtransactionmodel
- django_ledger_thuesuatthuegtgtmodel
- django_ledger_transactionmodel
- django_ledger_tygiamodel
- django_ledger_unitofmeasuremodel
- django_ledger_user_profile
- django_ledger_vattusanphamdonvitinhmodel
- django_ledger_vattusanphamhanghoamodel
- django_ledger_vendormodel
- django_ledger_vuviecmodel

### Bảng Nghiệp Vụ

- bo_phan
- bo_phan_su_dung_ccdc
- bo_phan_su_dung_ts
- chi_phi
- chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc
- chi_phi_chi_tiet_phieu_nhap_chi_phi_mua_hang
- chi_phi_hoa_don_mua_hang_trong_nuoc
- chi_phi_khong_hop_le
- chi_phi_phieu_nhap_chi_phi_mua_hang
- chi_tiet_dinh_muc_nguyen_vat_lieu
- chi_tiet_don_ban_hang
- chi_tiet_don_mua_hang
- chi_tiet_hoa_don
- chi_tiet_hoa_don_ban_hang
- chi_tiet_hoa_don_dich_vu_tra_lai_giam_gia
- chi_tiet_hoa_don_mua_hang_trong_nuoc
- chi_tiet_khe_uoc_lai_suat
- chi_tiet_khe_uoc_thanh_toan
- chi_tiet_phieu_nhap_chi_phi_mua_hang
- chi_tiet_phieu_nhap_kho
- chi_tiet_phieu_thanh_toan_tam_ung
- chi_tiet_phieu_xuat_dieu_chuyen
- chi_tiet_phieu_xuat_kho
- chi_tiet_phieu_xuat_tra_lai_nha_cung_cap
- chi_tieu_ngan_sach
- chi_tieu_ngan_sach_chi_tiet
- chung_tu
- co_quan_thue
- danh_muc_cong_doan
- dia_chi
- dia_chi_nhan_hang
- dich_vu
- dinh_muc_nguyen_vat_lieu
- dm_thue
- dmdvt
- dmloaigb
- dmnd
- dmnk
- dmnk2
- dmnk4
- dmphinganhang
- dmqddvt0
- dmvt
- dmvt_gia_ban
- dmvt_gia_ban_ct
- dmvt_qddvt
- dmvt_theo_dv
- don_ban_hang
- don_mua_hang
- dot_thanh_toan
- edmkbsdtkhddt
- edmnkhddt
- edmtkhddt
- group
- han_thanh_toan
- hinh_thuc_thanh_toan
- hoa_don_ban_hang
- hoa_don_dich_vu
- hoa_don_dich_vu_tra_lai_giam_gia
- hoa_don_dieu_chinh_thong_tin
- hoa_don_mua_hang_trong_nuoc
- hop_dong
- kenh_ban_hang
- khai_bao_ma_hang_ipos
- khe_uoc
- khu_vuc
- kich_co
- loai_tai_san_cong_cu
- loai_yeu_to
- ly_do_tang_giam_ccdc
- ly_do_tang_giam_tai_san_co_dinh
- mau_sac
- ngan_hang
- nhap_xuat
- phieu_nhap_chi_phi_mua_hang
- phieu_nhap_kho
- phieu_thanh_toan_tam_ung
- phieu_xuat_dieu_chuyen
- phieu_xuat_kho
- phieu_xuat_tra_lai_nha_cung_cap
- ptgh
- pttt
- ptvc
- quan_huyen
- quoc_gia
- quy_cach
- quy_doi_don_vi_tinh_chi_tiet
- quyet_toan_cac_lan_tam_ung
- so_du_tuc_thoi_theo_tai_khoan
- tai_khoan_cua_hang
- thong_tin_thanh_toan_hoa_don_ban_hang
- thong_tin_thanh_toan_hoa_don_dich_vu
- thue_hoa_don_mua_hang_trong_nuoc
- thue_phieu_nhap_chi_phi_mua_hang
- thue_phieu_thanh_toan_tam_ung
- tien_do_thanh_toan
- tinh_chat_thue
- tinh_thanh
- vi_tri
- vi_tri_kho_hang
- xa_phuong

## Ghi Chú

- Bảng trong database được chia thành 3 nhóm chính:
  - Bảng hệ thống Django: Quản lý người dùng, quyền, phiên làm việc, v.v.
  - Bảng Django Ledger: Các bảng có tiền tố `django_ledger_` quản lý các chức năng kế toán cốt lõi
  - Bảng nghiệp vụ: Các bảng không có tiền tố, quản lý các chức năng nghiệp vụ cụ thể
- Danh sách trên dựa trên kết quả thực tế từ database hiện tại
- Để cập nhật danh sách này khi database thay đổi, hãy chạy lại script `get_database_tables.py`
