import React from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import HoaDonMuaHangNhapKhauBasicInfoTab from './BasicInfoTab';
import AritoModal from '@/components/custom/arito/modal';
import HoaDonMuaHangNhapKhauDetailTab from './DetailTab';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';

interface Props {
  showPopup: boolean;
  setShowPopup: (value: boolean) => void;
  formMode: 'add' | 'edit' | 'view';
}

export const HoaDonMuaHangNhapKhauPopup = ({ showPopup, setShowPopup, formMode }: Props) => {
  const handleSubmit = (data: any) => {
    setShowPopup(false);
  };
  return (
    <AritoModal
      open={showPopup}
      onClose={() => {
        setShowPopup(false);
      }}
      title={'Mới'}
      titleIcon={<AritoIcon icon={699} />}
      maxWidth='md'
    >
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
          <AritoForm<any>
            mode={formMode}
            hasAritoActionBar={false}
            className='!static !w-full'
            onSubmit={handleSubmit}
            onClose={() => {
              setShowPopup(false);
            }}
            headerFields={<HoaDonMuaHangNhapKhauBasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'chi_tiet',
                label: 'Chi tiết',
                component: <HoaDonMuaHangNhapKhauDetailTab formMode={formMode} />
              }
            ]}
          />
        </div>

        <BottomBar
          mode={formMode}
          onSubmit={() => {}}
          onClose={() => {
            setShowPopup(false);
          }}
        />
      </div>
    </AritoModal>
  );
};
