import { useState, useEffect } from 'react';
import { ViTriKho, ViTriKhoInput, ViTriKhoResponse } from '@/types/schemas/vi-tri-kho.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseWarehouseLocationReturn {
  warehouseLocations: ViTriKho[];
  isLoading: boolean;
  addWarehouseLocation: (newLocation: ViTriKhoInput) => Promise<void>;
  updateWarehouseLocation: (uuid: string, updatedLocation: ViTriKhoInput) => Promise<void>;
  deleteWarehouseLocation: (uuid: string) => Promise<void>;
  refreshWarehouseLocations: () => Promise<void>;
  getWarehouseLocationsByWarehouse: (warehouseId: string) => Promise<ViTriKho[]>;
}

/**
 * Hook for managing warehouse locations
 *
 * This hook provides functions to fetch, create, update, and delete warehouse locations.
 */
export const useWarehouseLocation = (initialLocations: ViTriKho[] = []): UseWarehouseLocationReturn => {
  const [warehouseLocations, setWarehouseLocations] = useState<ViTriKho[]>(initialLocations);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  /**
   * Fetch all warehouse locations
   */
  const fetchWarehouseLocations = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<ViTriKhoResponse>(`/entities/${entity.slug}/erp/warehouse-locations/`);
      setWarehouseLocations(response.data.results || []);
    } catch (error) {
      console.error('Error fetching warehouse locations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get warehouse locations by warehouse ID
   */
  const getWarehouseLocationsByWarehouse = async (warehouseId: string): Promise<ViTriKho[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<ViTriKhoResponse>(
        `/entities/${entity.slug}/erp/warehouse-locations/?ma_kho=${warehouseId}`
      );
      return response.data.results || [];
    } catch (error) {
      console.error('Error fetching warehouse locations by warehouse:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Add a new warehouse location
   */
  const addWarehouseLocation = async (newLocation: ViTriKhoInput): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.post(`/entities/${entity.slug}/erp/warehouse-locations/`, {
        ma_kho: newLocation.ma_kho,
        ma_vi_tri: newLocation.ma_vi_tri,
        ten_vi_tri: newLocation.ten_vi_tri,
        ten_vi_tri2: newLocation.ten_vi_tri2 || null,
        ghi_chu: newLocation.ghi_chu || null,
        status: newLocation.status || '1'
      });

      const addedLocation: ViTriKho = response.data;
      setWarehouseLocations(prev => [...prev, addedLocation]);
    } catch (error) {
      console.error('Error adding warehouse location:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update an existing warehouse location
   */
  const updateWarehouseLocation = async (uuid: string, updatedLocation: ViTriKhoInput): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.put(`/entities/${entity.slug}/erp/warehouse-locations/${uuid}/`, {
        ma_kho: updatedLocation.ma_kho,
        ma_vi_tri: updatedLocation.ma_vi_tri,
        ten_vi_tri: updatedLocation.ten_vi_tri,
        ten_vi_tri2: updatedLocation.ten_vi_tri2 || null,
        ghi_chu: updatedLocation.ghi_chu || null,
        status: updatedLocation.status || '1'
      });

      const updatedLocationData: ViTriKho = response.data;
      setWarehouseLocations(prev =>
        prev.map(location => (location.uuid === updatedLocationData.uuid ? updatedLocationData : location))
      );
    } catch (error) {
      console.error('Error updating warehouse location:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete a warehouse location
   */
  const deleteWarehouseLocation = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/warehouse-locations/${uuid}/`);
      setWarehouseLocations(prev => prev.filter(location => location.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting warehouse location:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Refresh warehouse locations
   */
  const refreshWarehouseLocations = async (): Promise<void> => {
    await fetchWarehouseLocations();
  };

  // Fetch warehouse locations when entity changes
  useEffect(() => {
    fetchWarehouseLocations();
  }, [entity?.slug, fetchWarehouseLocations]);

  return {
    warehouseLocations,
    isLoading,
    addWarehouseLocation,
    updateWarehouseLocation,
    deleteWarehouseLocation,
    refreshWarehouseLocations,
    getWarehouseLocationsByWarehouse
  };
};
