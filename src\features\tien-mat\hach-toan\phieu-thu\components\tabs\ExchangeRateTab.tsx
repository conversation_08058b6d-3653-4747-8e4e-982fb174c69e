import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { FormMode } from '@/types/form';

interface ExchangeRateTabProps {
  formMode: FormMode;
  editExchangeRate: boolean;
  onEditExchangeRateChange: (value: boolean) => void;
}

export const ExchangeRateTab: React.FC<ExchangeRateTabProps> = ({
  formMode,
  editExchangeRate,
  onEditExchangeRateChange
}) => {
  const disabled = formMode === 'view';

  return (
    <div className='flex items-center gap-12 p-4'>
      <label htmlFor='edit-exchange-rate' className='text-sm font-medium'>
        Sửa tỷ giá ghi sổ
      </label>
      <Checkbox
        id='edit-exchange-rate'
        checked={editExchangeRate}
        onCheckedChange={checked => onEditExchangeRateChange(checked as boolean)}
        disabled={disabled}
        className='h-3.5 w-3.5 border-gray-300 data-[state=checked]:bg-[#666]'
      />
    </div>
  );
};
