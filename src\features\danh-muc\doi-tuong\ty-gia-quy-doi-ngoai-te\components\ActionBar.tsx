import { <PERSON><PERSON>, <PERSON>Search, <PERSON>cil, Plus, Refresh<PERSON><PERSON>, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddIconClick: () => void;
  onEditIconClick: () => void;
  onDeleteIconClick: () => void;
  onCopyIconClick: () => void;
  onWatchIconClick: () => void;
  onRefreshClick?: () => void;
  hasRowSelected?: boolean;
}

const ActionBar: React.FC<ActionBarProps> = ({
  onAddIconClick,
  onEditIconClick,
  onDeleteIconClick,
  onCopyIconClick,
  onWatchIconClick,
  onRefreshClick,
  hasRowSelected = false
}) => {
  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Tỷ giá quy đổi ngoại tệ</h1>
          <div className='mt-1 flex items-center'>
            <AritoColoredDot color='#FF0000' size={6} className='mr-2' />
            <span className='text-xs text-gray-600'>Cập nhật tỷ giá</span>
          </div>
        </div>
      }
    >
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddIconClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditIconClick} disabled={!hasRowSelected} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteIconClick} disabled={!hasRowSelected} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyIconClick} disabled={!hasRowSelected} />
      <AritoActionButton title='Xem' icon={FileSearch} onClick={onWatchIconClick} disabled={!hasRowSelected} />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={18} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
