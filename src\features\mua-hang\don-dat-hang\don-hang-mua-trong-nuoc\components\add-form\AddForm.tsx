import React from 'react';
import { AritoForm } from '@/components/custom/arito';
import DeliveryInfoTab from './DeliveryInfoTab';
import { BasicInfoTab } from './BasicInfoTab';
import { BottomBar } from '../BottomBar';
import { DetailTab } from './DetailTab';
import OtherTab from './OtherTab';

const AddForm = ({
  open,
  onClose,
  formMode,
  initialData,
  handleFormSubmit,
  handleCloseForm,
  showTaxCodePopupForm,
  setShowTaxCodePopupForm
}: {
  open: boolean;
  onClose: (show: boolean) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData: any;
  handleFormSubmit: (data: any) => void;
  handleCloseForm: () => void;
  showTaxCodePopupForm: boolean;
  setShowTaxCodePopupForm: (show: boolean) => void;
}) => {
  return (
    <div className='flex size-full flex-col overflow-hidden'>
      <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
        <AritoForm<any>
          title={formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Xem'}
          subTitle='Đơn hàng mua trong nước'
          mode={formMode}
          initialData={initialData || {}}
          className='!static !w-full'
          onSubmit={handleFormSubmit}
          onClose={handleCloseForm}
          headerFields={
            <BasicInfoTab
              formMode={formMode}
              showTaxCodePopupForm={showTaxCodePopupForm}
              setShowTaxCodePopupForm={setShowTaxCodePopupForm}
            />
          }
          tabs={[
            {
              id: 'chi-tiet',
              label: 'Chi tiết',
              component: <DetailTab formMode={formMode} value={initialData?.details || []} onChange={newValue => {}} />
            },
            {
              id: 'thong-tin-giao-hang',
              label: 'Thông tin giao hàng',
              component: <DeliveryInfoTab formMode={formMode} />
            },
            {
              id: 'khac',
              label: 'Khác',
              component: <OtherTab formMode={formMode} onFileChange={file => {}} />
            }
          ]}
        />
      </div>

      <div className='fixed bottom-0 left-0 right-0 bg-white'>
        <BottomBar
          totalQuantity={0}
          totalExpense={0}
          totalTax={0}
          totalAmount={0}
          totalPayment={0}
          formMode={formMode}
        />
      </div>
    </div>
  );
};

export default AddForm;
