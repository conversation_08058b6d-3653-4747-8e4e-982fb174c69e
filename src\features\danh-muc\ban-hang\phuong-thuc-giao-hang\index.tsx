'use client';

import React from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, ConfirmDialog } from './components';
import { useFormState, useData } from './hooks';
import { Columns } from './cols-definition';

export default function PhuongThucGiaoHang({ initialRows }: { initialRows: any[] }) {
  const {
    showForm,
    showDelete,
    formMode,
    handleCloseForm,
    handleCloseDelete,
    handleCloseCopy,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const {
    rows,
    selectedRowIndex,
    showData,
    handleRowClick,
    handleFormSubmit,
    handleCopySubmit,
    handleDeleteConfirm,
    handleRefreshClick,
    handleFixedColumnsClick
  } = useData(initialRows);

  const tables = [
    {
      name: '',
      rows: rows,
      columns: Columns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          onConfirm={handleDeleteConfirm}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn muốn xoá không?'
        />
      )}
      {showForm && (
        <FormDialog
          open={showForm}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
        />
      )}

      {showData && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteClick}
            onCopyClick={handleCopyClick}
            onViewClick={handleViewClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onRefreshClick={handleRefreshClick}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
