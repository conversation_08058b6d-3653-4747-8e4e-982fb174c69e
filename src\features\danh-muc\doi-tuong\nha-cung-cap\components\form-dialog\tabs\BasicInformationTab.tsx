import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const BasicInformationTab = ({ formMode }: Props) => (
  <div className='p-4'>
    <div className='grid gap-x-4 gap-y-4 lg:grid-cols-1'>
      {/* Mã nhà cung cấp + combobox + 2 checkbox */}
      <div className='flex gap-x-2'>
        <FormField
          label='Mã nhà cung cấp'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='customer_code'
          type='text'
          disabled={formMode === 'view' || formMode === 'edit'}
        />
        <FormField
          className='flex items-center gap-x-1'
          labelClassName='w-[160px]'
          name='customer_type'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { label: 'Cá nhân', value: '1' },
            { label: '<PERSON><PERSON>h nghiệp', value: '2' }
          ]}
        />
        <FormField
          label='Khách hàng'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[100px]'
          name='is_customer'
          type='checkbox'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Nhà cung cấp'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[100px]'
          name='is_vendor'
          type='checkbox'
          disabled={formMode === 'view'}
        />
      </div>

      <FormField
        label='Tên nhà cung cấp'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='customer_name'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Tên khác'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='alternative_name'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Địa chỉ'
        className='flex items-center gap-x-1'
        labelClassName='min-w-[160px]'
        name='address'
        type='text'
        disabled={formMode === 'view'}
      />

      {/* Mã số thuế + Người liên hệ */}
      <div className='flex gap-x-2'>
        <FormField
          label='Mã số thuế'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='tax_code'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Người liên hệ'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[120px]'
          name='contact_person'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
