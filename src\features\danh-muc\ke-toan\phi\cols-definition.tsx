import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const FeeColumns: GridColDef[] = [
  { field: 'ma_phi', headerName: 'Mã phí', width: 150 },
  { field: 'ten_phi', headerName: 'Tên phí', width: 250 },
  {
    field: 'nhom_phi_1',
    headerName: 'Nhóm phí 1',
    width: 150,
    renderCell: (params: any) => params.row.nhom_phi_1?.ten_phan_nhom || ''
  },
  {
    field: 'nhom_phi_2',
    headerName: 'Nhóm phí 2',
    width: 150,
    renderCell: (params: any) => params.row.nhom_phi_2?.ten_phan_nhom || ''
  },
  {
    field: 'nhom_phi_3',
    headerName: 'Nhóm phí 3',
    width: 150,
    renderCell: (params: any) => params.row.nhom_phi_3?.ten_phan_nhom || ''
  },
  { field: 'bo_phan', headerName: 'Mã bộ phận', width: 150 },
  {
    field: 'trang_thai',
    headerName: 'Trạng thái',
    width: 150,
    renderCell: (params: any) => (params.row.trang_thai === 1 ? 'Hoạt động' : 'Không hoạt động')
  },
  { field: 'created', headerName: 'Ngày tạo', width: 200 }
];

export const feeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_phi', headerName: 'Mã phí', flex: 1 },
  { field: 'ten_phi', headerName: 'Tên phí', flex: 2 }
];

export const feeGroupSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', flex: 1 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', flex: 2 }
];

export const departmentSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', flex: 2 }
];
