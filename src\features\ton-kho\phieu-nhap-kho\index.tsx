'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import { getWarehouseReceiptColumns, warehouseReceiptDetailColumns } from './cols-definition';
import { WarehouseReceiptActionBar } from './components/WarehouseReceiptActionBar';
import { WarehouseReceiptItemsTab } from './components/WarehouseReceiptItemTab';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import { BasicInfoTab } from './components/BasicInfoTab';
import { BottomBar } from './components/BottomBar';
import { warehouseReceiptSchema } from './schemas';

// Mock data for initial state
const initialReceiptRows: any[] = [
  {
    id: 1,
    status: 'Chờ duyệt',
    receiptNumber: 'PN001',
    date: '2023-07-15',
    objectCode: 'NCC001',
    objectName: 'Công ty ABC',
    departmentCode: 'IT',
    description: 'Phiếu nhập kho tháng 7/2023',
    totalAmount: ********,
    currency: 'VND',
    totalQuantity: 10,
    documentCode: 'CT001',
    createdBy: 'admin',
    createdAt: '2023-07-15',
    updatedBy: 'admin',
    updatedAt: '2023-07-15',
    receiptDetails: [
      {
        id: 'DET001',
        productCode: 'SP001',
        productName: 'Laptop Dell XPS 13',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 2,
        averagePrice: ********,
        priceInVND: ********,
        amountInVND: ********,
        debitAccount: '156',
        importReason: 'Mua hàng',
        creditAccount: '331',
        department: 'IT',
        product: 'SP001',
        price: ********,
        amount: ********,
        quantityReceived: 2,
        receiptNumber: 'PN001',
        receiptLine: '1'
      },
      {
        id: 'DET002',
        productCode: 'SP002',
        productName: 'Màn hình Dell 27"',
        unit: 'Cái',
        warehouseCode: 'KHO01',
        quantity: 3,
        averagePrice: 5000000,
        priceInVND: 5000000,
        amountInVND: ********,
        debitAccount: '156',
        importReason: 'Mua hàng',
        creditAccount: '331',
        department: 'IT',
        product: 'SP002',
        price: 5000000,
        amount: ********,
        quantityReceived: 3,
        receiptNumber: 'PN001',
        receiptLine: '2'
      }
    ]
  }
];

export function WarehouseReceipt() {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [receiptRows, setReceiptRows] = useState<any[]>(initialReceiptRows);
  //Add and Edit
  const [inputReceiptDetail, setInputReceiptDetail] = useState<any[]>([]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.receiptDetails) {
      setInputReceiptDetail([...obj.receiptDetails]);
    } else {
      setInputReceiptDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.receiptDetails) {
      setInputReceiptDetail([...obj.receiptDetails]);
    } else {
      setInputReceiptDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputReceiptDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    // ===== TẠO REQUEST BODY THEO MẪU CHUẨN =====
    const requestBody = {
      // ===== THÔNG TIN CƠ BẢN =====
      action: 'NHAP_KHO',
      question_ids: data.question_ids || '',
      ma_ngv: '3', // Fixed value for warehouse receipt
      ma_gd: data.ma_gd || '', // Giao dịch
      ma_kh: data.ma_kh_data?.uuid || data.ma_kh || '', // Mã đối tượng (uuid)
      dien_giai: data.dien_giai || '', // Diễn giải

      // ===== THÔNG TIN ĐƠN VỊ VÀ TIẾN ĐỘ =====
      unit_id: data.unit_id || null, // id đơn vị
      id_progress: null, // backend auto generate
      xprogress: '', // backend auto generate

      // ===== THÔNG TIN CHỨNG TỪ =====
      i_so_ct: '', // backend xử lý
      ma_nk: data.ma_nk_data?.uuid || data.ma_nk || '', // mã quyển chứng từ (uuid)
      so_ct: '', // backend auto generate
      ngay_ct: data.ngay_ct || '', // Ngày chứng từ
      ngay_lct: data.ngay_ct || '', // Ngày lập chứng từ = ngày chứng từ
      xdatetime2: '', // backend auto generate

      // ===== THÔNG TIN TIỀN TỆ =====
      ma_nt: data.ma_nt || '', // Mã ngoại tệ (uuid)
      ty_gia: Number(data.ty_gia) || 1.0, // Tỷ giá

      // ===== TRẠNG THÁI =====
      status: data.status === 'create' ? '1' : data.status === 'pending' ? '2' : data.status === 'approved' ? '3' : '1', // Trạng thái
      transfer_yn: Boolean(data.transfer_yn) || false, // Chuyển dữ liệu

      // ===== THÔNG TIN SỐ LƯỢNG VÀ TIỀN =====
      t_so_luong: inputReceiptDetail.reduce((sum, item) => sum + (Number(item.so_luong) || 0), 0),
      t_tien_nt: inputReceiptDetail.reduce((sum, item) => sum + (Number(item.tien_nt) || 0), 0),
      t_tien: inputReceiptDetail.reduce((sum, item) => sum + (Number(item.tien_nt) || 0), 0),

      // ===== THÔNG TIN KHÁC =====
      xfile: data.xfile || '',

      // ===== CHI TIẾT PHIẾU NHẬP KHO =====
      chi_tiet: inputReceiptDetail.map((item, index) => ({
        line: index + 1,
        ma_vt: item.vat_tu_data?.uuid || item.ma_vt || '',
        dvt: item.vat_tu_data?.dvt_data?.uuid || item.dvt || '',
        ten_dvt: item.vat_tu_data?.dvt_data?.dvt || item.ten_dvt || '',
        ma_kho: item.kho_data?.uuid || item.ma_kho || '',
        ten_kho: item.kho_data?.ten_kho || item.ten_kho || '',
        ma_lo: item.ma_lo || '',
        ten_lo: item.ten_lo || '',
        lo_yn: item.lo_yn ? 1 : 0,
        ma_vi_tri: item.ma_vi_tri || '',
        ten_vi_tri: item.ten_vi_tri || '',
        vi_tri_yn: item.vi_tri_yn ? 1 : 0,
        he_so: Number(item.he_so) || 1.0,
        qc_yn: item.qc_yn ? 1 : 0,
        so_luong: Number(item.so_luong) || 0.0,
        pn_tb: item.pn_tb ? 1 : 0,
        gia_nt: Number(item.gia_nt) || 0.0,
        tien_nt: Number(item.tien_nt) || 0.0,
        gia: Number(item.gia_nt) || 0.0,
        tien: Number(item.tien_nt) || 0.0,
        tk_vt: item.tk_vt || '',
        ma_nx: item.ly_do_data?.uuid || item.ma_nx || '',
        tk_du: item.tk_du_data?.uuid || item.tk_du || '',
        ma_bp: item.bo_phan_data?.uuid || item.ma_bp || '',
        ma_vv: item.vu_viec_data?.uuid || item.ma_vv || '',
        ma_hd: item.hop_dong_data?.uuid || item.ma_hd || '',
        ma_dtt: item.dot_thanh_toan_data?.uuid || item.ma_dtt || '',
        ma_ku: item.khe_uoc_data?.uuid || item.ma_ku || '',
        ma_phi: item.phi_data?.uuid || item.ma_phi || '',
        ma_sp: item.ma_sp || '',
        ma_lsx: item.lenh_san_xuat_data?.so_lsx || item.ma_lsx || '',
        ma_cp0: item.chi_phi_data?.uuid || item.ma_cp0 || '',
        sl_pn: Number(item.so_luong) || 0.0,
        id_pn: item.id_pn || null,
        line_pn: item.line_pn || null,
        id_sx1: item.id_sx1 || null,
        line_sx1: item.line_sx1 || null
      }))
    };

    // Log request body for debugging
    console.log('Request Body:', requestBody);

    try {
      if (inputReceiptDetail.length === 0) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi nhập liệu',
          html: `<p class="text-[15px]">Bạn chưa nhập chi tiết phiếu nhập</p>`
        });
        return;
      }

      // Validate receipt detail
      for (const row of inputReceiptDetail) {
        warehouseReceiptSchema.parse(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...receiptRows.map(row => row.id || 0)) + 1;
        const newReceipt = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          receiptDetails: inputReceiptDetail
        };

        setReceiptRows([...receiptRows, newReceipt]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = receiptRows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              receiptDetails: inputReceiptDetail
            };
          }
          return row;
        });

        setReceiptRows(updatedRows);
      }

      setShowForm(false);
      setInputReceiptDetail([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const handleRowClick = (params: GridRowParams) => {
    const receipt = params.row as any;
    setSelectedObj(receipt);
    setInputReceiptDetail(receipt.receiptDetails);
  };

  interface FilterValue {
    name: string;
    value: string;
    color: string;
  }

  const filterValues: FilterValue[] = [
    { name: 'Lập chứng từ', value: 'Lập chứng từ', color: 'pink' },
    { name: 'Chờ duyệt', value: 'Chờ duyệt', color: '#EF4444' },
    { name: 'Nhập kho', value: 'Nhập kho', color: '#3B82F6' }
  ];

  const tables = [
    {
      name: 'Tất cả',
      rows: receiptRows,
      columns: getWarehouseReceiptColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = receiptRows.filter(row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: getWarehouseReceiptColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: receiptRows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: getWarehouseReceiptColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  const from = (
    <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Thực tế'
      >
        Thực tế
      </button>
    </div>
  );

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'lineItems',
                label: 'Chi tiết',
                component: (
                  <WarehouseReceiptItemsTab
                    value={inputReceiptDetail}
                    onChange={setInputReceiptDetail}
                    formMode={formMode}
                  />
                )
              },
              {
                id: 'filekem',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Phiếu nhập kho'}
            from={from}
            bottomBar={<BottomBar totalQuantity={0} totalAmount={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <WarehouseReceiptActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.receiptDetails || []}
                columns={warehouseReceiptDetailColumns}
                mode={formMode}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
