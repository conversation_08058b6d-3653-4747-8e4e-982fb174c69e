import * as yup from 'yup';

// Define the form schema for validation
export const specialConsumptionTaxRateSchema = yup
  .object({
    taxCode: yup.string().required('Bạn chưa nhập mã thuế'),
    taxName: yup.string().required('Bạn chưa nhập tên thuế'),
    nameAlias: yup.string().required('Bạn chưa nhập tên khác'),
    taxRate: yup.number().required('Bạn chưa nhập thuế suất').min(0, '<PERSON>hu<PERSON> suất không được âm'),
    outputTaxAccount: yup.string().required('Bạn chưa nhập TK thuế'),
    status: yup.string().required('Bạn chưa nhập trạng thái')
  })
  .required();
