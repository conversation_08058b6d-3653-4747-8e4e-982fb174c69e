'use client';

import { ActionBar, DeleteDialog, DepartmentDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useDepartment } from '@/hooks/queries/useDepartment';
import { useDialogState, useRowSelection } from './hooks';
import { getDataTableColumns } from './cols-definition';

export default function DanhMucBoPhanKeToan() {
  const { departments, isLoading, addDepartment, updateDepartment, deleteDepartment, refreshDepartments } =
    useDepartment();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  } = useDialogState(clearSelection);

  const tables = [
    {
      name: 'T<PERSON>t cả',
      rows: departments,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <DepartmentDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedObj && showEditDialog
              ? {
                  ma_bp: selectedObj.ma_bp,
                  ten_bp: selectedObj.ten_bp,
                  ten_bp2: selectedObj.ten_bp2,
                  ghi_chu: selectedObj.ghi_chu,
                  status: selectedObj.status
                }
              : selectedObj && showAddDialog && isCopyMode
                ? {
                    ma_bp: selectedObj.ma_bp,
                    ten_bp: selectedObj.ten_bp,
                    ten_bp2: selectedObj.ten_bp2,
                    ghi_chu: selectedObj.ghi_chu,
                    status: selectedObj.status
                  }
                : undefined
          }
          onClose={showEditDialog ? closeEditDialog : closeAddDialog}
          selectedObj={showEditDialog ? selectedObj : null}
          addDepartment={addDepartment}
          updateDepartment={updateDepartment}
        />
      )}

      {showDeleteDialog && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          selectedObj={selectedObj}
          deleteDepartment={deleteDepartment}
          clearSelection={clearSelection}
        />
      )}

      {showWatchDialog && selectedObj && (
        <DepartmentDialog
          open={showWatchDialog}
          mode='view'
          initialData={{
            ma_bp: selectedObj.ma_bp,
            ten_bp: selectedObj.ten_bp,
            ten_bp2: selectedObj.ten_bp2,
            ghi_chu: selectedObj.ghi_chu,
            status: selectedObj.status
          }}
          onClose={closeWatchDialog}
          selectedObj={selectedObj}
          addDepartment={addDepartment}
          updateDepartment={updateDepartment}
          onAddButtonClick={handleAddButtonClick}
          onEditButtonClick={handleEditButtonClick}
          onDeleteButtonClick={handleDeleteButtonClick}
          onCopyButtonClick={handleCopyButtonClick}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddIconClick={openAddDialog}
          onEditIconClick={() => selectedObj && openEditDialog()}
          onDeleteIconClick={() => selectedObj && openDeleteDialog()}
          onCopyIconClick={() => selectedObj && handleCopyButtonClick()}
          onWatchIconClick={() => selectedObj && openWatchDialog()}
          onRefreshClick={refreshDepartments}
        />

        {isLoading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
          </div>
        )}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>
    </div>
  );
}
