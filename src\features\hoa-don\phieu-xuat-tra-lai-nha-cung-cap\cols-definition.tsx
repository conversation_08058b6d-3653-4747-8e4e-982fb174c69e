import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getSupplierReturnReceiptColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 120 },
  { field: 'invoiceStatus', headerName: 'Trạng thái HĐĐT', width: 150 },
  {
    field: 'receiptNumber',
    headerName: 'Số c/từ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'receiptDate', headerName: 'Ngày c/từ', width: 120, type: 'date' },
  { field: 'customerCode', headerName: 'Mã khách hàng', width: 150 },
  { field: 'customerName', headerName: 'Tên khách hàng', width: 200 },
  { field: 'description', headerName: 'Diễn giải', width: 200 },
  { field: 'account', headerName: 'Tài khoản', width: 120 },
  { field: 'totalAmount', headerName: 'Tổng tiền', width: 120, type: 'number' },
  { field: 'foreignCurrency', headerName: 'Ngoại tệ', width: 100 }
];

export const supplierReturnReceiptDetailColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unit', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 120 },
  { field: 'lotCode', headerName: 'Mã lô', width: 120 },
  { field: 'quantity', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'price', headerName: 'Giá', width: 120, type: 'number' },
  { field: 'amount', headerName: 'Tiền', width: 120, type: 'number' },
  { field: 'tax', headerName: 'Thuế', width: 120, type: 'number' },
  { field: 'creditAccount', headerName: 'Tk có', width: 120 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'task', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 150 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 150 },
  { field: 'fee', headerName: 'Phí', width: 120, type: 'number' },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'productionOrder', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'invalidExpense', headerName: 'C/p không h/lệ', width: 150 },
  { field: 'purchaseInvoiceDomestic', headerName: 'Số hóa đơn mua trong nước', width: 180 },
  { field: 'purchaseInvoiceImport', headerName: 'Số hóa đơn mua nhập khẩu', width: 180 },
  { field: 'invoiceLine', headerName: 'Dòng HĐ', width: 100 },
  { field: 'receiptNumber', headerName: 'Số phiếu nhập', width: 150 },
  { field: 'receiptLine', headerName: 'Dòng', width: 80 }
];

export const supplierReturnReceiptItemColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unit', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 120 },
  { field: 'lotCode', headerName: 'Mã lô', width: 120 },
  { field: 'quantity', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'priceVND', headerName: 'Giá VND', width: 120, type: 'number' },
  { field: 'amountVND', headerName: 'Tiền VND', width: 120, type: 'number' },
  { field: 'taxVND', headerName: 'Thuế VND', width: 120, type: 'number' },
  { field: 'creditAccount', headerName: 'Tk có', width: 120 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'task', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 150 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 150 },
  { field: 'fee', headerName: 'Phí', width: 120, type: 'number' },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'productionOrder', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'invalidExpense', headerName: 'C/p không h/lệ', width: 150 },
  { field: 'purchaseInvoiceDomestic', headerName: 'Số hóa đơn mua trong nước', width: 180 },
  { field: 'purchaseInvoiceImport', headerName: 'Số hóa đơn mua nhập khẩu', width: 180 },
  { field: 'invoiceLine', headerName: 'Dòng HĐ', width: 100 }
];
