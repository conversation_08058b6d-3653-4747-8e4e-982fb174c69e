import { z } from 'zod';

export const FormSchema = z.object({
  loai_tscc: z.string().nonempty('Loại tài sản/công cụ là bắt buộc'),
  ma_lts: z.string().nonempty('Mã loại là bắt buộc'),
  ten_lts: z.string().nonempty('Tên loại là bắt buộc'),
  ten_lts2: z.string().optional().nullable(),
  status: z.string().nonempty('Trạng thái là bắt buộc')
});
export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  loai_tscc: '1',
  ma_lts: '',
  ten_lts: '',
  ten_lts2: '',
  status: '1'
};
