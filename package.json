{"name": "ttmi-erp", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "build": "cross-env NODE_OPTIONS='--max-old-space-size=6144' next build", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "lint": "next lint", "format": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json,css}\"", "type-check": "tsc --noEmit", "prepare": "husky install && chmod +x .husky/pre-commit .husky/commit-msg", "commitlint": "commitlint --edit"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^6.4.3", "@mui/material": "^6.4.6", "@mui/x-data-grid": "^7.26.0", "@mui/x-date-pickers": "^7.27.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.20.6", "allotment": "^1.20.3", "antd": "^5.24.2", "await-to-js": "^3.0.0", "axios": "^1.7.9", "axios-mock-adapter": "^2.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "exceljs": "^4.4.0", "framer-motion": "^12.4.7", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next": "13.5.8", "next-intl": "^3.26.3", "next-themes": "^0.4.4", "node-fetch": "^2.7.0", "node-fetch-cookies": "^2.1.1", "nookies": "^2.5.2", "re-resizable": "^6.11.2", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.7", "react-split": "^2.0.14", "recharts": "^2.15.1", "shadcn-time-range-picker": "^1.2.1", "sonner": "^1.7.4", "sweetalert2": "^11.17.2", "sweetalert2-react-content": "^5.1.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tough-cookie": "^5.1.1", "undici": "^7.3.0", "vaul": "^1.1.2", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "autoprefixer": "^10", "eslint": "^8.57.1", "eslint-config-next": "13.5.8", "husky": "^8.0.0", "lint-staged": "^15.5.1", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-sort-imports": "^1.8.6", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,css,md,html}": ["prettier --write"], "src/app/**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"]}}