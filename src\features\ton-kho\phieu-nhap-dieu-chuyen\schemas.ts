import * as yup from 'yup';

export const transferReceiptSchema = yup.object().shape({
  // requestNumber: yup.string().required("<PERSON>ui lòng nhập số phiếu"),
  // date: yup.string().required("<PERSON>ui lòng nhập ngày yêu cầu"),
  // department: yup.string().required("Vui lòng chọn bộ phận"),
  // warehouseCode: yup.string().required("Vui lòng chọn kho"),
});

export const transferReceiptItemSchema = yup.object().shape({
  // productCode: yup.string().required("Vui lòng nhập mã sản phẩm"),
  // quantity: yup.number().required("Vui lòng nhập số lượng").min(1),
  // warehouseCode: yup.string().required("Vui lòng chọn kho"),
  // expectedDate: yup.string().required("<PERSON>ui lòng nhập ngày cần"),
});
