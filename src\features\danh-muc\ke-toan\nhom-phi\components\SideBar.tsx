import { ChevronLeft, ChevronRight } from 'lucide-react';
import SidebarButton from '@/components/custom/arito/sidebar-button';
import { AritoIcon } from '@/components/custom/arito';
import { GroupType } from '@/types/schemas';

interface SidebarProps {
  activeGroup: GroupType | null;
  onFilterChange: (groupType: GroupType | null) => void;
  groupTypes: { label: string; value: GroupType }[];
  isOpen: boolean;
  toggleSidebar: () => void;
}

export const Sidebar = ({ activeGroup, onFilterChange, groupTypes, isOpen, toggleSidebar }: SidebarProps) => {
  if (!isOpen) return null;
  return (
    <div className='relative flex h-full flex-col border-r bg-white'>
      <div className='flex-1 overflow-y-auto p-4'>
        <div className='mb-4 flex items-center gap-x-2'>
          <AritoIcon icon={538} />
          <h2 className='text-lg font-semibold'><PERSON>ại nhóm</h2>
        </div>
        <hr className='mb-4' />
        <ul>
          {groupTypes.map(group => (
            <li key={group.value} className='mb-2 text-sm'>
              <SidebarButton isActive={activeGroup === group.value} onClick={() => onFilterChange(group.value)}>
                {group.label}
              </SidebarButton>
            </li>
          ))}
        </ul>
      </div>
      <div className={`absolute right-0 top-4 z-10 translate-x-1/2 transform`}>
        <button
          onClick={toggleSidebar}
          className='flex h-6 w-6 items-center justify-center rounded-full border border-gray-300 bg-gray-200 shadow-sm hover:bg-gray-100'
        >
          <ChevronLeft className='h-4 w-4' />
        </button>
      </div>
    </div>
  );
};
