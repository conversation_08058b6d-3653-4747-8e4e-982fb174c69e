import { <PERSON><PERSON>, <PERSON>cil, Plus, Trash, FileSearch, RefreshCw } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onViewClick?: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  isEditDisabled = true,
  isViewDisabled = true,
  onRefreshClick,
  onFixedColumnsClick,
  onExportClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục phí</h1>}>
      {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
      {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />}
      {onDeleteClick && (
        <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />
      )}
      {onCopyClick && (
        <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />
      )}
      {onViewClick && (
        <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} disabled={isViewDisabled} />
      )}

      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: onFixedColumnsClick,
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExportClick,
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};
