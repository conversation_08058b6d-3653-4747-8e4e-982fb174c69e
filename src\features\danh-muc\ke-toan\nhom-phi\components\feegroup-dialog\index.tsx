import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { FeeGroupFormattedData, SearchFormValues, searchSchema } from '../../schemas';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog, AritoForm } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { Group, GroupType } from '@/types/schemas';
import ConfirmDialog from './ConfirmDialog';
import FeeGroupForm from './FeeGroupForm';
import { FormMode } from '@/types/form';

interface FeeGroupDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: SearchFormValues;
  onClose: () => void;
  onSubmit?: (data: FeeGroupFormattedData) => void;
  selectedObj?: Group | null;
  updateFeeGroup?: (uuid: string, data: FeeGroupFormattedData) => Promise<Group | undefined>;
  addFeeGroup?: (data: FeeGroupFormattedData) => Promise<Group | undefined>;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  activeGroupType?: string | null;
}

const FeeGroupDialog = ({
  open,
  mode,
  initialData,
  onClose,
  onSubmit,
  selectedObj,
  updateFeeGroup,
  addFeeGroup,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick,
  activeGroupType
}: FeeGroupDialogProps) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const handleSubmit = async (data: SearchFormValues) => {
    const formattedData: FeeGroupFormattedData = {
      ma_nhom: data.ma_nhom,
      ten_phan_nhom: data.ten_phan_nhom,
      ten2: data.ten2 || null,
      trang_thai: data.trang_thai.toString(),
      loai_nhom: (activeGroupType as any) || GroupType.FEE1
    };

    try {
      if (mode === 'edit' && selectedObj && updateFeeGroup) {
        await updateFeeGroup(selectedObj.uuid, formattedData);
      } else if (mode === 'add' && addFeeGroup) {
        await addFeeGroup(formattedData);
      }

      if (onSubmit) {
        onSubmit(formattedData);
      }

      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const title = mode === 'add' ? 'Thêm nhóm phí' : mode === 'edit' ? 'Sửa nhóm phí' : 'Xem nhóm phí';

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={searchSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<FeeGroupForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}

              {mode !== 'view' && (
                <div className='flex justify-end gap-2 p-4'>
                  <Button variant='outlined' onClick={onClose}>
                    Hủy
                  </Button>
                  <Button variant='contained' color='primary' type='submit'>
                    Lưu
                  </Button>
                </div>
              )}
            </>
          }
        />
      </AritoDialog>

      <ConfirmDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={() => {
          setShowConfirmDialog(false);
          onClose();
        }}
      />
    </>
  );
};

export default FeeGroupDialog;
