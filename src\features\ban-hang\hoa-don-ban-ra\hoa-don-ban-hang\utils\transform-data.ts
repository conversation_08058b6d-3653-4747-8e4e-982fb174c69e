import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    ma_vt: row.ma_vt_data?.uuid || '',
    dvt: row.ma_vt_data?.dvt || '',
    ma_kho: row.ma_kho_data?.uuid || '',
    ct_km: row.ct_km || '0',
    so_luong: row.so_luong,
    gia_nt2: row.gia_nt2,
    tien_nt2: row.tien_nt2,
    px_dd: row.px_dd,
    gia_nt: row.gia_nt,
    tien_nt: row.tien_nt,
    ma_thue: row.ma_thue || '',
    thue_nt: row.thue_nt,
    thanh_tien: row.thanh_tien,
    don_gia: row.don_gia,
    giam_gia: row.giam_gia,
    thue_suat: row.thue_suat,
    tk_thue_co: row.tk_thue_co_data?.uuid || '',
    tk_dt: row.tk_dt_data?.uuid || '',
    tk_gv: row.tk_gv_data?.uuid || '',
    tk_vt: row.tk_vt_data?.uuid || '',
    tk_ck: row.tk_ck_data?.uuid || '',
    ghi_chu: row.ghi_chu || '',
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_lsx: row.ma_lsx_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null,
    sl_px: row.sl_px || null,
    line_dh: row.line_dh || null,
    line_hd: row.line_hd || null
  }));
};

/**
 * Transform account rows for API submission
 * @param accountRows - Array of account row data from the form
 * @returns Transformed account rows ready for API submission
 */
export const transformAccountRows = (accountRows: any[]) => {
  return accountRows.map((row: any, index: number) => ({
    line: index + 1,
    ma_httt: row.ma_httt_data?.uuid || '',
    ten_httt: row.ma_httt_data?.ten_httt || '',
    tknh: row.tknh || '',
    tk: row.tk_data?.uuid || '',
    ma_ct: row.ma_ct_data?.uuid || '',
    ngay_ct: row.ngay_ct || '',
    ma_nk: row.ma_nk_data?.uuid || '',
    t_tt_nt: row.t_tt_nt || 0,
    id_ct_tt: row.ma_ct_data?.uuid || ''
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param accountRows - Array of account row data
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (data: any, state: FormFieldState, ...rest: any[]) => {
  // Transform detail and account rows
  const detail = transformDetailRows(rest[0]);
  const account = transformAccountRows(rest[1]);

  // Build the final form data object
  return {
    ...data,
    // Form state checkboxes
    hdbh_yn: state.hdbh_yn,
    px_yn: state.px_yn,
    pt_tao_yn: state.pt_tao_yn,
    ck_yn: state.ck_yn,
    loai_ck: state.loai_ck,

    // Payment method from state
    ma_httt: state.ma_httt || 'TMB',

    // Customer information
    ma_kh: state.khachHang?.uuid || '',
    ma_so_thue: data.ma_so_thue || state.khachHang?.tax_code || '',
    ten_kh_thue: data.ten_kh_thue || state.khachHang?.customer_name || '',
    dia_chi: data.dia_chi || state.khachHang?.address || '',
    ong_ba: data.ong_ba || state.khachHang?.contact_person || '',
    e_mail: data.e_mail || state.khachHang?.email || '',
    dien_giai: data.dien_giai || state.khachHang?.description || '',

    // References
    ma_nvbh: state.nhanVien?.uuid || state.khachHang?.sales_rep_data?.uuid || '',
    tk: state.taiKhoan?.uuid || state.khachHang?.account_data?.uuid || '',
    ma_tt: state.hanThanhToan?.uuid || state.khachHang?.payment_term_data?.uuid || '',
    so_ct: state.chungTu?.uuid || '',
    ma_dc: state.diaChi?.uuid || '',
    ma_ptvc: state.phuongTienVanChuyen?.uuid || '',
    ma_ptgh: state.phuongTienGiaoHang?.uuid || '',
    ma_kh9: state.cucThue?.uuid || '',

    // Document information
    ma_ngv: '1',
    i_so_ct: '3',
    i_so_px: '3',
    so_px: state.chungTu?.uuid || '',
    so_ct2: state.chungTu?.uuid || '',
    ngay_ct: data.ngay_ct || '',
    ngay_lct: data.ngay_ct || '',

    // E-invoice information
    so_ct_hddt0: data.so_ct_hddt0 || '',
    ngay_ct_hddt0: data.ngay_ct_hddt0 || '',
    so_ct2_hddt0: data.so_ct2_hddt0 || '',

    // Totals
    t_tien_nt2: data.t_tien_nt2 || 0,
    t_tien2: data.t_tien_nt2 || 0,
    t_thue_nt: data.t_thue_nt || 0,
    t_thue: data.t_thue_nt || 0,
    t_ck_nt: data.t_ck_nt || 0,
    t_ck: data.t_ck_nt || 0,
    t_tt_nt: data.t_tt_nt || 0,
    t_tt: data.t_tt_nt || 0,

    // Detail arrays
    chi_tiet: detail,
    thong_tin_thanh_toan: account
  };
};
