import * as yup from 'yup';

// Define the form schema for validation
export const orderSchema = yup
  .object({
    // orderItems: yup.array().of(
    //   yup.object({
    //     productCode: yup.string().required('Bạn chưa chọn sản phẩm'),
    //     quantity: yup
    //       .number()
    //       .required('Bạn chưa nhập số lượng')
    //       .min(1, 'Số lượng phải lớn hơn 0'),
    //     unitPrice: yup
    //       .number()
    //       .required('Bạn chưa nhập đơn giá')
    //       .min(0, 'Đơn giá không được âm'),
    //     unit: yup.string().required('Bạn chưa chọn đơn vị tính'),
    //     amount: yup.number().min(0, 'Thành tiền không được âm'),
    //     note: yup.string().nullable()
    //   })
    // )
    // .min(1, 'Đơn hàng phải có ít nhất một sản phẩm')
    // .required('Bạn chưa thêm sản phẩm vào đơn hàng'),
  })
  .required();

export const orderDetailSchema = yup
  .object({
    // productCode: yup.string().required("Bạn chưa nhập mã vật tư"),
    // Add other validation rules
  })
  .required();

export const expenseSchema = yup
  .object({
    // expenseCode: yup.string().required("Bạn chưa nhập mã chi phí"),
    // Add other validation rules
  })
  .required();
