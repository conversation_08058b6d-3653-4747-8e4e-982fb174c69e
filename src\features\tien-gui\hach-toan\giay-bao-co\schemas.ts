import { z } from 'zod';

export const exportCreditAdviceSchema = z.object({
  // Basic info fields from BasicInfoTab (excluding SearchField fields)
  docType: z.string().optional(),
  // address: SearchField - removed from schema
  // payer: SearchField - removed from schema
  dien_giai: z.string().optional(), // FormField
  date: z.string().optional(), // <PERSON><PERSON><PERSON> lập chứng từ
  foreignCurrency: z.string().optional(),
  exchangeRate: z.number().optional(),
  status: z.string().optional(),
  dataReceived: z.boolean().optional(),

  // Additional fields from mock data (FormField only)
  so_ct: z.string().optional(), // FormField
  ngay_ct: z.string().optional(), // FormField
  ma_kh: z.string().optional(),
  ten_kh: z.string().optional(),
  tknh: z.string().optional(),
  sten_tknh: z.string().optional(),
  tk: z.string().optional(),
  t_tien_nt: z.number().optional(),
  ma_nt: z.string().optional(),
  ma_ngv: z.string().optional(),
  unit_id: z.number().optional(),
  ma_unit: z.string().optional()
  // chung_tu_goc: SearchField - removed from schema
  // ten_ngan_hang: SearchField - removed from schema
});

export const exportCreditAdviceDetailSchema = z.object({
  // Empty object schema for now, add fields as needed
});

// Bank Fee Item Schema
const bankFeeSchema = z.object({
  uuid: z.string().optional(),
  id: z.string().optional(),
  customerCode: z.string(),
  customerName: z.string(),
  feeCode: z.string(),
  bankFeeName: z.string(),
  feeVND: z.number(),
  invoiceNumber: z.string(),
  symbol: z.string(),
  invoiceDate: z.string(),
  description: z.string(),
  feeAccount: z.string(),
  counterAccount: z.string(),
  taxAccount: z.string(),
  taxRate: z.number(),
  taxAmountVND: z.number(),
  department: z.string(),
  project: z.string(),
  contract: z.string(),
  paymentPhase: z.string(),
  agreement: z.string(),
  feeNote: z.string(),
  product: z.string(),
  productionOrder: z.string(),
  invalidDocument: z.string(),
  created: z.string(),
  updated: z.string()
});

// Export the BankFeeItem type
export type BankFeeItem = z.infer<typeof bankFeeSchema>;

// Detail Item Schema
const detailItemSchema = z.object({
  uuid: z.string().optional(),
  id: z.string().optional(),
  description: z.string(),
  targetCode: z.string(),
  targetName: z.string(),
  debtBalance: z.number(),
  invoice: z.string(),
  invoiceNumber: z.string(),
  invoiceDate: z.string(),
  creditAccount: z.string(),
  currency: z.string(),
  invoiceExchangeRate: z.number(),
  invoiceAmount: z.number(),
  allocatedAmount: z.number(),
  remainingAmount: z.number(),
  amountInVND: z.number(),
  department: z.string(),
  project: z.string(),
  contract: z.string(),
  paymentPhase: z.string(),
  agreement: z.string(),
  fee: z.string(),
  product: z.string(),
  productionOrder: z.string(),
  invalidDocument: z.string(),
  created: z.string(),
  updated: z.string()
});

// Export the DetailItem type
export type DetailItem = z.infer<typeof detailItemSchema>;

// Search dialog schema
export const searchSchema = z.object({
  // Basic info fields
  from_date: z.string().optional(),
  to_date: z.string().optional(),
  from_number: z.string().optional(),
  to_number: z.string().optional(),
  loai_xuat: z.string().default('Tất cả'),

  // Detail fields
  tai_khoan_no: z.string().optional(),
  dien_giai: z.string().optional(),
  ma_khach_hang: z.string().optional(),
  tai_khoan_co: z.string().optional(),
  don_vi: z.string().optional(),
  trang_thai: z.string().default('Tất cả'),
  loc_theo_nguoi_sd: z.string().default('Tất cả')
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialSearchValues: SearchFormValues = {
  from_date: '2025-05-01',
  to_date: '2025-05-31',
  from_number: '',
  to_number: '',
  loai_xuat: 'Tất cả',
  tai_khoan_no: '',
  dien_giai: '',
  ma_khach_hang: '',
  tai_khoan_co: '',
  don_vi: '',
  trang_thai: 'Tất cả',
  loc_theo_nguoi_sd: 'Tất cả'
};
