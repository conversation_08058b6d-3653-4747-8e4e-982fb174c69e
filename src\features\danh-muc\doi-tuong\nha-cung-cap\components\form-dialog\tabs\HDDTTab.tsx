import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const HDDTTab = ({ formMode }: Props) => {
  const isViewMode = formMode === 'view';

  return (
    <div className='p-4'>
      <div className='grid gap-x-4 gap-y-4 lg:grid-cols-1'>
        <FormField
          label='Sử dụng HĐĐT'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='use_einvoice'
          type='select'
          disabled={isViewMode}
          options={[
            { label: 'Có', value: 'Yes' },
            { label: 'Không', value: 'No' }
          ]}
        />
        <FormField
          label='Email HĐĐT'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='einvoice_email'
          type='text'
          disabled={isViewMode}
        />
        <FormField
          label='Người đại diện'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='representative'
          type='text'
          disabled={isViewMode}
        />
      </div>
    </div>
  );
};
