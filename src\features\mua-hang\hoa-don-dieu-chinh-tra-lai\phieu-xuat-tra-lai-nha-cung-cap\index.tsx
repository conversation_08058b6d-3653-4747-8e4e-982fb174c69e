'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoColoredDot from '@/components/custom/arito/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import AritoModal from '@/components/custom/arito/modal';
import { AritoForm } from '@/components/custom/arito';

import { set } from 'lodash';
import {
  ActionBar,
  BasicInformationTab,
  EInvoiceTab,
  ItemsTab,
  OtherTab,
  TaxTab,
  TaxCodePopup,
  PhieuXuatTraLaiNhaCungCapFrom,
  SearchPopup,
  HoaDonMuaHangNhapKhauPopup,
  HoaDonMuaHangTrongNuocPopup
} from './components';
import {
  getSupplierReturnReceiptColumns,
  supplierReturnReceiptDetailColumns,
  supplierReturnReceiptItemColumns
} from './cols-definition';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { PageBottomBar } from './components/BottomBar';
import { AritoIcon } from '@/components/custom/arito';

export default function PhieuXuatTraLaiNhaCungCapPage({ initialRows }: { initialRows: any[] }) {
  const [showSearchForm, setShowSearchForm] = useState(false);
  const [showTaxCodeForm, setShowTaxCodeForm] = useState(false);
  const [showPrintForm, setShowPrintForm] = useState(false);
  const [showHoaDonMuaHangNhapKhauForm, setshowHoaDonMuaHangNhapKhauForm] = useState(false);
  const [showHoaDonMuaHangTrongNuocForm, setshowHoaDonMuaHangTrongNuocForm] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  const [returnOrderRows, setReturnOrderRows] = useState<any[]>(initialRows);
  const [inputReturnOrderDetails, setInputReturnOrderDetails] = useState<any[]>([]);
  const [inputTaxDetail, setInputTaxDetail] = useState<any[]>([]);

  const handleCloseForm = () => setShowForm(false);

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.returnOrderDetails) {
      setInputReturnOrderDetails([...obj.returnOrderDetails]);
    } else {
      setInputReturnOrderDetails([]);
    }
    if (obj.expenses) {
      setInputTaxDetail([...obj.expenses]);
    } else {
      setInputTaxDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.returnOrderDetails) {
      setInputReturnOrderDetails([...obj.returnOrderDetails]);
    } else {
      setInputReturnOrderDetails([]);
    }
    if (obj.expenses) {
      setInputTaxDetail([...obj.expenses]);
    } else {
      setInputTaxDetail([]);
    }
    setShowForm(true);
  };
  const handleOpenSearchForm = () => {
    setShowSearchForm(true);
  };
  const handleCloseSearchForm = () => {
    setShowSearchForm(false);
  };
  const handleOpenHoaDonMuaHangNhapKhauForm = () => {
    setshowHoaDonMuaHangNhapKhauForm(true);
  };
  const handleCloseHoaDonMuaHangNhapKhauForm = () => {
    setshowHoaDonMuaHangNhapKhauForm(false);
  };
  const handleOpenHoaDonMuaHangTrongNuocForm = () => {
    setshowHoaDonMuaHangTrongNuocForm(true);
  };
  const handleCloseHoaDonMuaHangTrongNuocForm = () => {
    setshowHoaDonMuaHangTrongNuocForm(false);
  };
  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputReturnOrderDetails([]);
    setInputTaxDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      for (const row of inputReturnOrderDetails) {
        // await returnOrderDetailSchema.validate(row);
      }

      for (const row of inputTaxDetail) {
        // await expenseSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...returnOrderRows.map(row => row.id || 0)) + 1;
        const newReturnOrder = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          statusHDDT: 'Chưa xuất',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          returnOrderDetails: inputReturnOrderDetails
        };

        setReturnOrderRows([...returnOrderRows, newReturnOrder]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = returnOrderRows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              returnOrderDetails: inputReturnOrderDetails
            };
          }
          return row;
        });

        setReturnOrderRows(updatedRows);
      }

      setShowForm(false);
      setInputReturnOrderDetails([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const convertToDetails = (order: any) => {
    return order.returnOrderItems.map((item: any) => ({
      ...item,
      productCode: item.productCode,
      productName: item.productName,
      unit: item.unit,
      warehouseCode: item.warehouseCode
    }));
  };

  const handleRowClick = (params: GridRowParams) => {
    let order = params.row as any;
    if (order.returnOrderItems) {
      const details = convertToDetails(order);
      order.returnOrderDetails = details;
    }

    console.log(order);

    setSelectedObj(order);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: returnOrderRows,
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Chưa ghi sổ',
      rows: returnOrderRows.filter(row => row.status === 'Lập chứng từ'),
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: returnOrderRows.filter(row => row.status === 'Chờ duyệt'),
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Đã ghi sổ',
      rows: returnOrderRows.filter(row => row.statusHDDT === 'Đã duyệt'),
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#3B82F6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Khác',
      rows: returnOrderRows,
      columns: getSupplierReturnReceiptColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#000000' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showTaxCodeForm && (
        <TaxCodePopup showTaxCodePopup={showTaxCodeForm} setShowTaxCodePopup={setShowTaxCodeForm} formMode={'add'} />
      )}
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={
              <BasicInformationTab
                formMode={formMode}
                setShowTaxCodePopupForm={setShowTaxCodeForm}
                showTaxCodePopupForm={showTaxCodeForm}
              />
            }
            tabs={[
              {
                id: 'items',
                label: 'Chi tiết',
                component: <ItemsTab formMode={formMode} columns={supplierReturnReceiptItemColumns} />
              },
              {
                id: 'tax',
                label: 'Thuế',
                component: <TaxTab formMode={formMode} />
              },
              {
                id: 'express',
                label: 'HĐĐT (Không sử dụng)',
                component: <EInvoiceTab formMode={formMode} />
              },
              {
                id: 'more',
                label: 'Khác',
                component: <OtherTab formMode={formMode} value={[]} onChange={() => {}} onFileChange={() => {}} />
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Phiếu xuất trả lại nhà cung cấp'}
            from={PhieuXuatTraLaiNhaCungCapFrom({
              setshowHoaDonMuaHangNhapKhauForm,
              setshowHoaDonMuaHangTrongNuocForm
            })}
            bottomBar={
              <PageBottomBar totalQuantity={0} totalTax={0} totalAmount={0} totalPayment={0} formMode={formMode} />
            }
          />
        </div>
      )}

      {showHoaDonMuaHangNhapKhauForm && (
        <HoaDonMuaHangNhapKhauPopup
          showPopup={showHoaDonMuaHangNhapKhauForm}
          setShowPopup={setshowHoaDonMuaHangNhapKhauForm}
          formMode={'add'}
        />
      )}

      {showHoaDonMuaHangTrongNuocForm && (
        <HoaDonMuaHangTrongNuocPopup
          showPopup={showHoaDonMuaHangTrongNuocForm}
          setShowPopup={setshowHoaDonMuaHangTrongNuocForm}
          formMode={'add'}
        />
      )}

      {showSearchForm && <SearchPopup showPopup={showSearchForm} setShowPopup={setShowSearchForm} formMode={'add'} />}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
            onDeleteClick={() => {}}
            onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
            onShowSearchForm={handleOpenSearchForm}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.returnOrderDetails || []}
                columns={supplierReturnReceiptDetailColumns}
                mode={formMode}
                tableActionButtons={['export', 'pin']}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
