import React from 'react';
import { departmentColumns, groupColumns } from './create-edit-group-fee-tabs/cols-def';
import { CreateEditDepartmentTab } from './create-edit-department-tabs';
import { FormField } from '@/components/custom/arito/form/form-field';
import { CreateEditGroupFeeTab } from './create-edit-group-fee-tabs';
import { Label } from '@/components/ui/label';
import SearchField from '../..';

interface HeaderTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const HeaderTab: React.FC<HeaderTabProps> = ({ formMode }) => {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-2 md:space-y-2'>
          {/* Mã phí */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã phí</Label>
            <FormField type='text' name='feeCode' className='w-[400px]' />
          </div>

          {/* Tên phí */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên phí</Label>
            <FormField type='text' name='feeName' className='w-[400px]' />
          </div>

          {/* Tên khác */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên khác</Label>
            <FormField type='text' name='otherName' className='w-[400px]' />
          </div>

          {/* Nhóm phí 1 */}
          <SearchField
            name='account'
            label='Nhóm phí 1'
            formMode={formMode}
            searchColumns={groupColumns}
            defaultSearchColumn='name'
            actionButtons={['add', 'edit']}
            headerFields={<CreateEditGroupFeeTab />}
          />
          {/* Nhóm phí 2 */}
          <SearchField
            name='account'
            label='Nhóm phí 2'
            formMode={formMode}
            searchColumns={groupColumns}
            defaultSearchColumn='name'
            actionButtons={['add', 'edit']}
            headerFields={<CreateEditGroupFeeTab />}
          />
          {/* Nhóm phí 3 */}
          <SearchField
            name='account'
            label='Nhóm phí 3'
            formMode={formMode}
            searchColumns={groupColumns}
            defaultSearchColumn='name'
            actionButtons={['add', 'edit']}
            headerFields={<CreateEditGroupFeeTab />}
          />

          {/* Bộ phận */}
          <SearchField
            name='account'
            label='Bộ phận'
            formMode={formMode}
            searchColumns={departmentColumns}
            defaultSearchColumn='name'
            actionButtons={['add', 'edit']}
            headerFields={<CreateEditDepartmentTab />}
          />

          {/* Trạng thái*/}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Trạng thái</Label>
            <FormField
              type='select'
              name='status'
              className='w-[400px]'
              options={[
                { label: '1. Còn sử dụng', value: 'active' },
                { label: '0. Không sử dụng', value: 'inactive' }
              ]}
              defaultValue='active'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderTab;
