'use client';

import { format } from 'date-fns';
import { useTyGiaQuyDoiNgoaiTe } from '@/hooks/queries/useTyGiaQuyDoiNgoaiTe';
import { ActionBar, DeleteDialog, ExchangeRateDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useDialogState, useRowSelection } from './hooks';
import { getDataTableColumns } from './cols-definition';

export default function DanhMucTyGiaQuyDoiNgoaiTe() {
  const { exchangeRates, isLoading, addExchangeRate, updateExchangeRate, deleteExchangeRate, refreshExchangeRates } =
    useTyGiaQuyDoiNgoaiTe();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  } = useDialogState(clearSelection);

  const handleEditClick = () => {
    if (selectedObj) {
      openEditDialog();
    }
  };

  const handleWatchClick = () => {
    if (selectedObj) {
      openWatchDialog();
    }
  };

  const exchangeRatesArray = Array.isArray(exchangeRates) ? exchangeRates : [];

  const mappedExchangeRates = exchangeRatesArray.map(rate => ({
    ...rate,
    id: rate.uuid,
    nt_code: rate.ma_nt_data?.ma_nt || rate.ma_nt,
    nt_name: rate.ma_nt_data?.ten_nt || rate.ten_nt
  }));

  const tables = [
    {
      name: 'Tất cả',
      rows: mappedExchangeRates,
      columns: getDataTableColumns()
    }
  ];

  const parseDate = (dateString?: string) => {
    if (!dateString) return new Date();
    try {
      return new Date(dateString);
    } catch (error) {
      return new Date();
    }
  };

  console.log(selectedObj);
  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <ExchangeRateDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedObj && showEditDialog
              ? {
                  ngay_hl: parseDate(selectedObj.ngay_hl),
                  ma_nt: selectedObj.ma_nt_data?.uuid || selectedObj.ma_nt,
                  ty_gia: Number(selectedObj.ty_gia)
                }
              : selectedObj && showAddDialog && isCopyMode
                ? {
                    ngay_hl: parseDate(selectedObj.ngay_hl),
                    ma_nt: selectedObj.ma_nt_data?.uuid || selectedObj.ma_nt,
                    ty_gia: Number(selectedObj.ty_gia)
                  }
                : undefined
          }
          onClose={showEditDialog ? closeEditDialog : closeAddDialog}
          selectedObj={showEditDialog || (showAddDialog && isCopyMode) ? selectedObj : null}
          addExchangeRate={addExchangeRate}
          updateExchangeRate={updateExchangeRate}
        />
      )}

      {showDeleteDialog && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          selectedObj={selectedObj}
          deleteExchangeRate={deleteExchangeRate}
          clearSelection={clearSelection}
        />
      )}

      {showWatchDialog && selectedObj && (
        <ExchangeRateDialog
          open={showWatchDialog}
          mode='view'
          initialData={{
            ngay_hl: parseDate(selectedObj.ngay_hl),
            ma_nt: selectedObj.ma_nt_data?.uuid || selectedObj.ma_nt,
            ty_gia: Number(selectedObj.ty_gia)
          }}
          onClose={closeWatchDialog}
          selectedObj={selectedObj}
          addExchangeRate={addExchangeRate}
          updateExchangeRate={updateExchangeRate}
          onAddButtonClick={handleAddButtonClick}
          onEditButtonClick={handleEditButtonClick}
          onDeleteButtonClick={handleDeleteButtonClick}
          onCopyButtonClick={handleCopyButtonClick}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddIconClick={openAddDialog}
          onEditIconClick={() => selectedObj && handleEditClick()}
          onDeleteIconClick={() => selectedObj && openDeleteDialog()}
          onCopyIconClick={() => selectedObj && handleCopyButtonClick()}
          onWatchIconClick={() => selectedObj && handleWatchClick()}
          onRefreshClick={refreshExchangeRates}
          hasRowSelected={!!selectedObj}
        />

        {isLoading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
          </div>
        )}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>
    </div>
  );
}
