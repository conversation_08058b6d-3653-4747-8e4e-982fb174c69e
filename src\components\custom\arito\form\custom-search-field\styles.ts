import { SxProps, Theme } from '@mui/material';

// Search icon button styles
export const searchIconButtonStyle: SxProps<Theme> = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'text.secondary',
  cursor: 'pointer',
  position: 'absolute',
  right: 4,
  padding: '4px',
  '&:hover': {
    color: '#2563EB'
  }
};

// Confirm button styles
export const confirmButtonStyle: SxProps<Theme> = {
  backgroundColor: '#2563eb',
  color: 'white',
  '&:hover': {
    backgroundColor: '#1d4ed8'
  }
};

// Cancel button styles
export const cancelButtonStyle: SxProps<Theme> = {
  color: '#6b7280',
  '&:hover': {
    backgroundColor: '#f3f4f6'
  }
};

// Search input styles
export const searchInputFieldStyle: SxProps<Theme> = {
  width: '300px',
  ml: 1,
  '& .MuiOutlinedInput-input': {
    padding: '4px 8px 4px 12px',
    fontSize: '0.8rem',
    height: '22px'
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '0px'
  }
};

// Text field input styles
export const textFieldInputStyle = (type: string): SxProps<Theme> => ({
  '& .MuiInput-root': {
    fontSize: '14px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    '&:before': {
      borderBottom: '1px solid #e5e7eb'
    },
    '&:hover:not(.Mui-disabled):before': {
      borderBottom: '1px solid #2563EB'
    },
    '&.Mui-focused:after': {
      borderBottom: '1px solid #2563EB'
    }
  },
  '& .MuiInput-input': {
    padding: '4px 8px',
    paddingRight: '28px', // Add space for the search icon
    textAlign: { xs: 'left', sm: 'left' }, // Left align text on mobile
    height: '24px'
  },
  marginTop: type === 'number' ? '8px' : '0px'
});
