'use client';

import { useDialogState, useRowSelection, useLyDoTangGiamCCDC } from './hooks';
import { getToolEquipmentChangeReasonColumns } from './cols-definition';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, DeleteDialog, FormDialog } from './components';

export default function LyDoTangGiamCCDC() {
  const {
    toolEquipmentChangeReasons,
    isLoading,
    addToolEquipmentChangeReason,
    updateToolEquipmentChangeReason,
    deleteToolEquipmentChangeReason
  } = useLyDoTangGiamCCDC();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection, setSelectedObj, setSelectedRowIndex } =
    useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  } = useDialogState(clearSelection);

  const mappedToolEquipmentChangeReasons = toolEquipmentChangeReasons.map(reason => ({
    ...reason,
    id: reason.uuid
  }));

  const handleCodeClick = (uuid: string) => {
    const row = toolEquipmentChangeReasons.find(r => r.uuid === uuid);
    if (row) {
      const mappedRow = { ...row, id: row.uuid };
      clearSelection();
      setTimeout(() => {
        setSelectedObj(mappedRow);
        setSelectedRowIndex(typeof mappedRow.id === 'string' ? (mappedRow.id as unknown as number) : mappedRow.id);
        openWatchDialog();
      }, 0);
    }
  };

  const tables = [
    {
      name: '',
      rows: mappedToolEquipmentChangeReasons,
      columns: getToolEquipmentChangeReasonColumns(handleCodeClick)
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <FormDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedObj && showEditDialog
              ? {
                  loai_tg_cc: selectedObj.loai_tg_cc,
                  ma_tg_cc: selectedObj.ma_tg_cc,
                  ten_tg_cc: selectedObj.ten_tg_cc,
                  ten_tg_cc2: selectedObj.ten_tg_cc2 || '',
                  status: selectedObj.status || '1',
                  action: selectedObj.action || '',
                  param: selectedObj.param || null
                }
              : selectedObj && showAddDialog && isCopyMode
                ? {
                    loai_tg_cc: selectedObj.loai_tg_cc,
                    ma_tg_cc: selectedObj.ma_tg_cc,
                    ten_tg_cc: selectedObj.ten_tg_cc,
                    ten_tg_cc2: selectedObj.ten_tg_cc2 || '',
                    status: selectedObj.status || '1',
                    action: selectedObj.action || '',
                    param: selectedObj.param || null
                  }
                : undefined
          }
          onClose={showEditDialog ? closeEditDialog : closeAddDialog}
          selectedObj={selectedObj}
          addToolEquipmentChangeReason={addToolEquipmentChangeReason}
          updateToolEquipmentChangeReason={updateToolEquipmentChangeReason}
        />
      )}

      {showDeleteDialog && selectedObj && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          selectedObj={selectedObj}
          deleteToolEquipmentChangeReason={deleteToolEquipmentChangeReason}
          clearSelection={clearSelection}
        />
      )}

      {showWatchDialog && selectedObj && (
        <FormDialog
          open={showWatchDialog}
          mode='view'
          initialData={{
            loai_tg_cc: selectedObj.loai_tg_cc,
            ma_tg_cc: selectedObj.ma_tg_cc,
            ten_tg_cc: selectedObj.ten_tg_cc,
            ten_tg_cc2: selectedObj.ten_tg_cc2 || '',
            status: selectedObj.status || '1',
            action: selectedObj.action || '',
            param: selectedObj.param || null
          }}
          onClose={closeWatchDialog}
          selectedObj={selectedObj}
          addToolEquipmentChangeReason={addToolEquipmentChangeReason}
          updateToolEquipmentChangeReason={updateToolEquipmentChangeReason}
          onAddButtonClick={handleAddButtonClick}
          onEditButtonClick={handleEditButtonClick}
          onDeleteButtonClick={handleDeleteButtonClick}
          onCopyButtonClick={handleCopyButtonClick}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddIconClick={openAddDialog}
          onEditIconClick={() => selectedObj && openEditDialog()}
          onDeleteIconClick={() => selectedObj && openDeleteDialog()}
          onCopyIconClick={() => selectedObj && handleCopyButtonClick()}
          onWatchIconClick={() => selectedObj && openWatchDialog()}
        />

        {isLoading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
          </div>
        )}

        {!isLoading && (
          <AritoDataTables
            tables={tables}
            selectedRowId={selectedRowIndex !== null ? String(selectedRowIndex) : undefined}
            onRowClick={handleRowClick}
          />
        )}
      </div>
    </div>
  );
}
