import { Button } from '@mui/material';
import { format } from 'date-fns';
import React from 'react';
import { TyGiaQuyDoiNgoaiTe } from '@/types/schemas/ty-gia-quy-doi-ngoai-te.type';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: TyGiaQuyDoiNgoaiTe | null;
  deleteExchangeRate: (uuid: string) => Promise<void>;
  clearSelection: () => void;
}

function DeleteDialog({ onClose, open, selectedObj, deleteExchangeRate, clearSelection }: DeleteDialogProps) {
  const handleDelete = async () => {
    if (selectedObj && selectedObj.uuid) {
      try {
        await deleteExchangeRate(selectedObj.uuid);
        clearSelection();
        onClose();
      } catch (error) {
        console.error('Error deleting exchange rate:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <AritoDialog open={open} onClose={onClose} title='Xác nhận xóa' maxWidth='sm' titleIcon={<AritoIcon icon={12} />}>
      <div className='p-4'>
        <p className='mb-4'>
          Bạn có chắc chắn muốn xóa tỷ giá ngoại tệ <strong>{selectedObj?.ma_nt_data?.ma_nt}</strong> ngày{' '}
          <strong>{selectedObj?.ngay_hl ? formatDate(selectedObj.ngay_hl) : ''}</strong>?
        </p>
        <div className='flex justify-end gap-2'>
          <Button variant='outlined' onClick={onClose}>
            Hủy
          </Button>
          <Button variant='contained' color='error' onClick={handleDelete}>
            Xóa
          </Button>
        </div>
      </div>
    </AritoDialog>
  );
}

export default DeleteDialog;
