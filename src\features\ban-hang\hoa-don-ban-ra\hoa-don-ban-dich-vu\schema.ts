import { z } from 'zod';

// Schema for FormDialog - matches only actual form fields
export const formSchema = z.object({
  // BasicInfoTab - Customer Information (text fields)
  ma_so_thue: z.string().optional(),
  ong_ba: z.string().optional(),
  e_mail: z.string().optional(),
  dien_giai: z.string().optional(),
  ma_httt: z.string().optional(),
  pt_tao_yn: z.boolean().optional(),

  // BasicInfoTab - Document Information
  ngay_ct: z.string().optional(), // date field
  ma_nt: z.string().optional(), // CurrencyField text input
  ma_ntType: z.string().optional(), // CurrencyField select input
  status: z.string().optional(), // select field

  // OtherTab Fields (all text fields)
  ly_do_huy: z.string().optional(),
  ly_do: z.string().optional(),
  ten_vt_thue: z.string().optional(),
  ghi_chu: z.string().optional(),

  // EInvoiceTab Fields (text and date fields)
  so_ct_hddt: z.string().optional(),
  ngay_ct_hddt: z.string().optional(), // date field
  so_ct2_hddt: z.string().optional(),
  ma_mau_ct_hddt: z.string().optional(),
  ma_tt: z.string().optional(),
  ma_tthddt: z.string().optional(),
  ma_pttt: z.string().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export interface DetailType {
  id: string;
  serviceCode: string;
  serviceName: string;
  revenueAccount: string;
  accountName: string;
  unit: string;
  quantity: number;
  price: number;
  amount: number;
  description: string;
  discountPercentage: number;
  discount: number;
  discountAccount: string;
  taxRate: string;
  taxCreditAccount: string;
  tax: number;
  sellingPrice: number;
  totalAmount: number;
  totalDiscount: number;
  totalTax: number;
  department: string;
  case: string;
  contract: string;
  paymentPhase: string;
  agreement: string;
  fee: string;
  product: string;
  productionOrder: string;
  invalidExpense: string;
}

export interface TaxType {
  id: string;
  invoiceNumber: string;
  symbol: string;
  invoiceDate: string;
  taxRate: string;
  invoiceTemplate: string;
  reportTemplate: string;
  natureCode: string;
  supplierCode: string;
  supplierName: string;
  address: string;
  taxCode: string;
  productServiceName: string;
  amountVND: number;
  taxAccount: string;
  correspondingAccount: string;
  taxVND: number;
  taxDepartment: string;
  paymentCode: string;
  note: string;
  department: string;
  case: string;
  contract: string;
  paymentPhase: string;
  agreement: string;
  fee: string;
  product: string;
  productionOrder: string;
  invalidExpense: string;
}
