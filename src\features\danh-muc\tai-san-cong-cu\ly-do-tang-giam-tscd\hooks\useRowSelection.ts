import { GridEventListener } from '@mui/x-data-grid';
import { useState } from 'react';

interface UseRowSelectionReturn {
  selectedObj: any | null;
  selectedRowIndex: number | null;
  handleRowClick: GridEventListener<'rowClick'>;
  clearSelection: () => void;
  setSelectedObj: (obj: any | null) => void;
  setSelectedRowIndex: (index: number | null) => void;
}

export const useRowSelection = (): UseRowSelectionReturn => {
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);

  const handleRowClick: GridEventListener<'rowClick'> = params => {
    setSelectedObj(params.row);
    setSelectedRowIndex(params.id as number);
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection,
    setSelectedObj,
    setSelectedRowIndex
  };
};
