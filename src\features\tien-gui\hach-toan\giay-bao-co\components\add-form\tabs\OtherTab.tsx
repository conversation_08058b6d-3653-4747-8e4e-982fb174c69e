import { ChangeEvent, useState } from 'react';
import { UploadCloud } from 'lucide-react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';
import { FormMode } from '@/types/form';

interface Props {
  value: any[];
  onChange?: (newValue: any[]) => void;
  onFileChange?: (file: File | null) => void;
  formMode: FormMode;
}

export const OtherTab = ({ value, onChange, onFileChange, formMode }: Props) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);
  };

  return (
    <div className='min-w-[800px] py-4'>
      <FormField
        className='w-[250px] items-center'
        label='Mã đối tượng'
        type='text'
        name='bookNumber'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Kèm theo'
        className='w-[250px] items-center'
        type='number'
        name='dhmCode'
        disabled={formMode === 'view'}
      />
      <div className='flex items-center gap-2'>
        <Label className='w-32 min-w-32 text-left'>Chứng từ gốc</Label>
        <div className='flex-1'>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
            dialogTitle='Danh mục chứng từ'
            columnDisplay='chung_tu_goc'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
      <div className='flex items-center gap-2'>
        <Label className='w-32 min-w-32 text-left'>Tên ngân hàng</Label>
        <div className='flex-1'>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.NGAN_HANG}`}
            dialogTitle='Danh mục ngân hàng'
            columnDisplay='ten_ngan_hang'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
      <div className='flex items-center gap-4'>
        <Label htmlFor='file-upload' className='min-w-[100px] text-sm font-medium'>
          Chọn file
        </Label>
        <div className='flex items-center gap-2'>
          <Label
            htmlFor='file-upload'
            className='flex cursor-pointer items-center gap-2 rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90'
          >
            <UploadCloud className='h-4 w-4' />
            <span>Chọn file</span>
          </Label>
          <Input
            id='file-upload'
            type='file'
            className='hidden'
            onChange={handleFileChange}
            disabled={formMode === 'view'}
          />
          {selectedFile && <span className='text-sm text-gray-600'>{selectedFile.name}</span>}
        </div>
      </div>
    </div>
  );
};
