import { Button } from '@mui/material';
import { useState } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { Group, GroupInput, GroupType } from '@/types/schemas';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { customerGroupSchema } from '../schemas';
import { BasicInfoTab } from './BasicInfoTab';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: GroupInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: Group;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  activeGroup?: GroupType;
  groups?: Group[];
  isCopyMode?: boolean;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick,
  activeGroup,
  groups = [],
  isCopyMode = false
}: FormDialogProps) => {
  const [showDuplicateCodeDialog, setShowDuplicateCodeDialog] = useState(false);

  const handleSubmit = async (data: any) => {
    try {
      const existingGroups = Array.isArray(groups) ? groups : [];

      if (formMode === 'add') {
        const isDuplicate = existingGroups.some((group: Group) => group.ma_nhom === data.ma_nhom);

        if (isDuplicate) {
          setShowDuplicateCodeDialog(true);
          return;
        }
      } else if (formMode === 'edit' && initialData) {
        const isDuplicate = existingGroups.some(
          (group: Group) => group.ma_nhom === data.ma_nhom && group.uuid !== initialData.uuid
        );

        if (isDuplicate) {
          setShowDuplicateCodeDialog(true);
          return;
        }
      }

      onSubmit(data);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={`${formMode === 'add' ? 'Thêm mới' : formMode === 'edit' ? 'Sửa' : 'Danh mục phân nhóm'}`}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={customerGroupSchema}
          onSubmit={handleSubmit}
          initialData={
            formMode === 'add' && initialData && isCopyMode
              ? {
                  ...initialData,
                  uuid: undefined,
                  ten_phan_nhom: initialData.ten_phan_nhom,
                  ten2: initialData.ten2 || '',
                  trang_thai: initialData.trang_thai || '1'
                }
              : formMode === 'add' && !isCopyMode
                ? undefined
                : initialData
          }
          className='w-[800px]'
          headerFields={<BasicInfoTab formMode={formMode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <BottomBar
              mode={formMode}
              onAdd={onAddButtonClick}
              onEdit={onEditButtonClick}
              onDelete={onDeleteButtonClick}
              onCopy={onCopyButtonClick}
              onSubmit={() => {}}
              onClose={onClose}
            />
          }
        />
      </AritoDialog>

      <AritoDialog
        open={showDuplicateCodeDialog}
        onClose={() => setShowDuplicateCodeDialog(false)}
        title='Cảnh báo'
        maxWidth='sm'
        disableBackdropClose={false}
        disableEscapeKeyDown={false}
        titleIcon={<AritoIcon icon={260} />}
      >
        <div className='p-4'>
          <p className='mb-4 text-center'>Mã nhóm đã tồn tại trong danh mục nhóm</p>
          <div className='flex justify-end'>
            <Button
              className='!bg-main text-white hover:!bg-main'
              onClick={() => setShowDuplicateCodeDialog(false)}
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
          </div>
        </div>
      </AritoDialog>
    </>
  );
};

export default FormDialog;
