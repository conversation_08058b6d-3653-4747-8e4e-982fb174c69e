import { z } from 'zod';
export interface FeeFormattedData {
  ma_phi: string;
  ten_phi: string;
  ten_khac?: string | null;
  nhom_phi_1?: string | null;
  nhom_phi_2?: string | null;
  nhom_phi_3?: string | null;
  bo_phan: string | null;
  trang_thai: string;
}

export const searchSchema = z.object({
  ma_phi: z.string().min(1, 'Mã phí là bắt buộc'),
  ten_phi: z.string().min(1, 'Tên phí là bắt buộc'),
  ten_khac: z.string().optional().nullable(),
  nhom_phi_1: z.string().optional().nullable(),
  nhom_phi_2: z.string().optional().nullable(),
  nhom_phi_3: z.string().optional().nullable(),
  bo_phan: z.string().optional().nullable(),
  trang_thai: z.number().default(1)
});
export type SearchFormValues = z.infer<typeof searchSchema>;
