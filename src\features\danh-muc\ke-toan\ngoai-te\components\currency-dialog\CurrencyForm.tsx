import React, { useState, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import BasicInfoTabType2 from '@/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { Type2Tabs } from '@/components/cac-loai-form/popup-form-type-2/Tabs';
import { FormField } from '@/components/custom/arito/form/form-field';
import { TaiKhoanSearchColumns } from '../../cols-definition';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface CurrencyFormProps {
  mode: 'add' | 'edit' | 'view';
}

const CurrencyForm: React.FC<CurrencyFormProps> = ({ mode }) => {
  const isDisabled = mode === 'view';
  const labelClass = 'w-32 text-right';
  const isViewMode = mode === 'view';
  const [tkPsclNo, setTkPsclNo] = useState<any>(null);
  const [tkPsclCo, setTkPsclCo] = useState<any>(null);

  const { setValue, getValues } = useFormContext();

  const handleTkPsclNoSelection = (row: any) => {
    setTkPsclNo(row);
    setValue('tk_pscl_no', row.uuid);
  };

  const handleTkPsclCoSelection = (row: any) => {
    setTkPsclCo(row);
    setValue('tk_pscl_co', row.uuid);
  };

  useEffect(() => {
    const formValues = getValues();

    if (formValues.tk_pscl_no_data) {
      setTkPsclNo(formValues.tk_pscl_no_data);
      setValue('tk_pscl_no', formValues.tk_pscl_no_data.uuid);
    } else if (formValues.tk_pscl_no && typeof formValues.tk_pscl_no === 'object') {
      setTkPsclNo(formValues.tk_pscl_no);
      setValue('tk_pscl_no', formValues.tk_pscl_no.uuid);
    }

    if (formValues.tk_pscl_co_data) {
      setTkPsclCo(formValues.tk_pscl_co_data);
      setValue('tk_pscl_co', formValues.tk_pscl_co_data.uuid);
    } else if (formValues.tk_pscl_co && typeof formValues.tk_pscl_co === 'object') {
      setTkPsclCo(formValues.tk_pscl_co);
      setValue('tk_pscl_co', formValues.tk_pscl_co.uuid);
    }
  }, [getValues, setValue]);

  return (
    <div className='flex flex-col gap-1 p-4'>
      <FormField
        label='Ngoại tệ'
        name='ma_nt'
        type='text'
        disabled={isDisabled || mode === 'edit'}
        labelClassName={labelClass}
        className='w-1/2'
      />
      <FormField label='Tên ngoại tệ' name='ten_nt' type='text' disabled={isDisabled} labelClassName={labelClass} />
      <FormField label='Tên khác' name='ten_nt2' type='text' disabled={isDisabled} labelClassName={labelClass} />

      <div className='my-2 flex flex-col sm:flex-row sm:items-center'>
        <Label className='sm:mb-0 sm:w-32 sm:min-w-32'>Tk phát sinh cl nợ</Label>
        <SearchField<any>
          type='text'
          columnDisplay='code'
          className='ml-1 w-[11.25rem]'
          searchEndpoint='/accounts'
          searchColumns={TaiKhoanSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkPsclNo?.code || ''}
          onRowSelection={handleTkPsclNoSelection}
          disabled={isViewMode}
        />
        {tkPsclNo?.code && <span className='flex items-center text-xs text-gray-700'>{tkPsclNo?.name}</span>}
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='sm:mb-0 sm:w-32 sm:min-w-32'>Tk phát sinh cl có</Label>
        <SearchField<any>
          type='text'
          columnDisplay='code'
          className='ml-1 w-[11.25rem]'
          searchEndpoint='/accounts'
          searchColumns={TaiKhoanSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkPsclCo?.code || ''}
          onRowSelection={handleTkPsclCoSelection}
          disabled={isViewMode}
        />
        {tkPsclCo?.code && <span className='flex items-center text-xs text-gray-700'>{tkPsclCo?.name}</span>}
      </div>
      <FormField
        label='Stt'
        name='stt'
        type='number'
        disabled={isDisabled}
        labelClassName={labelClass}
        defaultValue={0}
        className='w-1/2'
      />
      <FormField
        label='Đọc lẻ'
        name='ra_ndec'
        type='number'
        disabled={isDisabled}
        labelClassName={labelClass}
        defaultValue={0}
        className='w-1/2'
      />
      <div className='flex w-4/5 items-center'>
        <div className='flex flex-1 gap-2'>
          <FormField label='Cách đọc' name='ra_1' type='text' disabled={isDisabled} labelClassName={labelClass} />
          <FormField name='ra_2' type='text' disabled={isDisabled} />
          <FormField name='ra_3' type='text' disabled={isDisabled} />
          <FormField name='ra_4' type='text' disabled={isDisabled} />
          <FormField name='ra_5' type='text' disabled={isDisabled} />
        </div>
      </div>

      <div className='flex w-4/5 items-center'>
        <div className='flex flex-1 gap-2'>
          <FormField label='Cách đọc 2' name='ra_12' type='text' disabled={isDisabled} labelClassName={labelClass} />
          <FormField name='ra_22' type='text' disabled={isDisabled} />
          <FormField name='ra_32' type='text' disabled={isDisabled} />
          <FormField name='ra_42' type='text' disabled={isDisabled} />
          <FormField name='ra_52' type='text' disabled={isDisabled} />
        </div>
      </div>
      <FormField
        label='Trạng thái'
        name='status'
        type='select'
        disabled={isDisabled}
        labelClassName={labelClass}
        className='w-2/3'
        options={[
          { value: '1', label: '1. Còn sử dụng' },
          { value: '0', label: '0. Không sử dụng' }
        ]}
      />
    </div>
  );
};

export default CurrencyForm;
