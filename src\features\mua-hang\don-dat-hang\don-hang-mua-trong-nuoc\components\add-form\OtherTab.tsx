import { ChangeEvent, useState } from 'react';
import { UploadCloud } from 'lucide-react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface Props {
  formMode: 'add' | 'edit' | 'view';
  onFileChange?: (file: File | null) => void;
}

const OtherTab = ({ formMode, onFileChange }: Props) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);
  };

  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Giao dịch'
        name='giao_dich'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
        options={[
          { label: 'Mua hàng', value: 'Mua hàng' },
          { label: 'Bán hàng', value: 'Bán hàng' }
        ]}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Lý do hủy'
        name='ly_do_huy'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Ghi chú'
        name='ghi_chu'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <div className='flex items-center gap-4'>
        <Label htmlFor='file-upload' className='min-w-[100px] text-sm font-medium'>
          Chọn file
        </Label>
        <div className='flex items-center gap-2'>
          <Label
            htmlFor='file-upload'
            className='flex cursor-pointer items-center gap-2 rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90'
          >
            <UploadCloud className='h-4 w-4' />
            <span>Chọn file</span>
          </Label>
          <Input
            id='file-upload'
            type='file'
            className='hidden'
            onChange={handleFileChange}
            disabled={formMode === 'view'}
          />
          {selectedFile && <span className='text-sm text-gray-600'>{selectedFile.name}</span>}
        </div>
      </div>
    </div>
  );
};

export default OtherTab;
