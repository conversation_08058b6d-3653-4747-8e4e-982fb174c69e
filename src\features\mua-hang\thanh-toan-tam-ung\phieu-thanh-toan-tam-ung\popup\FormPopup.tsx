import React from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { SearchBasicInfoTab } from './SearchBasicInfoTab';
import AritoModal from '@/components/custom/arito/modal';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { SearchDetailTab } from './SearchDetailTab';

interface Props {
  showFromPopUpForm: boolean;
  setShowFromPopUpForm: (value: boolean) => void;
  formMode: 'add' | 'edit' | 'view';
}

const FromPopup = ({ showFromPopUpForm, setShowFromPopUpForm, formMode }: Props) => {
  const handleSubmit = (data: any) => {
    setShowFromPopUpForm(false);
  };
  return (
    <AritoModal
      open={showFromPopUpForm}
      onClose={() => {
        setShowFromPopUpForm(false);
      }}
      title={'Tì<PERSON> kiếm'}
      titleIcon={<AritoIcon icon={699} />}
      maxWidth='md'
    >
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
          <AritoForm<any>
            mode={formMode}
            hasAritoActionBar={false}
            className='!static !w-full'
            onSubmit={handleSubmit}
            onClose={() => {
              setShowFromPopUpForm(false);
            }}
            headerFields={<SearchBasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'chi_tiet',
                label: 'Chi tiết',
                component: <SearchDetailTab formMode={formMode} />
              }
            ]}
          />
        </div>

        <BottomBar
          mode={formMode}
          onSubmit={() => {}}
          onClose={() => {
            setShowFromPopUpForm(false);
          }}
        />
      </div>
    </AritoModal>
  );
};

export default FromPopup;
