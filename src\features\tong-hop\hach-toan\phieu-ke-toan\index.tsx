import { Save, LogOut, Plus, Pencil, Trash, <PERSON><PERSON>, Printer, RefreshCcw, Table } from 'lucide-react';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  accountingVoucherDetailColumns,
  accountingVoucherExpenseColumns,
  formDetailColumns,
  getAccountingVoucherColumns
} from './cols-definition';
import {
  AritoInputTable,
  AritoForm,
  AritoActionButton,
  AritoHeaderTabs,
  AritoDataTables,
  AritoColoredDot
} from '@/components/custom/arito';
import { expenseSchema, orderDetailSchema } from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/schemas';
import { BasicInfoTab, DetailsTab, HistoryTab, CommentTab, ActionBar, BottomBar, TaxsTab } from './components';
export default function AccountingVoucherPage({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [activeTab, setActiveTab] = useState<string>('info');

  //Initial
  const [rows, setOrderRows] = useState<any[]>(initialRows);
  //Add and Edit
  const [inputOrderDetail, setInputOrderDetail] = useState<any[]>([]);
  const [inputExpenseDetail, setInputExpenseDetail] = useState<any[]>([]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const getFormTitle = () => {
    switch (activeTab) {
      case 'history':
        return 'Lịch sử';
      case 'comment':
        return 'Bình luận';
      default:
        return 'Phiếu kế toán';
    }
  };

  const getActionButtons = () => {
    if (formMode !== 'view') {
      return (
        <>
          <AritoActionButton title='Lưu' icon={Save} variant='secondary' type='submit' />
          <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseForm} />
        </>
      );
    }

    switch (activeTab) {
      case 'history':
        return (
          <>
            <AritoActionButton
              title='Refresh'
              icon={RefreshCcw}
              variant='secondary'
              onClick={() => console.log('Refresh clicked')}
            />
            <AritoActionButton
              title='Cố định cột'
              icon={Table}
              variant='secondary'
              onClick={() => console.log('Cố định cột clicked')}
            />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseForm} />
          </>
        );
      case 'comment':
        return (
          <>
            <AritoActionButton
              title='Refresh'
              icon={RefreshCcw}
              variant='secondary'
              onClick={() => console.log('Refresh clicked')}
            />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseForm} />
          </>
        );
      default: // info tab
        return (
          <>
            <AritoActionButton
              title='In'
              icon={Printer}
              variant='secondary'
              onClick={() => console.log('In clicked')}
            />
            <AritoActionButton title='Thêm' icon={Plus} variant='primary' onClick={() => console.log('Thêm clicked')} />
            <AritoActionButton
              title='Sửa'
              icon={Pencil}
              variant='secondary'
              onClick={() => console.log('Sửa clicked')}
            />
            <AritoActionButton
              title='Xóa'
              icon={Trash}
              variant='destructive'
              onClick={() => console.log('Xóa clicked')}
            />
            <AritoActionButton
              title='Sao chép'
              icon={Copy}
              variant='secondary'
              onClick={() => console.log('Sao chép clicked')}
            />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseForm} />
          </>
        );
    }
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.orderDetails) {
      setInputOrderDetail([...obj.orderDetails]);
    } else {
      setInputOrderDetail([]);
    }
    if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.orderDetails) {
      setInputOrderDetail([...obj.orderDetails]);
    } else {
      setInputOrderDetail([]);
    }
    if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputOrderDetail([]);
    setInputExpenseDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      // Validate order detail
      for (const row of inputOrderDetail) {
        await orderDetailSchema.validate(row);
      }

      for (const row of inputExpenseDetail) {
        await expenseSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...rows.map(row => row.id || 0)) + 1;
        const newOrder = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          statusHDDT: 'Chưa xuất',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          orderDetails: inputOrderDetail
        };

        setOrderRows([...rows, newOrder]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = rows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              orderDetails: inputOrderDetail
            };
          }
          return row;
        });

        setOrderRows(updatedRows);
      }

      setShowForm(false);
      setInputOrderDetail([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const convertToDetails = (order: any) => {
    const details = order.orderItems.map((item: any) => ({
      ...item,
      productCode: item.productCode,
      productName: item.productName,
      unit: item.unit,
      warehouseCode: item.warehouseCode
    }));
    return details;
  };

  const handleRowClick = (params: GridRowParams) => {
    let order = params.row as any;
    if (order.orderItems) {
      const details = convertToDetails(order);
      order.orderDetails = details;
    }

    setSelectedObj(order);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Chưa ghi sổ',
      rows: rows.filter(row => row.status === 'Chưa ghi sổ'),
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: rows.filter(row => row.status === 'Chờ duyệt'),
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Đã ghi sổ',
      rows: rows.filter(row => row.statusHDDT === 'Đã duyệt'),
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#3B82F6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            hasAritoActionBar={true}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            headerFields={
              <AritoHeaderTabs
                defaultTabIndex={0}
                onTabChange={tabId => setActiveTab(tabId)}
                tabs={[
                  { id: 'info', label: 'Thông tin', component: <BasicInfoTab formMode={formMode} /> },
                  { id: 'history', label: 'Lịch sử', component: <HistoryTab formMode={formMode} /> },
                  { id: 'comment', label: 'Bình luận', component: <CommentTab formMode={formMode} /> }
                ]}
              />
            }
            tabs={
              activeTab === 'info'
                ? [
                    {
                      id: 'details',
                      label: 'Chi tiết',
                      component: <DetailsTab formMode={formMode} columns={formDetailColumns} />
                    },
                    {
                      id: 'tax',
                      label: 'Thuế',
                      component: <TaxsTab formMode={formMode} columns={accountingVoucherExpenseColumns} />
                    },
                    {
                      id: 'filekem',
                      label: 'File đính kèm',
                      component: <div className='p-4'>File đính kèm</div>
                    }
                  ]
                : []
            }
            onClose={handleCloseForm}
            title={getFormTitle()}
            subTitle={'Phiếu kế toán'}
            actionButtons={getActionButtons()}
            bottomBar={<BottomBar totalQuantity={0} totalPayment={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.orderDetails || []}
                columns={accountingVoucherDetailColumns}
                mode={formMode}
                tableActionButtons={['downloadExcel', 'pin']}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
