import {
  accountSearchColumns,
  khuVucSearchColumns,
  nhanVienSearchColumns,
  nhomNhaCungCapSearchColumns
} from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { useGeneralTabState } from './hooks/useGeneralTabState';
import { AccountModel } from '@/types/schemas/account.type';
import { NhanVien } from '@/types/schemas/nhan-vien.type';
import { KhuVuc } from '@/types/schemas/khu-vuc.type';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Group } from '@/types/schemas/group.type';
import { Label } from '@/components/ui/label';

interface Props {
  formMode: 'add' | 'edit' | 'view';
  selectedObj?: any; // Data from selectedObj when copying
}

export const GeneralTab = ({ formMode, selectedObj }: Props) => {
  const isViewMode = formMode === 'view';

  // Custom hooks
  const {
    selectedEmployee,
    selectedDefaultAccount,
    selectedPayableAccount,
    selectedPaymentMethod,
    selectedGroup1,
    selectedGroup2,
    selectedGroup3,
    selectedArea,
    handleEmployeeSelection,
    handleDefaultAccountSelection,
    handlePayableAccountSelection,
    handlePaymentMethodSelection,
    handleGroup1Selection,
    handleGroup2Selection,
    handleGroup3Selection,
    handleAreaSelection
  } = useGeneralTabState({ formMode, selectedObj });

  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Nhân viên bán hàng</Label>
        <div className='w-[57.5%]'>
          <SearchField<NhanVien>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}`}
            searchColumns={nhanVienSearchColumns}
            dialogTitle='Danh mục nhân viên'
            columnDisplay='ma_nhan_vien'
            displayRelatedField='ho_ten_nhan_vien'
            value={selectedEmployee?.ma_nhan_vien || ''}
            relatedFieldValue={selectedEmployee?.ho_ten_nhan_vien || ''}
            onRowSelection={handleEmployeeSelection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Tài khoản ngầm định</Label>
        <div className='w-[57.5%]'>
          <SearchField<AccountModel>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={selectedDefaultAccount?.code || ''}
            relatedFieldValue={selectedDefaultAccount?.name || ''}
            onRowSelection={handleDefaultAccountSelection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Mã th.toán công nợ</Label>
        <div className='w-[57.5%]'>
          <SearchField<AccountModel>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={selectedPayableAccount?.code || ''}
            relatedFieldValue={selectedPayableAccount?.name || ''}
            onRowSelection={handlePayableAccountSelection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Ph/th th.toán (HĐĐT)</Label>
        <div className='w-[57.5%]'>
          <SearchField<AccountModel>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={selectedPaymentMethod?.code || ''}
            relatedFieldValue={selectedPaymentMethod?.name || ''}
            onRowSelection={handlePaymentMethodSelection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <FormField
        className='w-[27%] items-start gap-y-1 sm:items-center'
        label='Giới hạn tín dụng'
        name='credit_limit'
        type='number'
        labelClassName='min-w-[180px]'
        disabled={isViewMode}
      />

      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Nhóm 1</Label>
        <div className='w-[57.5%]'>
          <SearchField<Group>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.NHOM}`}
            searchColumns={nhomNhaCungCapSearchColumns}
            dialogTitle='Danh mục nhóm khách hàng 1'
            columnDisplay='ma_nhom'
            displayRelatedField='ten_phan_nhom'
            value={selectedGroup1?.ma_nhom || ''}
            relatedFieldValue={selectedGroup1?.ten_phan_nhom || ''}
            onRowSelection={handleGroup1Selection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Nhóm 2</Label>
        <div className='w-[57.5%]'>
          <SearchField<Group>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.NHOM}`}
            searchColumns={nhomNhaCungCapSearchColumns}
            dialogTitle='Danh mục nhóm khách hàng 2'
            columnDisplay='ma_nhom'
            displayRelatedField='ten_phan_nhom'
            value={selectedGroup2?.ma_nhom || ''}
            relatedFieldValue={selectedGroup2?.ten_phan_nhom || ''}
            onRowSelection={handleGroup2Selection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Nhóm 3</Label>
        <div className='w-[57.5%]'>
          <SearchField<Group>
            type='text'
            searchEndpoint={`/${QUERY_KEYS.NHOM}`}
            searchColumns={nhomNhaCungCapSearchColumns}
            dialogTitle='Danh mục nhóm khách hàng 3'
            columnDisplay='ma_nhom'
            displayRelatedField='ten_phan_nhom'
            value={selectedGroup3?.ma_nhom || ''}
            relatedFieldValue={selectedGroup3?.ten_phan_nhom || ''}
            onRowSelection={handleGroup3Selection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40 text-left'>Khu vực</Label>
        <div className='w-[57.5%]'>
          <SearchField<KhuVuc>
            type='text'
            name='region'
            searchEndpoint={`/${QUERY_KEYS.KHU_VUC}`}
            searchColumns={khuVucSearchColumns}
            dialogTitle='Danh mục khu vực'
            columnDisplay='rg_code'
            displayRelatedField='rgname'
            value={selectedArea?.rg_code || ''}
            relatedFieldValue={selectedArea?.rgname || ''}
            onRowSelection={handleAreaSelection}
            disabled={isViewMode}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
      </div>

      <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Điện thoại'
          name='phone'
          type='text'
          labelClassName='min-w-[100px]'
          disabled={isViewMode}
        />
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Fax'
          name='fax'
          type='text'
          labelClassName='min-w-[100px]'
          disabled={isViewMode}
        />
      </div>

      <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Email'
          name='email'
          type='text'
          labelClassName='min-w-[100px]'
          disabled={isViewMode}
        />
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Trang chủ(Website)'
          name='website'
          type='text'
          labelClassName='min-w-[100px]'
          disabled={isViewMode}
        />
      </div>

      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Ghi chú'
        name='notes'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={isViewMode}
      />

      <FormField
        className='w-[400px] items-start gap-y-1 sm:items-center'
        label='Trạng thái'
        name='status'
        type='select'
        labelClassName='min-w-[100px]'
        disabled={isViewMode}
        options={[
          { value: 'Active', label: '1. Còn sử dụng' },
          { value: 'Inactive', label: '0. Không sử dụng' }
        ]}
      />
    </div>
  );
};
